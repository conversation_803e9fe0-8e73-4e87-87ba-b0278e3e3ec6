{"folders": [{"name": "Cool Admin <PERSON>", "path": "./cool-admin-java"}, {"name": "<PERSON>", "path": "./cool-admin-vue"}], "settings": {"files.encoding": "utf8", "[java]": {"files.encoding": "utf8"}, "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "args": ["-NoExit", "-Command", "chcp 65001"]}, "Command Prompt": {"path": "cmd.exe", "args": ["/k", "chcp 65001"]}}, "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.fontFamily": "'Sarasa Mono SC', <PERSON><PERSON><PERSON>, '微软雅黑'", "java.debug.settings.onBuildFailureProceed": true, "java.configuration.workspaceCacheLimit": 90, "java.server.launchMode": "Standard"}, "launch": {"version": "0.2.0", "configurations": [{"type": "java", "name": "Debug Cool Admin Java (UTF-8)", "request": "launch", "mainClass": "com.cool.CoolApplication", "projectName": "cool-admin", "vmArgs": ["-Dfile.encoding=UTF-8", "-Dsun.jnu.encoding=UTF-8", "-Dspring.output.ansi.enabled=ALWAYS", "-Dspring.profiles.active=local", "-Dserver.port=8001", "-Dconsole.encoding=UTF-8", "-Dlogging.charset.console=UTF-8", "-Dlogging.charset.file=UTF-8", "-Djan<PERSON>.force=true", "-<PERSON><PERSON><PERSON>.passthrough=true"], "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8", "LANG": "zh_CN.UTF-8", "LC_ALL": "zh_CN.UTF-8", "CHCP": "65001"}, "console": "integratedTerminal", "encoding": "UTF-8"}]}}
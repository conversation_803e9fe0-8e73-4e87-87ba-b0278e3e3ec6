# 业主公示需求文档（新版）

## 1. 业务背景与目标

为落实物业管理信息公开、保障业主知情权，提升物业服务透明度，系统需建设“业主公示”模块，集中展示物业服务相关核心信息，支持PC与移动端一体化适配，页面美观、权威、易查阅。

## 2. 主要功能需求

### 2.1 公示数据结构与页面合并
- 公示表（announcement）已包含html字段，所有公示内容通过html富文本动态渲染。
- 原“物业服务区域概况”“业主共有部分经营收益收支”“物业服务资金收支”“物业小区问题马上解决报告”四个页面合并为一个“公示详情”页面，内容由html字段动态生成。

### 2.2 公示列表与筛选
- 新增“公示列表”页面：
  - 支持按“月份（month）”和“项目（project）”筛选公示数据。
  - 列表展示公示标题、项目、月份、状态（已发布/已撤销）、操作等。
  - 支持公示的发布、撤销、H5预览等管理操作。

### 2.3 公示详情与H5预览
- 点击公示列表中的某条数据，进入“公示详情”页面，直接渲染展示html字段内容。
- 支持一键H5预览，H5页面与主站风格一致，适配移动端浏览器，便于扫码、分享、微信/支付宝等内嵌浏览器访问。

### 2.4 权限与交互
- 仅有权限的用户可管理（发布/撤销）公示。
- 普通业主仅可查看本项目的公示数据。
- 公示数据接口需鉴权，防止未授权访问。

### 2.5 其他说明
- 公示内容结构、样式、说明等均通过html富文本灵活配置，支持后续动态扩展。
- 支持导出PDF、打印等功能，导出内容与html页面一致。

## 3. 主要界面与交互说明
- “公示列表”页面：筛选、管理、预览、发布/撤销等操作入口。
- “公示详情”页面：渲染html内容，支持H5预览、导出、打印。
- H5页面：移动端适配，扫码访问，内容与详情页一致。

## 4. 数据结构与接口
- 公示表（announcement）核心字段：id, project_id, title, month, html, status, created_time, updated_time 等。
- 支持按project_id和month查询公示列表，支持发布/撤销、H5预览、详情查询等接口。

## 5. 安全与权限
- 公示数据接口需鉴权，防止未授权访问和数据泄露。
- H5接口为公开接口，仅返回已发布的公示内容。

## 6. 后续扩展建议
- 支持多项目/多小区切换，接口增加projectId参数。
- 支持历史数据归档与查询，接口支持时间区间筛选。
- 支持多语言、主题色切换、LOGO自定义等。
- 预留与第三方平台（如政务、社区App）对接能力。 
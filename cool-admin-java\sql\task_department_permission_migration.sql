-- 任务系统部门权限迁移脚本
-- 作者: yayaai 开发团队
-- 版本: v1.0
-- 创建时间: 2025-07-02

-- ============================================================================
-- 1. 为现有任务表添加部门字段
-- ============================================================================

-- 1.1 任务包表添加部门字段
ALTER TABLE task_package 
ADD COLUMN department_id BIGINT COMMENT '所属部门ID' AFTER owner_name,
ADD COLUMN creator_department_id BIGINT COMMENT '创建者部门ID' AFTER department_id;

-- 为任务包表添加索引
ALTER TABLE task_package 
ADD INDEX idx_department_id (department_id),
ADD INDEX idx_creator_department_id (creator_department_id),
ADD INDEX idx_dept_status (department_id, package_status),
ADD INDEX idx_creator_time (creator_department_id, create_time);

-- 1.2 任务信息表添加部门字段
ALTER TABLE task_info 
ADD COLUMN department_id BIGINT COMMENT '所属部门ID' AFTER step_name,
ADD COLUMN creator_department_id BIGINT COMMENT '创建者部门ID' AFTER department_id;

-- 为任务信息表添加索引
ALTER TABLE task_info 
ADD INDEX idx_department_id (department_id),
ADD INDEX idx_creator_department_id (creator_department_id),
ADD INDEX idx_dept_status (department_id, task_status),
ADD INDEX idx_package_dept (package_id, department_id);

-- 1.3 任务执行表添加部门字段
ALTER TABLE task_execution 
ADD COLUMN department_id BIGINT COMMENT '执行部门ID' AFTER assignee_name,
ADD COLUMN assignee_department_id BIGINT COMMENT '执行人部门ID' AFTER department_id;

-- 为任务执行表添加索引
ALTER TABLE task_execution 
ADD INDEX idx_department_id (department_id),
ADD INDEX idx_assignee_department_id (assignee_department_id),
ADD INDEX idx_dept_assignee (department_id, assignee_id),
ADD INDEX idx_status_dept (execution_status, assignee_department_id);

-- ============================================================================
-- 2. 创建任务权限日志表
-- ============================================================================

CREATE TABLE IF NOT EXISTS task_permission_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '操作用户ID',
    username VARCHAR(255) NOT NULL COMMENT '操作用户名',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型(VIEW/EDIT/DELETE/ASSIGN等)',
    task_type VARCHAR(50) NOT NULL COMMENT '任务类型(package/info/execution)',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    department_id BIGINT COMMENT '涉及部门ID',
    permission_result TINYINT NOT NULL COMMENT '权限验证结果(0:失败,1:成功)',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    user_agent TEXT COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_operation_time (operation_time),
    INDEX idx_task_type_id (task_type, task_id),
    INDEX idx_department_id (department_id),
    INDEX idx_permission_result (permission_result),
    INDEX idx_user_operation (user_id, operation_type, operation_time)
) COMMENT '任务权限操作日志表';

-- ============================================================================
-- 3. 数据迁移 - 为现有任务数据设置部门信息
-- ============================================================================

-- 3.1 为现有任务包设置部门信息
UPDATE task_package tp
SET department_id = (
    SELECT u.department_id 
    FROM base_sys_user u 
    WHERE u.id = tp.creator_id
    LIMIT 1
),
creator_department_id = (
    SELECT u.department_id 
    FROM base_sys_user u 
    WHERE u.id = tp.creator_id
    LIMIT 1
)
WHERE tp.department_id IS NULL 
AND tp.creator_id IS NOT NULL;

-- 3.2 为现有任务信息设置部门信息（继承任务包的部门）
UPDATE task_info ti
SET department_id = (
    SELECT tp.department_id 
    FROM task_package tp 
    WHERE tp.id = ti.package_id
    LIMIT 1
),
creator_department_id = (
    SELECT tp.creator_department_id 
    FROM task_package tp 
    WHERE tp.id = ti.package_id
    LIMIT 1
)
WHERE ti.department_id IS NULL 
AND ti.package_id IS NOT NULL;

-- 3.3 为现有任务执行设置部门信息
UPDATE task_execution te
SET department_id = (
    SELECT ti.department_id 
    FROM task_info ti 
    WHERE ti.id = te.task_id
    LIMIT 1
),
assignee_department_id = (
    SELECT u.department_id 
    FROM base_sys_user u 
    WHERE u.id = te.assignee_id
    LIMIT 1
)
WHERE te.department_id IS NULL;

-- ============================================================================
-- 4. 处理孤儿数据 - 为没有关联部门的数据设置默认部门
-- ============================================================================

-- 获取默认部门ID（通常是根部门）
SET @default_department_id = (
    SELECT id FROM base_sys_department 
    WHERE parent_id IS NULL 
    ORDER BY id 
    LIMIT 1
);

-- 为无法确定部门的任务包设置默认部门
UPDATE task_package 
SET 
    department_id = @default_department_id, 
    creator_department_id = @default_department_id
WHERE department_id IS NULL;

-- 为无法确定部门的任务信息设置默认部门
UPDATE task_info 
SET 
    department_id = @default_department_id, 
    creator_department_id = @default_department_id
WHERE department_id IS NULL;

-- 为无法确定部门的任务执行设置默认部门
UPDATE task_execution 
SET 
    department_id = @default_department_id, 
    assignee_department_id = @default_department_id
WHERE department_id IS NULL;

-- ============================================================================
-- 5. 创建数据一致性检查视图
-- ============================================================================

CREATE OR REPLACE VIEW v_task_department_consistency AS
SELECT 
    'task_package' as table_name,
    COUNT(*) as total_records,
    COUNT(department_id) as with_department,
    COUNT(*) - COUNT(department_id) as without_department
FROM task_package
UNION ALL
SELECT 
    'task_info' as table_name,
    COUNT(*) as total_records,
    COUNT(department_id) as with_department,
    COUNT(*) - COUNT(department_id) as without_department
FROM task_info
UNION ALL
SELECT 
    'task_execution' as table_name,
    COUNT(*) as total_records,
    COUNT(department_id) as with_department,
    COUNT(*) - COUNT(department_id) as without_department
FROM task_execution;

-- ============================================================================
-- 6. 创建权限检查存储过程
-- ============================================================================

DELIMITER $$

CREATE PROCEDURE CheckTaskDepartmentPermission(
    IN p_user_id BIGINT,
    IN p_task_type VARCHAR(50),
    IN p_task_id BIGINT,
    OUT p_has_permission BOOLEAN
)
BEGIN
    DECLARE v_department_id BIGINT;
    DECLARE v_user_department_count INT DEFAULT 0;
    
    -- 根据任务类型获取部门ID
    CASE p_task_type
        WHEN 'package' THEN
            SELECT department_id INTO v_department_id 
            FROM task_package WHERE id = p_task_id;
        WHEN 'info' THEN
            SELECT department_id INTO v_department_id 
            FROM task_info WHERE id = p_task_id;
        WHEN 'execution' THEN
            SELECT department_id INTO v_department_id 
            FROM task_execution WHERE id = p_task_id;
        ELSE
            SET v_department_id = NULL;
    END CASE;
    
    -- 检查用户是否有该部门权限
    IF v_department_id IS NOT NULL THEN
        SELECT COUNT(*) INTO v_user_department_count
        FROM base_sys_user_role ur
        JOIN base_sys_role_department rd ON ur.role_id = rd.role_id
        WHERE ur.user_id = p_user_id AND rd.department_id = v_department_id;
        
        SET p_has_permission = (v_user_department_count > 0);
    ELSE
        SET p_has_permission = FALSE;
    END IF;
END$$

DELIMITER ;

-- ============================================================================
-- 执行完成提示
-- ============================================================================

-- 查看迁移结果
SELECT '=== 任务系统部门权限迁移完成 ===' as status;
SELECT * FROM v_task_department_consistency;

-- 显示统计信息
SELECT 
    '任务包总数' as metric,
    COUNT(*) as count,
    COUNT(department_id) as with_department_count,
    ROUND(COUNT(department_id) * 100.0 / COUNT(*), 2) as percentage
FROM task_package
UNION ALL
SELECT 
    '任务信息总数' as metric,
    COUNT(*) as count,
    COUNT(department_id) as with_department_count,
    ROUND(COUNT(department_id) * 100.0 / COUNT(*), 2) as percentage
FROM task_info
UNION ALL
SELECT 
    '任务执行总数' as metric,
    COUNT(*) as count,
    COUNT(department_id) as with_department_count,
    ROUND(COUNT(department_id) * 100.0 / COUNT(*), 2) as percentage
FROM task_execution;

SELECT '迁移脚本执行完成，请检查上述统计信息确认迁移效果' as notice; 
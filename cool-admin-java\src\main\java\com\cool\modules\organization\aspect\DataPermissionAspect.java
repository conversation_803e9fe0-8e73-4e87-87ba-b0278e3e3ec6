package com.cool.modules.organization.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.annotation.DataPermissionFilter;
import com.cool.modules.organization.service.DualDimensionDataPermissionService;
import com.mybatisflex.core.query.QueryWrapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据权限AOP切面
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class DataPermissionAspect {
    
    private final DualDimensionDataPermissionService permissionService;
    
    @Around("@annotation(dataPermissionFilter)")
    public Object applyDataPermissionFilter(ProceedingJoinPoint joinPoint, 
                                           DataPermissionFilter dataPermissionFilter) throws Throwable {
        
        // 检查是否启用权限过滤
        if (!dataPermissionFilter.enable()) {
            return joinPoint.proceed();
        }
        
        try {
            // 获取当前用户ID
            Long userId = CoolSecurityUtil.getCurrentUserId();
            
            // 检查是否忽略管理员权限
            if (!dataPermissionFilter.ignoreAdmin() && permissionService.isSystemAdmin(userId)) {
                log.debug("系统管理员跳过权限过滤: {}", dataPermissionFilter.entityType());
                return joinPoint.proceed();
            }
            
            // 查找QueryWrapper参数并应用权限过滤
            Object[] args = joinPoint.getArgs();
            boolean filterApplied = false;
            
            for (Object arg : args) {
                if (arg instanceof QueryWrapper) {
                    QueryWrapper queryWrapper = (QueryWrapper) arg;
                    permissionService.applyDataPermissionFilter(queryWrapper, userId, dataPermissionFilter.entityType());
                    filterApplied = true;
                    log.debug("已应用数据权限过滤: entityType={}, userId={}", dataPermissionFilter.entityType(), userId);
                    break;
                }
            }
            
            if (!filterApplied) {
                log.warn("未找到QueryWrapper参数，无法应用权限过滤: {}", dataPermissionFilter.entityType());
            }
            
            return joinPoint.proceed();
            
        } catch (Exception e) {
            log.error("应用数据权限过滤失败: entityType={}", dataPermissionFilter.entityType(), e);
            // 权限过滤失败时，为了安全起见，可以选择抛出异常或返回空结果
            throw e;
        }
    }
}

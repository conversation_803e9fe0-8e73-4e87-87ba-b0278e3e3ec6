package com.cool.modules.base.service.sys.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.base.entity.sys.BaseSysApiKeyEntity;
import com.cool.modules.base.mapper.sys.BaseSysApiKeyMapper;
import com.cool.modules.base.service.sys.BaseSysApiKeyService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * APIKEY授权表Service实现
 */
@Service
@RequiredArgsConstructor
public class BaseSysApiKeyServiceImpl extends BaseServiceImpl<BaseSysApiKeyMapper,BaseSysApiKeyEntity> implements BaseSysApiKeyService {
    private final BaseSysApiKeyMapper apiKeyMapper;

    /**
     * 创建APIKEY
     */
    @Override
    @Transactional
    public BaseSysApiKeyEntity createApiKey(Long userId, String remark, Integer expireDays) {
        String rawKey = "CAK_" + IdUtil.simpleUUID() + userId + System.nanoTime();
        String apikey = DigestUtil.sha256Hex(rawKey);
        BaseSysApiKeyEntity entity = new BaseSysApiKeyEntity();
        entity.setUserId(userId);
        entity.setApikey(apikey);
        entity.setStatus(1);
        entity.setRemark(remark);
        entity.setCreateTime(new Date());
        if (expireDays != null && expireDays > 0) {
            entity.setExpireTime(new Date(System.currentTimeMillis() + expireDays * 24L * 3600 * 1000));
        }
        apiKeyMapper.insert(entity);
        entity.setApikey(rawKey); // 只返回一次明文（带CAK_前缀）
        return entity;
    }

    /**
     * 重置APIKEY
     */
    @Override
    @Transactional
    public BaseSysApiKeyEntity resetApiKey(Long id) {
        BaseSysApiKeyEntity entity = apiKeyMapper.selectOneById(id);
        if (entity == null) return null;
        String rawKey = "CAK_" + IdUtil.simpleUUID() + entity.getUserId() + System.nanoTime();
        String apikey = DigestUtil.sha256Hex(rawKey);
        entity.setApikey(apikey);
        apiKeyMapper.update(entity);
        entity.setApikey(rawKey); // 只返回一次明文（带CAK_前缀）
        return entity;
    }

    /**
     * 禁用APIKEY
     */
    @Override
    @Transactional
    public boolean disableApiKey(Long id) {
        BaseSysApiKeyEntity entity = apiKeyMapper.selectOneById(id);
        if (entity == null) return false;
        entity.setStatus(0);
        return apiKeyMapper.update(entity) > 0;
    }

    /**
     * 启用APIKEY
     */
    @Override
    @Transactional
    public boolean enableApiKey(Long id) {
        BaseSysApiKeyEntity entity = apiKeyMapper.selectOneById(id);
        if (entity == null) return false;
        entity.setStatus(1);
        return apiKeyMapper.update(entity) > 0;
    }

    /**
     * 删除APIKEY
     */
    @Override
    @Transactional
    public boolean deleteApiKey(Long id) {
        return apiKeyMapper.deleteById(id) > 0;
    }

    /**
     * 查询用户的APIKEY列表
     */
    @Override
    public List<BaseSysApiKeyEntity> listByUserId(Long userId) {
        return apiKeyMapper.selectListByQuery(QueryWrapper.create().eq("user_id", userId));
    }

    /**
     * 根据apikey查找
     */
    @Override
    public BaseSysApiKeyEntity findByApiKey(String apikey) {
        if (apikey != null && apikey.startsWith("CAK_")) {
            // apikey = DigestUtil.sha256Hex(apikey.trim());
            apikey = apikey.replace("CAK_", "");
        } else {
            return null;
        }
        return apiKeyMapper.selectOneByQuery(QueryWrapper.create().eq("apikey", apikey));
    }
} 
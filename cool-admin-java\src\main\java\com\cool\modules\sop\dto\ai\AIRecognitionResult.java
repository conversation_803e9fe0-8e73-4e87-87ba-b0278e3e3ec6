package com.cool.modules.sop.dto.ai;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * AI统一识别结果DTO
 * 包含场景识别、优先级、时间等所有信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIRecognitionResult {

    /**
     * 识别是否成功
     */
    private boolean success;

    /**
     * 识别置信度 (0-1)
     */
    private Double confidence;

    /**
     * 错误信息（如果识别失败）
     */
    private String errorMessage;

    /**
     * 场景识别结果
     */
    private ScenarioRecognition scenario;

    /**
     * 优先级识别结果 (1-5)
     */
    private Integer priority;

    /**
     * 时间识别结果
     */
    private TimeRecognition timeInfo;

    /**
     * AI识别的原始响应
     */
    private String rawResponse;

    /**
     * AI返回的场景编码（兼容LLM直接输出的scenarioCode）
     */
    private String scenarioCode;

    /**
     * 场景识别结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScenarioRecognition {
        /**
         * 场景ID
         */
        private Long scenarioId;

        /**
         * 场景编码
         */
        private String scenarioCode;

        /**
         * 场景名称
         */
        private String scenarioName;

        /**
         * 匹配置信度 (0-1)
         */
        private Double matchConfidence;

        /**
         * 匹配原因
         */
        private String matchReason;
    }

    /**
     * 时间识别结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRecognition {
        /**
         * 开始时间 (YYYY-MM-DD HH:mm:ss)
         */
        private String startTime;

        /**
         * 结束时间 (YYYY-MM-DD HH:mm:ss)
         */
        private String endTime;

        /**
         * 时间识别置信度 (0-1)
         */
        private Double timeConfidence;

        /**
         * 时间识别原因
         */
        private String timeReason;

        /**
         * 是否使用了默认时间
         */
        private Boolean useDefaultTime;
    }

    /**
     * 创建成功的识别结果
     */
    public static AIRecognitionResult success(ScenarioRecognition scenario, Integer priority, TimeRecognition timeInfo, Double confidence) {
        return AIRecognitionResult.builder()
                .success(true)
                .confidence(confidence)
                .scenario(scenario)
                .priority(priority)
                .timeInfo(timeInfo)
                .build();
    }

    /**
     * 创建失败的识别结果
     */
    public static AIRecognitionResult error(String errorMessage) {
        return AIRecognitionResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .confidence(0.0)
                .build();
    }
}

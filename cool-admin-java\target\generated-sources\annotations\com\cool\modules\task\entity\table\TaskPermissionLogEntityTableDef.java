package com.cool.modules.task.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class TaskPermissionLogEntityTableDef extends TableDef {

    /**
     * 任务权限操作日志实体
     */
    public static final TaskPermissionLogEntityTableDef TASK_PERMISSION_LOG_ENTITY = new TaskPermissionLogEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    public final QueryColumn CLIENT_IP = new QueryColumn(this, "client_ip");

    public final QueryColumn TASK_TYPE = new QueryColumn(this, "task_type");

    public final QueryColumn USERNAME = new QueryColumn(this, "username");

    public final QueryColumn USER_AGENT = new QueryColumn(this, "user_agent");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn DEPARTMENT_ID = new QueryColumn(this, "department_id");

    public final QueryColumn OPERATION_TIME = new QueryColumn(this, "operation_time");

    public final QueryColumn OPERATION_TYPE = new QueryColumn(this, "operation_type");

    public final QueryColumn PERMISSION_RESULT = new QueryColumn(this, "permission_result");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, REMARK, TASK_ID, USER_ID, CLIENT_IP, TASK_TYPE, USERNAME, USER_AGENT, CREATE_TIME, UPDATE_TIME, DEPARTMENT_ID, OPERATION_TIME, OPERATION_TYPE, PERMISSION_RESULT};

    public TaskPermissionLogEntityTableDef() {
        super("", "task_permission_log");
    }

    private TaskPermissionLogEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public TaskPermissionLogEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new TaskPermissionLogEntityTableDef("", "task_permission_log", alias));
    }

}

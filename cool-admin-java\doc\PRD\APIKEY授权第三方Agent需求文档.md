# APIKEY授权第三方Agent需求文档

## 一、背景
为满足第三方AI Agent安全、灵活地调用系统API的需求，需支持APIKEY授权机制，并与现有JWT认证体系保持一致，实现接口与数据的细粒度授权和安全审计。

## 二、目标
- 支持为第三方AI Agent分配独立APIKEY，允许其通过APIKEY调用指定接口。
- APIKEY认证方式与现有JWT认证保持一致，统一使用Authorization请求头，认证流程、权限注入、数据隔离、接口调用方式与JWT完全一致。
- 可细粒度控制每个APIKEY的接口访问权限和数据访问范围（如部门、数据类型等）。
- 支持APIKEY的生成、查询、禁用、删除、权限配置、调用日志审计等。

## 三、APIKEY格式规范
- APIKEY明文统一以`CAK_`前缀开头，例如：`CAK_8f3e2b1c4d5e6f7a8b9c0d1e2f3a4b5c123456`
- 前端页面生成和展示APIKEY时，需完整展示和复制CAK_前缀，调用接口时必须带前缀。
- 认证拦截器通过前缀高效分流，CAK_开头为APIKEY认证，三段.分隔为JWT认证，其他为非法token。

## 四、主要功能
1. APIKEY生成、管理、禁用、删除、重置。
2. 支持为每个用户分配多个APIKEY。
3. APIKEY可设置永久有效或自定义有效期。
4. 支持接口（菜单）权限与部门数据权限的细粒度授权。
5. 支持APIKEY调用日志审计。
6. APIKEY与userId强绑定，权限体系完全复用，认证后注入用户上下文。

## 五、业务流程
1. 管理员在用户管理页面为指定用户生成APIKEY，设置有效期、备注、权限范围。
2. 第三方Agent调用接口时，Header中统一携带 Authorization: Bearer {APIKEY}，APIKEY需带CAK_前缀。
3. 后端拦截器优先判断CAK_前缀，分流APIKEY或JWT认证，查找用户、校验权限、注入上下文。
4. 权限校验通过后，按授权范围返回数据，并记录调用日志。

## 六、权限与安全
- APIKEY与用户强绑定，权限体系复用用户角色、菜单、部门权限。
- 支持APIKEY禁用、重置、删除，权限变更实时生效。
- 支持调用频率限制、IP白名单等安全策略（可扩展）。

## 七、前端管理
- 用户管理详情页新增"APIKEY管理"Tab，支持生成、禁用、重置、删除、查看日志。
- APIKEY生成后只显示一次，后续仅可重置。
- 前端/第三方调用方式与JWT完全一致，无需特殊适配。
- 前端页面需完整展示和复制CAK_前缀，防止用户误删。

## 八、后端接口
- 创建、禁用、重置、删除APIKEY接口
- 查询APIKEY列表、调用日志接口

## 九、典型场景
- 第三方AI Agent长期集成，按需分配接口和数据权限。
- 多Agent协作，分配不同APIKEY，权限独立管理。
- 与JWT认证共用拦截器和权限体系，调用方式一致。

## 十、后续扩展
- 支持APIKEY调用频率限制、IP白名单、调用统计等。
- 支持APIKEY细粒度接口/数据授权。 
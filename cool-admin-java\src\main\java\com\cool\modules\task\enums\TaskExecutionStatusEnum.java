package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务执行状态枚举
 */
@Getter
@AllArgsConstructor
public enum TaskExecutionStatusEnum {
    
    /**
     * 已分配 - 任务已分配给执行人，等待接受
     */
    ASSIGNED("ASSIGNED", "已分配"),
    
    /**
     * 已接受 - 执行人已接受任务，准备开始执行
     */
    ACCEPTED("ACCEPTED", "已接受"),
    
    /**
     * 执行中 - 任务正在执行
     */
    IN_PROGRESS("IN_PROGRESS", "执行中"),
    
    /**
     * 已完成 - 任务执行完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 已拒绝 - 执行人拒绝执行该任务
     */
    REJECTED("REJECTED", "已拒绝"),
    
    /**
     * 已取消 - 任务被取消或撤销
     */
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static TaskExecutionStatusEnum getByCode(String code) {
        if (code == null || code.isEmpty()) return null;
        for (TaskExecutionStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(String code) {
        TaskExecutionStatusEnum status = getByCode(code);
        return status != null ? status.getName() : "未知";
    }
    
    /**
     * 检查是否为有效的执行状态
     */
    public static boolean isValidStatus(String code) {
        return getByCode(code) != null;
    }
    
    /**
     * 检查是否为进行中状态（已分配或执行中）
     */
    public static boolean isActiveStatus(String code) {
        TaskExecutionStatusEnum status = getByCode(code);
        return status == ASSIGNED || status == ACCEPTED || status == IN_PROGRESS;
    }
    
    /**
     * 检查是否为完成状态
     */
    public static boolean isCompletedStatus(String code) {
        TaskExecutionStatusEnum status = getByCode(code);
        return status == COMPLETED;
    }
    
    /**
     * 检查是否为终止状态（已完成、已拒绝、已取消）
     */
    public static boolean isTerminalStatus(String code) {
        TaskExecutionStatusEnum status = getByCode(code);
        return status == COMPLETED || status == REJECTED || status == CANCELLED;
    }
} 
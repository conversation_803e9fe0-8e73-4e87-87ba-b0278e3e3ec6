@echo off
REM VSCode Java Debug UTF-8 启动脚本
chcp 65001 >nul 2>&1

echo ========================================
echo VSCode Java Debug UTF-8 启动脚本
echo ========================================

REM 设置环境变量
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS
set LANG=zh_CN.UTF-8
set LC_ALL=zh_CN.UTF-8

echo 环境变量设置:
echo JAVA_TOOL_OPTIONS=%JAVA_TOOL_OPTIONS%
echo LANG=%LANG%
echo LC_ALL=%LC_ALL%
echo.

REM 启动VSCode
echo 启动VSCode...
code .

echo VSCode已启动，请使用 "Debug Cool Admin Java (UTF-8)" 配置进行调试
pause

<template>
  <div class="template-task-config">
    <div class="task-list">
      <div v-for="(task, index) in modelValue" :key="index" class="task-item">
        <el-input v-model="task.name" placeholder="任务名称" />
        <el-button @click="removeTask(index)" type="danger" size="small">删除</el-button>
      </div>
    </div>
    
    <el-button @click="addTask" type="primary">添加任务</el-button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: any[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: any[]];
}>();

const addTask = () => {
  const newTasks = [...props.modelValue, { name: '', description: '' }];
  emit('update:modelValue', newTasks);
};

const removeTask = (index: number) => {
  const newTasks = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', newTasks);
};
</script>

<style lang="scss" scoped>
.template-task-config {
  padding: 20px;
  
  .task-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
  }
}
</style>

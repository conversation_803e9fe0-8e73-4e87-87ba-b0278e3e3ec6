# 双维度组织架构需求文档

## 1. 项目背景

### 1.1 现状分析
当前Cool Admin系统采用传统的单一部门维度组织架构，存在以下问题：
- 跨部门项目协作困难，权限管理复杂
- 项目管理依赖部门结构，缺乏独立的项目维度
- 用户只能在固定的部门视角下工作，缺乏灵活性
- 项目相关的权限和数据管理与部门权限耦合严重

### 1.2 业务需求
随着企业组织形态的发展，需要支持矩阵式管理模式：
- 支持部门维度和项目维度的双重组织架构
- 用户可以同时归属于部门和多个项目
- 提供灵活的组织形态切换能力
- 保持现有部门权限体系的稳定性

## 2. 需求概述

### 2.1 核心目标
构建一个支持**部门维度**和**项目维度**的双维度组织架构系统，实现：
- 保持现有部门权限体系不变
- 新增独立的项目维度组织架构
- 用户可在两种组织形态间自由切换
- 项目维度采用全局角色，统一权限管理

### 2.2 目标用户
- **系统管理员**：管理双维度组织架构配置
- **部门经理**：在部门维度下管理部门事务
- **项目经理**：在项目维度下管理项目事务
- **普通员工**：根据工作需要切换组织形态

## 3. 功能需求

### 3.1 组织形态管理

#### 3.1.1 组织形态定义
- **部门维度（DEPARTMENT）**：传统的部门组织架构
  - 保持现有的部门权限体系
  - 用户归属于特定部门
  - 基于部门的数据权限过滤

- **项目维度（PROJECT）**：新增的项目组织架构
  - 独立于部门的项目管理体系
  - 用户可以参与多个项目
  - 基于项目的数据权限过滤

#### 3.1.2 组织形态切换
- 用户可以在部门维度和项目维度间切换
- 切换后系统界面、菜单、数据视图相应变化
- 保存用户的组织形态偏好设置
- 提供切换确认和引导机制

### 3.2 项目维度功能

#### 3.2.1 项目管理
- **项目创建**：创建独立的项目实体
- **项目信息**：项目名称、描述、状态、优先级等
- **项目生命周期**：进行中、已完成、已暂停、已取消
- **项目分类**：支持项目标签和分类管理

#### 3.2.2 项目成员管理
- **成员添加**：将用户添加到项目中
- **角色分配**：为项目成员分配全局项目角色
- **权限管理**：基于角色的项目权限控制
- **成员移除**：从项目中移除成员

#### 3.2.3 全局项目角色
定义四种全局项目角色，适用于所有项目：

- **PROJECT_OWNER（项目负责人）**
  - 项目完全控制权限
  - 可以删除项目、管理所有成员
  - 拥有项目内所有功能权限

- **PROJECT_ADMIN（项目管理员）**
  - 项目管理权限
  - 可以编辑项目、管理成员（除负责人外）
  - 拥有任务管理和报表权限

- **PROJECT_MEMBER（项目成员）**
  - 项目参与权限
  - 可以查看项目信息、创建和编辑任务
  - 拥有基础的项目功能权限

- **PROJECT_VIEWER（项目观察者）**
  - 项目只读权限
  - 只能查看项目信息和报表
  - 无法进行任何编辑操作

**注意**：这些全局项目角色将作为系统角色在角色管理界面中配置，管理员可以为每个角色分配具体的菜单权限，而不是在代码中硬编码菜单权限。

### 3.3 权限体系

#### 3.3.1 双维度权限计算
- **系统级权限**：系统管理员拥有所有权限
- **部门维度权限**：基于用户的部门角色计算
- **项目维度权限**：基于用户的项目角色计算
- **权限继承**：高级权限自动包含低级权限

#### 3.3.2 数据权限过滤
- **部门维度**：用户只能访问有权限的部门数据
- **项目维度**：用户只能访问参与的项目数据
- **动态过滤**：根据当前组织形态应用对应的数据过滤规则

### 3.4 用户界面需求

#### 3.4.1 组织形态切换器
- 在系统顶部提供组织形态切换入口
- 显示当前组织形态和可切换的形态
- 提供切换确认对话框
- 支持快捷键切换

#### 3.4.2 动态菜单系统
- **部门形态菜单**：显示部门相关功能菜单（基于现有菜单权限配置）
- **项目形态菜单**：显示项目相关功能菜单（通过角色管理界面配置）
- **权限控制**：根据用户在当前组织形态下的角色动态显示菜单项
- **平滑切换**：组织形态切换时菜单平滑过渡
- **菜单配置**：管理员可在系统管理→菜单管理中配置项目维度的菜单结构
- **角色权限**：管理员可在系统管理→角色管理中为项目角色分配菜单权限

#### 3.4.3 项目工作台
- **项目概览**：显示用户参与的所有项目
- **项目仪表板**：项目进度、任务统计、成员信息
- **我的项目**：用户参与的项目列表和角色信息
- **项目报表**：项目相关的统计和分析报表

## 4. 非功能需求

### 4.1 性能要求
- 组织形态切换响应时间 < 2秒
- 权限计算缓存机制，减少数据库查询
- 支持1000+项目和10000+用户的并发访问
- 菜单加载时间 < 1秒

### 4.2 兼容性要求
- 保持现有部门权限体系100%兼容
- 现有用户数据无需迁移，自动适配
- 支持渐进式升级，不影响现有功能
- 向后兼容现有API接口

### 4.3 安全性要求
- 权限切换需要验证用户身份
- 防止权限绕过和越权访问
- 完整的操作审计日志
- 敏感操作需要二次确认

### 4.4 可用性要求
- 界面友好，操作简单直观
- 提供完整的用户操作指南
- 支持多语言界面
- 响应式设计，支持移动端访问

## 5. 业务流程

### 5.1 用户组织形态切换流程
```
用户登录 → 选择组织形态 → 验证权限 → 加载对应菜单 → 应用数据过滤 → 进入工作界面
```

### 5.2 项目创建流程
```
创建项目 → 设置项目信息 → 添加项目成员 → 分配项目角色 → 配置项目权限 → 项目启动
```

### 5.3 项目成员管理流程
```
选择项目 → 添加成员 → 选择全局角色 → 设置权限范围 → 确认分配 → 通知成员
```

## 6. 验收标准

### 6.1 功能验收
- [ ] 用户可以在部门维度和项目维度间切换
- [ ] 项目维度下用户可以参与多个项目
- [ ] 全局项目角色在所有项目中权限一致
- [ ] 部门权限体系保持不变
- [ ] 权限计算准确，无越权访问

### 6.2 性能验收
- [ ] 组织形态切换时间 < 2秒
- [ ] 菜单加载时间 < 1秒
- [ ] 支持1000+并发用户
- [ ] 权限查询响应时间 < 500ms

### 6.3 兼容性验收
- [ ] 现有部门功能正常运行
- [ ] 现有用户数据完整保留
- [ ] API接口向后兼容
- [ ] 数据库结构平滑升级

## 7. 风险评估

### 7.1 技术风险
- **权限复杂性**：双维度权限计算可能增加系统复杂性
- **性能影响**：权限查询可能影响系统性能
- **数据一致性**：双维度数据可能存在一致性问题

### 7.2 业务风险
- **用户适应性**：用户需要时间适应新的组织形态
- **培训成本**：需要对用户进行系统培训
- **变更管理**：组织架构变更可能影响现有工作流程

### 7.3 风险缓解措施
- 分阶段实施，降低技术风险
- 充分测试，确保系统稳定性
- 提供详细的用户培训和文档
- 建立完善的回滚机制

## 8. 实施计划

### 8.1 开发阶段
- **第一阶段（2周）**：基础架构和数据模型
- **第二阶段（2周）**：前端界面和组织形态切换
- **第三阶段（2周）**：业务功能集成和测试
- **第四阶段（1周）**：优化完善和上线准备

### 8.2 上线计划
- **灰度发布**：先在小范围用户中测试
- **逐步推广**：根据反馈逐步扩大使用范围
- **全面上线**：确保稳定后全面推广使用

## 9. 成功指标

### 9.1 技术指标
- 系统稳定性 > 99.9%
- 响应时间满足性能要求
- 零安全事故
- 用户满意度 > 90%

### 9.2 业务指标
- 项目协作效率提升 > 30%
- 跨部门项目管理便利性提升 > 50%
- 用户组织形态切换使用率 > 60%
- 项目维度功能使用率 > 80%

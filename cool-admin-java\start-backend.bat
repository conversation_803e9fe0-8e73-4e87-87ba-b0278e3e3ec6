@echo off
chcp 65001 >nul
echo === Cool Admin Java 后端服务启动脚本 ===

REM 检查当前目录
echo 当前目录: %CD%

REM 设置环境变量
set SPRING_PROFILES_ACTIVE=local
set SERVER_PORT=8001
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS

REM 检查是否有已编译的jar包
for /r target %%i in (*.jar) do (
    if not "%%~ni"=="*sources" (
        set JAR_FILE=%%i
        goto :found_jar
    )
)

:found_jar
if defined JAR_FILE (
    echo 找到JAR文件: %JAR_FILE%
    echo 启动Java应用...
    
    REM 启动应用
    start "Cool Admin Java" java -Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS -jar "%JAR_FILE%"
    
    echo ✅ Java应用已启动
    echo 应用将在端口8001运行
    
) else (
    echo 未找到JAR文件，尝试使用Maven启动...
    
    REM 检查Maven是否可用
    mvn -version >nul 2>&1
    if %errorlevel% equ 0 (
        echo 使用Maven启动开发服务器...
        start "Cool Admin Maven" mvn spring-boot:run -Dspring-boot.run.profiles=local -Dspring-boot.run.jvmArguments="-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"
        echo ✅ Maven开发服务器已启动
    ) else (
        echo ❌ 未找到Maven，请先编译项目:
        echo    mvn clean package -DskipTests
        echo    然后重新运行此脚本
        pause
        exit /b 1
    )
)

REM 等待服务启动
echo 等待服务启动...
timeout /t 5 /nobreak >nul

REM 检查服务状态
echo 检查服务状态...
curl -s http://localhost:8001/admin/base/open/eps >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务启动成功，EPS接口可访问
    echo 🌐 服务地址: http://localhost:8001
    echo 📋 EPS接口: http://localhost:8001/admin/base/open/eps
) else (
    echo ⚠️  服务可能还在启动中，请稍等片刻后检查
    echo 🔍 可以通过以下命令检查日志:
    echo   查看控制台输出
)

echo === 启动完成 ===
pause 
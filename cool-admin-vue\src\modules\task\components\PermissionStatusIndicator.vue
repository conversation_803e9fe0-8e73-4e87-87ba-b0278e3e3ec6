<template>
  <div class="permission-status-indicator">
    <el-tooltip :content="permissionText" placement="top">
      <el-icon 
        :class="permissionClass" 
        :color="permissionColor"
        :size="iconSize"
      >
        <component :is="permissionIcon" />
      </el-icon>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermission } from '../hooks/usePermission'

const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  taskType: {
    type: String,
    default: 'package'
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  }
})

const { getTaskPermissionLevel } = usePermission()

const permissionLevel = computed(() => {
  return getTaskPermissionLevel(props.task)
})

const permissionText = computed(() => {
  const texts = {
    'full': '完全权限 - 可完全管理此任务',
    'department': '部门权限 - 可编辑和分配此任务',
    'limited': '受限权限 - 仅可查看此任务',
    'none': '无权限 - 无法访问此任务'
  }
  return texts[permissionLevel.value] || '未知权限'
})

const permissionClass = computed(() => {
  return {
    'permission-icon': true,
    [`permission-${permissionLevel.value}`]: true,
    [`size-${props.size}`]: true
  }
})

const permissionColor = computed(() => {
  const colors = {
    'full': '#67c23a',
    'department': '#409eff', 
    'limited': '#e6a23c',
    'none': '#909399'
  }
  return colors[permissionLevel.value] || '#909399'
})

const permissionIcon = computed(() => {
  const icons = {
    'full': 'SuccessFilled',
    'department': 'UserFilled',
    'limited': 'WarningFilled',
    'none': 'CircleCloseFilled'
  }
  return icons[permissionLevel.value] || 'QuestionFilled'
})

const iconSize = computed(() => {
  const sizes = {
    'small': 14,
    'default': 16,
    'large': 18
  }
  return sizes[props.size] || 16
})
</script>

<style scoped>
.permission-status-indicator {
  display: inline-flex;
  align-items: center;
}

.permission-icon {
  transition: all 0.3s ease;
}

.permission-icon:hover {
  transform: scale(1.1);
}

.permission-full {
  filter: drop-shadow(0 0 2px rgba(103, 194, 58, 0.3));
}

.permission-department {
  filter: drop-shadow(0 0 2px rgba(64, 158, 255, 0.3));
}

.permission-limited {
  filter: drop-shadow(0 0 2px rgba(230, 162, 60, 0.3));
}

.permission-none {
  opacity: 0.6;
}

.size-small .permission-icon {
  font-size: 12px;
}

.size-default .permission-icon {
  font-size: 14px;
}

.size-large .permission-icon {
  font-size: 16px;
}
</style> 
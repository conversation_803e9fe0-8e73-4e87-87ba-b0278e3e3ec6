# 智能填充md模板与数据源设计文档

---

## 1. 设计目标与背景

本设计文档旨在规范如何将物业相关的md模板（如公示表、工作报告等）与多源数据（如master_data.txt、business_data.txt、financial_data.txt、smart_work_order.txt）自动匹配、统计、填充，输出结构化数据，便于大模型理解和自动化渲染。

---

## 2. 支持的md模板及结构说明

### 2.1 area-overview.md
- 物业服务区域概况公示表，包含基本信息、物业服务信息、业主共有部分。

### 2.2 owner-income-expense.md
- 业主共有部分经营收益收支情况公示表，主要为表格型，分收入、支出、结余等。

### 2.3 fund-income-expense.md
- 物业服务资金收支情况公示表，表格型，分收入、支出、酬金、结余等。

### 2.4 issue-report.md
- 物业小区问题解决情况工作报告，表格型，分诉求渠道、主责/非主责问题、满意度等。

---

## 3. 数据源文件结构说明

### 3.1 master_data.txt
- key-value型，包含社区、项目、地理、管理、物业、收费、面积、设施、状态等基础信息。

### 3.2 business_data.txt
- 表格型，字段丰富，涵盖事件类型、渠道来源、服务内容、评价、有效性等。

### 3.3 financial_data.txt
- 行式表格，科目树结构，包含收入、成本、费用、利润等多级明细。

### 3.4 smart_work_order.txt
- 表格型，智慧工单相关，包含工单编号、类型、渠道、状态、满意度等。

---

## 4. 字段映射与统计规则（以issue-report.md为例）

| 模板字段         | 数据源字段/计算方式                                      |
|------------------|--------------------------------------------------------|
| 诉求渠道         | 渠道来源/诉求渠道/事件类型归类（见归类规则）            |
| 反映问题总量     | COUNT(渠道=当前渠道)                                    |
| 响应时间         | AVG(事件处理时间-事件创建时间)                          |
| 物业主责问题_总量 | COUNT(渠道=当前渠道 AND 责任=物业)                      |
| 物业主责问题_已解决 | COUNT(渠道=当前渠道 AND 责任=物业 AND 状态=已解决)      |
| 物业主责问题_待解决 | COUNT(渠道=当前渠道 AND 责任=物业 AND 状态=未解决)      |
| 物业主责问题_满意度 | AVG(渠道=当前渠道 AND 责任=物业 的满意度/评价星级)      |
| 其他问题_总量     | COUNT(渠道=当前渠道 AND 责任=非物业)                    |
| 其他问题_已上报   | COUNT(渠道=当前渠道 AND 责任=非物业 AND 已上报=是)      |
| 其他问题_协助解决 | COUNT(渠道=当前渠道 AND 责任=非物业 AND 协助解决=是)    |
| 备注             | 可留空或补充说明                                        |

- smart_work_order.txt全部归为“智慧工单”渠道。
- business_data.txt用“事件类型”字段归类渠道。

---

## 5. 满意率等特殊字段的统计与缺省值处理规范

- 优先用“是否好评”字段，其次“满意度”，再次“评价星级>=4”。
- 排除“系统默认三星好评”时，需剔除相关数据。
- 若所有相关字段均缺失，输出“-”。
- 统计公式：满意率 = 满意评价数量 / 有效评价总数 × 100%，无数据时为“-”。

---

## 6. 输出数据结构设计方案

### 6.1 顶层结构
```json
{
  "template_key": "issue-report",
  "meta": {"项目名称": "梦之家管理处", "日期": "2024-06-10"},
  "sections": [
    {
      "type": "table",
      "title": "物业小区问题解决情况",
      "headers": ["序号", "诉求渠道", ...],
      "rows": [
        {"序号": 1, "诉求渠道": "客户问询", ...},
        ...
      ]
    }
  ]
}
```

### 6.2 area-overview.md 示例
```json
{
  "template_key": "area-overview",
  "meta": {
    "区域名称": "梦之家管理处",
    "所在省": "广东省",
    "所在市": "深圳市",
    "所在区": "南山区",
    "街道": "南山街道",
    "社区": "滨海社区",
    "区域范围": {"东至": "", "南至": "", "西至": "", "北至": ""},
    "总建筑面积": 207188,
    "总户数": 1916
  },
  "sections": [
    {
      "type": "kv",
      "title": "物业服务信息",
      "data": {
        "物业服务人": "xxx物业公司",
        "物业服务合同期限": "2020-11-01 ~ 2025-10-31",
        "物业服务执行标准": "国家/地方标准",
        "物业服务收费方式": "酬金制",
        "物业服务收费标准": {"高层": "4.5元/月", "别墅": "6.5元/月", "商业": "7.8元/月"}
      }
    },
    {
      "type": "table",
      "title": "业主共有部分",
      "headers": ["序号", "名称", "面积/数量", "坐落位置"],
      "rows": [
        {"序号": 1, "名称": "物业服务用房", "面积/数量": 100, "坐落位置": "1号楼"},
        {"序号": 2, "名称": "业主共有车位", "面积/数量": 2180, "坐落位置": "地下停车场"}
      ]
    }
  ]
}
```

### 6.3 owner-income-expense.md 示例
```json
{
  "template_key": "owner-income-expense",
  "meta": {
    "物业服务区域": "梦之家管理处",
    "物业服务人": "xxx物业公司",
    "日期": "2024-06-10"
  },
  "sections": [
    {
      "type": "table",
      "title": "业主共有部分经营收益收支情况",
      "headers": ["项目", "序列", "金额(元)", "备注"],
      "rows": [
        {"项目": "1.业主共有部分经营收入", "序列": 1, "金额(元)": 100000, "备注": ""},
        {"项目": "1.1共有停车位收入", "序列": 2, "金额(元)": 20000, "备注": ""},
        {"项目": "6.累计经营收益结余(属业主共有)", "序列": 29, "金额(元)": 50000, "备注": ""}
      ]
    }
  ]
}
```

### 6.4 fund-income-expense.md 示例
```json
{
  "template_key": "fund-income-expense",
  "meta": {
    "物业服务区域": "梦之家管理处",
    "物业服务人": "xxx物业公司",
    "日期": "2024-06-10"
  },
  "sections": [
    {
      "type": "table",
      "title": "物业服务资金收支情况",
      "headers": ["项目", "序列", "预算(元)", "当季(元)", "累计(元)", "备注"],
      "rows": [
        {"项目": "1.预收物业服务资金收入", "序列": 1, "预算(元)": 100000, "当季(元)": 25000, "累计(元)": 80000, "备注": ""},
        {"项目": "1.1住宅", "序列": 2, "预算(元)": 60000, "当季(元)": 15000, "累计(元)": 50000, "备注": ""},
        {"项目": "6.物业服务资金结余(累计)", "序列": 48, "预算(元)": "", "当季(元)": "", "累计(元)": 20000, "备注": ""}
      ]
    }
  ]
}
```

### 6.5 issue-report.md 示例
```json
{
  "template_key": "issue-report",
  "meta": {
    "项目名称": "梦之家管理处",
    "日期": "2024-06-10"
  },
  "sections": [
    {
      "type": "table",
      "title": "物业小区问题解决情况",
      "headers": [
        "序号", "诉求渠道", "反映问题总量", "响应时间",
        "物业主责问题_总量", "物业主责问题_已解决", "物业主责问题_待解决", "物业主责问题_满意度",
        "其他问题_总量", "其他问题_已上报", "其他问题_协助解决", "备注"
      ],
      "rows": [
        {
          "序号": 1,
          "诉求渠道": "客户问询",
          "反映问题总量": 20,
          "响应时间": "2.5天",
          "物业主责问题_总量": 10,
          "物业主责问题_已解决": 8,
          "物业主责问题_待解决": 2,
          "物业主责问题_满意度": "95%",
          "其他问题_总量": 10,
          "其他问题_已上报": 6,
          "其他问题_协助解决": 3,
          "备注": ""
        }
      ]
    }
  ]
}
```

### 6.6 字段说明
- template_key：模板标识
- meta：基础信息
- sections：区块列表（表格、kv、说明等）
- headers：表头
- rows：每行为dict，key为表头字段

---

## 7. 归类与映射规则

### 7.1 诉求渠道归类
- 客户报修、客户投诉、客户问询：直接归类
- 其他类型（如商品订单、社区活动等）：归为“其他”
- smart_work_order.txt全部为“智慧工单”

### 7.2 责任归属
- 依据责任字段或业务规则判断“物业主责”与“非物业”

### 7.3 状态归类
- “已解决”=已完成/已解决/已关闭等
- “待解决”=待处理/处理中/未解决等

### 7.4 诉求渠道归类建议（与模板诉求渠道的映射）

| 数据源事件类型/渠道来源 | 模板诉求渠道 |
|------------------------|--------------|
| 客户报修               | 客户报修     |
| 客户投诉               | 客户投诉     |
| 客户问询               | 客户问询     |
| 智慧工单（smart_work_order.txt全部） | 智慧工单 |
| 商品订单               | 其他         |
| 社区活动               | 其他         |
| 物品放行               | 其他         |
| 物品借用               | 其他         |
| 线上物业缴费           | 其他         |
| 装修申请               | 其他         |
| 其他/未知/空值         | 其他         |

- 仅“客户报修”、“客户投诉”、“客户问询”直接归入同名渠道。
- smart_work_order.txt全部归为“智慧工单”。
- 其余类型统一归为“其他”。
- 若后续有新类型，默认归为“其他”。

---

## 8. 未来可扩展性建议

- 支持更多模板类型（如多表格、嵌套结构等）
- 支持多数据源自动合并与字段映射配置
- 输出结构可扩展为多模板批量处理
- 字段归类、映射、统计规则可配置化
- 支持多语言、多格式输出

---

如需补充具体字段映射表、代码示例或业务规则细化，请在本设计文档基础上补充。 
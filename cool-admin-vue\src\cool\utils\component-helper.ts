import { ref, Ref, nextTick } from 'vue';
import { ElMessage } from 'element-plus';

/**
 * 安全的组件引用调用工具
 * 用于避免 "Cannot read properties of undefined" 错误
 */
export class SafeComponentRef<T = any> {
	private componentRef: Ref<T | null>;
	private retryCount = 0;
	private maxRetries = 5;
	private retryInterval = 100;

	constructor(componentRef: Ref<T | null>) {
		this.componentRef = componentRef;
	}

	/**
	 * 安全调用组件方法
	 */
	async safeCall<R = any>(
		methodName: string,
		args: any[] = [],
		options: {
			timeout?: number;
			showError?: boolean;
			fallback?: () => R;
		} = {}
	): Promise<R | null> {
		const { timeout = 3000, showError = true, fallback } = options;

		return new Promise((resolve, reject) => {
			const startTime = Date.now();

			const checkAndCall = async () => {
				// 检查超时
				if (Date.now() - startTime > timeout) {
					const error = `Component method "${methodName}" call timeout after ${timeout}ms`;
					console.error(error);
					
					if (showError) {
						ElMessage.error(`组件方法调用超时: ${methodName}`);
					}
					
					if (fallback) {
						resolve(fallback());
					} else {
						reject(new Error(error));
					}
					return;
				}

				// 检查组件是否已初始化
				if (!this.componentRef.value) {
					this.retryCount++;
					console.warn(`Component not ready, retry ${this.retryCount}/${this.maxRetries} for method: ${methodName}`);
					
					if (this.retryCount >= this.maxRetries) {
						const error = `Component not initialized after ${this.maxRetries} retries for method: ${methodName}`;
						console.error(error);
						
						if (showError) {
							ElMessage.error(`组件未就绪: ${methodName}`);
						}
						
						if (fallback) {
							resolve(fallback());
						} else {
							reject(new Error(error));
						}
						return;
					}

					// 等待后重试
					setTimeout(checkAndCall, this.retryInterval);
					return;
				}

				// 检查方法是否存在
				const component = this.componentRef.value as any;
				if (!component || typeof component[methodName] !== 'function') {
					const error = `Method "${methodName}" not found on component`;
					console.error(error, component);
					
					if (showError) {
						ElMessage.error(`组件方法不存在: ${methodName}`);
					}
					
					if (fallback) {
						resolve(fallback());
					} else {
						reject(new Error(error));
					}
					return;
				}

				// 安全调用方法
				try {
					const result = await component[methodName](...args);
					this.retryCount = 0; // 重置重试计数
					resolve(result);
				} catch (error) {
					console.error(`Error calling method "${methodName}":`, error);
					
					if (showError) {
						ElMessage.error(`组件方法调用失败: ${methodName}`);
					}
					
					if (fallback) {
						resolve(fallback());
					} else {
						reject(error);
					}
				}
			};

			// 立即检查一次
			nextTick(checkAndCall);
		});
	}

	/**
	 * 检查组件是否已就绪
	 */
	isReady(): boolean {
		return !!this.componentRef.value;
	}

	/**
	 * 等待组件就绪
	 */
	async waitForReady(timeout = 3000): Promise<boolean> {
		return new Promise((resolve) => {
			const startTime = Date.now();
			
			const check = () => {
				if (this.isReady()) {
					resolve(true);
					return;
				}
				
				if (Date.now() - startTime > timeout) {
					resolve(false);
					return;
				}
				
				setTimeout(check, 50);
			};
			
			check();
		});
	}
}

/**
 * 创建安全的组件引用
 */
export function createSafeComponentRef<T = any>(componentRef: Ref<T | null>): SafeComponentRef<T> {
	return new SafeComponentRef(componentRef);
}

/**
 * 安全调用Form.open方法的辅助函数
 */
export async function safeFormOpen(
	formRef: Ref<any>, 
	options: any, 
	fallbackMessage = '表单组件未就绪，请稍后重试'
) {
	const safeForm = createSafeComponentRef(formRef);
	
	try {
		await safeForm.safeCall('open', [options], {
			timeout: 3000,
			showError: true,
			fallback: () => {
				ElMessage.warning(fallbackMessage);
				return null;
			}
		});
	} catch (error) {
		console.error('Safe form open failed:', error);
		ElMessage.error(fallbackMessage);
	}
}

/**
 * 安全调用组件方法的装饰器
 */
export function SafeMethodCall(options: { 
	timeout?: number; 
	showError?: boolean; 
	fallbackMessage?: string 
} = {}) {
	return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
		const originalMethod = descriptor.value;

		descriptor.value = async function (...args: any[]) {
			try {
				return await originalMethod.apply(this, args);
			} catch (error: any) {
				console.error(`Safe method call failed for ${propertyKey}:`, error);
				
				if (error?.message?.includes('Cannot read properties of undefined')) {
					const message = options.fallbackMessage || '组件未就绪，请稍后重试';
					if (options.showError !== false) {
						ElMessage.warning(message);
					}
					return null;
				}
				
				throw error;
			}
		};

		return descriptor;
	};
} 
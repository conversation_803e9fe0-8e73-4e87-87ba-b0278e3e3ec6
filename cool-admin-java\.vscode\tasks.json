{"version": "2.0.0", "tasks": [{"label": "Start Cool Admin Java (Maven)", "type": "shell", "command": "powershell", "args": ["-Command", "mvn spring-boot:run -Dspring-boot.run.profiles=local -Dspring-boot.run.jvmArguments='-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS'"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"}}, "problemMatcher": []}, {"label": "Build Cool Admin Java", "type": "shell", "command": "powershell", "args": ["-Command", "mvn clean compile -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Start with PowerShell Script", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", "./start-backend.ps1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Clean and Compile", "type": "shell", "command": "powershell", "args": ["-Command", "mvn clean compile -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}
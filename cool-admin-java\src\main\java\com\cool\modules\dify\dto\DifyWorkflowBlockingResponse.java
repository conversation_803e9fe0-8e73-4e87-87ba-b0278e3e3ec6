package com.cool.modules.dify.dto;

import lombok.Data;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

@Data
public class DifyWorkflowBlockingResponse {
    private String workflowRunId;
    private String taskId;
    private DataDetail data;

    @Data
    public static class DataDetail {
        private String id;
        private String workflowId;
        private String status;
        private JSONObject outputs;
        private Object error;
        private Double elapsedTime;
        private Integer totalTokens;
        private Integer totalSteps;
        private Long createdAt;
        private Long finishedAt;


        public JSONObject getJSONObject(String key) {
            if (outputs.containsKey(key)) {
                String json = outputs.getStr(key);
                return JSONUtil.parseObj(json);
            }
            return null;
        }

        public String getString(String key) {
            if (outputs.containsKey(key)) {
                return outputs.getStr(key);
            }
            return null;
        }
    }
} 
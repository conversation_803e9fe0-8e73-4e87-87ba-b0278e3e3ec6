package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class WorkOrderEntityTableDef extends TableDef {

    /**
     * 工单实体
     */
    public static final WorkOrderEntityTableDef WORK_ORDER_ENTITY = new WorkOrderEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn TITLE = new QueryColumn(this, "title");

    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn ORDER_NO = new QueryColumn(this, "order_no");

    public final QueryColumn URGENCY = new QueryColumn(this, "urgency");

    public final QueryColumn PRIORITY = new QueryColumn(this, "priority");

    public final QueryColumn PROGRESS = new QueryColumn(this, "progress");

    /**
     * 关联项目ID
     */
    public final QueryColumn PROJECT_ID = new QueryColumn(this, "project_id");

    public final QueryColumn ASSIGNEE_ID = new QueryColumn(this, "assignee_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn AI_SCHEDULED = new QueryColumn(this, "ai_scheduled");

    public final QueryColumn APPLICANT_ID = new QueryColumn(this, "applicant_id");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    public final QueryColumn ASSIGNEE_NAME = new QueryColumn(this, "assignee_name");

    public final QueryColumn BUSINESS_DATA = new QueryColumn(this, "business_data");

    public final QueryColumn BUSINESS_TYPE = new QueryColumn(this, "business_type");

    public final QueryColumn QUALITY_SCORE = new QueryColumn(this, "quality_score");

    public final QueryColumn ACTUAL_END_TIME = new QueryColumn(this, "actual_end_time");

    public final QueryColumn APPLICANT_DEPT = new QueryColumn(this, "applicant_dept");

    public final QueryColumn APPLICANT_NAME = new QueryColumn(this, "applicant_name");

    public final QueryColumn EXECUTION_TEAM = new QueryColumn(this, "execution_team");

    public final QueryColumn FAILURE_REASON = new QueryColumn(this, "failure_reason");

    public final QueryColumn SOP_TEMPLATE_ID = new QueryColumn(this, "sop_template_id");

    public final QueryColumn ACTUAL_WORK_TIME = new QueryColumn(this, "actual_work_time");

    public final QueryColumn PLANNED_END_TIME = new QueryColumn(this, "planned_end_time");

    public final QueryColumn ACTUAL_START_TIME = new QueryColumn(this, "actual_start_time");

    public final QueryColumn APPLICANT_DEPT_ID = new QueryColumn(this, "applicant_dept_id");

    public final QueryColumn EXECUTION_RESULT = new QueryColumn(this, "execution_result");

    public final QueryColumn AI_RISK_ASSESSMENT = new QueryColumn(this, "ai_risk_assessment");

    public final QueryColumn AI_SCHEDULE_CONFIG = new QueryColumn(this, "ai_schedule_config");

    public final QueryColumn CUSTOMER_FEEDBACK = new QueryColumn(this, "customer_feedback");

    public final QueryColumn PLANNED_START_TIME = new QueryColumn(this, "planned_start_time");

    public final QueryColumn ESTIMATED_WORK_TIME = new QueryColumn(this, "estimated_work_time");

    public final QueryColumn RELATED_BUSINESS_ID = new QueryColumn(this, "related_business_id");

    public final QueryColumn AI_PREDICTED_END_TIME = new QueryColumn(this, "ai_predicted_end_time");

    public final QueryColumn RELATED_BUSINESS_TYPE = new QueryColumn(this, "related_business_type");

    public final QueryColumn CUSTOMER_SATISFACTION = new QueryColumn(this, "customer_satisfaction");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, TITLE, REMARK, STATUS, ORDER_NO, URGENCY, PRIORITY, PROGRESS, PROJECT_ID, ASSIGNEE_ID, CREATE_TIME, UPDATE_TIME, AI_SCHEDULED, APPLICANT_ID, DESCRIPTION, ASSIGNEE_NAME, BUSINESS_DATA, BUSINESS_TYPE, QUALITY_SCORE, ACTUAL_END_TIME, APPLICANT_DEPT, APPLICANT_NAME, EXECUTION_TEAM, FAILURE_REASON, SOP_TEMPLATE_ID, ACTUAL_WORK_TIME, PLANNED_END_TIME, ACTUAL_START_TIME, APPLICANT_DEPT_ID, EXECUTION_RESULT, AI_RISK_ASSESSMENT, AI_SCHEDULE_CONFIG, CUSTOMER_FEEDBACK, PLANNED_START_TIME, ESTIMATED_WORK_TIME, RELATED_BUSINESS_ID, AI_PREDICTED_END_TIME, RELATED_BUSINESS_TYPE, CUSTOMER_SATISFACTION};

    public WorkOrderEntityTableDef() {
        super("", "sop_work_order");
    }

    private WorkOrderEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public WorkOrderEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new WorkOrderEntityTableDef("", "sop_work_order", alias));
    }

}

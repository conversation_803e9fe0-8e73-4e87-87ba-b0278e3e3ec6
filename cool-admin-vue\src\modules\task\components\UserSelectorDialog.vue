<template>
	<el-dialog v-model="dialogVisible" :title="title" width="800px" @close="handleClose">
		<cl-crud ref="Crud">
			<cl-row>
				<cl-flex1 />
				<cl-search-key placeholder="搜索姓名、用户名" />
			</cl-row>
			<cl-row>
				<cl-table ref="Table" />
			</cl-row>
		</cl-crud>
		<template #footer>
			<el-button @click="handleClose">取消</el-button>
			<el-button type="primary" @click="handleConfirm">确认</el-button>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { useCrud, useTable } from "@cool-vue/crud";
import { useCool } from "/@/cool";

const props = defineProps({
	title: {
		type: String,
		default: "选择执行人"
	}
});

const emit = defineEmits(["confirm"]);

const { service } = useCool();

const dialogVisible = ref(false);

const Crud = useCrud(
	{
		service: service.base.sys.user
	},
	(app) => {
		app.refresh();
	}
);

const Table = useTable({
	columns: [
		{
			type: "selection",
			width: 60
		},
		{
			label: "姓名",
			prop: "name"
		},
		{
			label: "用户名",
			prop: "username"
		},
		{
			label: "手机号",
			prop: "phone"
		}
	]
});

const open = () => {
	dialogVisible.value = true;
	Crud.value?.refresh();
};

const handleClose = () => {
	dialogVisible.value = false;
	Table.value?.clearSelection();
};

const handleConfirm = () => {
	const selection = Table.value?.selection;
	if (!selection || selection.length === 0) {
		return ElMessage.warning("请选择至少一个执行人");
	}
	emit("confirm", selection);
	handleClose();
};

defineExpose({
	open
});
</script> 
{"files.encoding": "utf8", "files.autoGuessEncoding": true, "terminal.integrated.encoding": "utf8", "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.env.windows": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"}, "java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.debug.settings.hotCodeReplace": "auto", "java.test.config": {"vmargs": ["-Dfile.encoding=UTF-8", "-Dspring.output.ansi.enabled=ALWAYS"]}}
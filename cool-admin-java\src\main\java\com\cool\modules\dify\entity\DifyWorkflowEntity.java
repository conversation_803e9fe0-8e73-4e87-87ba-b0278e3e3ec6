package com.cool.modules.dify.entity;

import com.cool.core.base.BaseEntity;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Dify工作流实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "dify_workflow", comment = "Dify工作流")
public class DifyWorkflowEntity extends BaseEntity<DifyWorkflowEntity> {

    @ColumnDefine(comment = "英文名称", length = 100)
    private String name;

    @ColumnDefine(comment = "描述", length = 255)
    private String description;

    @ColumnDefine(comment = "地址", length = 255)
    private String url;

    @ColumnDefine(comment = "API Key", length = 255)
    private String apiKey;

    @ColumnDefine(comment = "入参", type = "text")
    private String inputParams;

    @ColumnDefine(comment = "输出说明", type = "text")
    private String outputDesc;

    @ColumnDefine(comment = "启用状态", type = "tinyint", defaultValue = "1")
    private Integer status;
} 
<template>
  <el-scrollbar>
    <div class="personal-workbench-page">

      <!-- 欢迎回来 - 个人版本 -->
      <div class="welcome-section-mini">
        <div class="welcome-content-mini">
          <span class="welcome-icon">👋</span>
          <div class="welcome-text">
            <h3>欢迎回来，{{ currentUser.name }}</h3>
            <p>个人工作台 - 专注于您的任务</p>
          </div>
            </div>
            </div>

      <!-- 个人任务统计 -->
      <div class="stats-section">
        <div class="section-header">
          <h3>📊 我的任务概览</h3>
          <p>个人任务执行情况</p>
            </div>
        <el-row :gutter="24">
          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-1">
              <div class="stat-icon">⏳</div>
              <div class="stat-content">
                <div class="stat-number">{{ personalStats.pendingTasks }}</div>
                <div class="stat-label">待执行任务</div>
          </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><Clock /></el-icon>
                <span>紧急</span>
        </div>
    </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-2">
              <div class="stat-icon">⚡</div>
              <div class="stat-content">
                <div class="stat-number">{{ personalStats.inProgressTasks }}</div>
                <div class="stat-label">执行中任务</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><Loading /></el-icon>
                <span>进行中</span>
            </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-3">
              <div class="stat-icon">✅</div>
              <div class="stat-content">
                <div class="stat-number">{{ personalStats.completedTasks }}</div>
                <div class="stat-label">已完成任务</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><Check /></el-icon>
                <span>本周+{{ personalStats.weeklyCompleted }}</span>
              </div>
            </div>
          </el-col>

          <el-col :xl="6" :lg="6" :md="12" :sm="12" :xs="24">
            <div class="stat-card stat-card-4">
              <div class="stat-icon">🎯</div>
              <div class="stat-content">
                <div class="stat-number">{{ personalStats.completionRate }}%</div>
                <div class="stat-label">完成率</div>
              </div>
              <div class="stat-trend">
                <el-icon class="trend-icon"><TrendCharts /></el-icon>
                <span>优秀</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 我的任务列表 -->
      <div class="my-tasks-section">
        <div class="section-header">
          <h3>📋 我的任务</h3>
          <p>当前分配给您的所有任务</p>
        </div>
        
        <!-- 任务筛选标签 -->
        <div class="task-filter-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="待执行" name="pending">
          <div class="task-list">
                <div v-if="pendingTasks.length === 0" class="empty-state">
                  <el-empty description="暂无待执行任务" />
                </div>
                <div v-else>
            <div 
                    v-for="task in pendingTasks"
              :key="task.id"
              class="task-item"
                    @click="handleViewTask(task)"
                  >
                    <div class="task-header">
                      <div class="task-title-info">
                        <h4>{{ task.name }}</h4>
                  <div class="task-meta">
                          <span class="scenario-info">
                            {{ task.scenarioName }} - 步骤{{ task.stepCode }}: {{ task.stepName }}
                          </span>
                          <div class="collaborators-info" v-if="task.executions && task.executions.length > 0">
                            <span class="collaborators-label">执行人员：</span>
                            <div class="collaborators-list">
                              <span
                                v-for="execution in task.executions"
                                :key="execution.id"
                                class="collaborator-item"
                                :class="getCollaboratorClass(execution)"
                              >
                                {{ execution.assigneeName }}
                                <el-tag
                                  :type="getExecutionStatusType(execution.executionStatus)"
                                  size="small"
                                  class="status-tag"
                                >
                                  {{ getExecutionStatusLabel(execution.executionStatus) }}
                                </el-tag>
                              </span>
                  </div>
                </div>
              </div>
                </div>
                      <el-tag type="warning" size="small">待执行</el-tag>
                </div>

                    <div class="task-details">
                      <div class="task-desc">{{ task.description }}</div>

                      <div class="task-core-info">
                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">📍 实体触点：</span>
                            <span class="value">{{ task.entityTouchpoint || '未设置' }}</span>
                          </div>
                          <div class="info-item">
                            <span class="label">🎯 任务活动：</span>
                            <span class="value">{{ task.taskActivity || '未设置' }}</span>
                          </div>
                        </div>

                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">👤 员工行为：</span>
                            <span class="value">{{ task.employeeBehavior || '未设置' }}</span>
                          </div>
                          <div class="info-item">
                            <span class="label">⭐ 工作亮点：</span>
                            <span class="value">{{ task.workHighlight || '未设置' }}</span>
                          </div>
                        </div>

                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">🏷️ 员工角色：</span>
                            <span class="value">{{ task.employeeRole || '未设置' }}</span>
                          </div>
                          <div class="info-item">
                            <span class="label">📷 拍照要求：</span>
                            <el-tag :type="task.photoRequired ? 'success' : 'info'" size="small">
                              {{ task.photoRequired ? '需要拍照' : '无需拍照' }}
                            </el-tag>
                          </div>
                        </div>
                      </div>
                    </div>

                <div class="task-actions">
                      <el-button type="primary" size="small" @click.stop="handleCompleteTask(task)">
                        完成任务
                  </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="执行中" name="inProgress">
              <div class="task-list">
                <div v-if="inProgressTasks.length === 0" class="empty-state">
                  <el-empty description="暂无执行中任务" />
                </div>
                <div v-else>
                  <div
                    v-for="task in inProgressTasks"
                    :key="task.id"
                    class="task-item"
                    @click="handleViewTask(task)"
                  >
                    <div class="task-header">
                      <div class="task-title-info">
                        <h4>{{ task.name }}</h4>
                        <div class="task-meta">
                          <span class="scenario-info">
                            {{ task.scenarioName }} - 步骤{{ task.stepCode }}: {{ task.stepName }}
                          </span>
                          <div class="collaborators-info" v-if="task.executions && task.executions.length > 0">
                            <span class="collaborators-label">执行人员：</span>
                            <div class="collaborators-list">
                              <span
                                v-for="execution in task.executions"
                                :key="execution.id"
                                class="collaborator-item"
                                :class="getCollaboratorClass(execution)"
                              >
                                {{ execution.assigneeName }}
                                <el-tag
                                  :type="getExecutionStatusType(execution.executionStatus)"
                    size="small"
                                  class="status-tag"
                  >
                                  {{ getExecutionStatusLabel(execution.executionStatus) }}
                                </el-tag>
                              </span>
                </div>
              </div>
            </div>
                      </div>
                      <el-tag type="primary" size="small">执行中</el-tag>
          </div>

                    <div class="task-details">
                      <div class="task-desc">{{ task.description }}</div>

                      <div class="task-core-info">
                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">📍 实体触点：</span>
                            <span class="value">{{ task.entityTouchpoint || '未设置' }}</span>
          </div>
                          <div class="info-item">
                            <span class="label">🎯 任务活动：</span>
                            <span class="value">{{ task.taskActivity || '未设置' }}</span>
                          </div>
                        </div>

                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">👤 员工行为：</span>
                            <span class="value">{{ task.employeeBehavior || '未设置' }}</span>
            </div>
                          <div class="info-item">
                            <span class="label">⭐ 工作亮点：</span>
                            <span class="value">{{ task.workHighlight || '未设置' }}</span>
                          </div>
                        </div>

                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">🏷️ 员工角色：</span>
                            <span class="value">{{ task.employeeRole || '未设置' }}</span>
                          </div>
                          <div class="info-item">
                            <span class="label">📷 拍照要求：</span>
                            <el-tag :type="task.photoRequired ? 'success' : 'info'" size="small">
                              {{ task.photoRequired ? '需要拍照' : '无需拍照' }}
                            </el-tag>
                          </div>
                        </div>
            </div>
            
                      <!-- 执行时间信息 -->
                      <div class="execution-time-info">
                        <div class="time-row">
                          <div class="time-item" v-if="task.myExecution?.acceptTime">
                            <span class="time-label">🕐 接单时间：</span>
                            <span class="time-value">{{ formatDateTime(task.myExecution.acceptTime) }}</span>
                          </div>
                          <div class="time-item" v-if="task.actualStartTime">
                            <span class="time-label">🚀 开始时间：</span>
                            <span class="time-value">{{ formatDateTime(task.actualStartTime) }}</span>
                          </div>
                        </div>
                        <div class="time-row" v-if="task.stats">
                          <div class="time-item">
                            <span class="time-label">👥 执行进度：</span>
                            <span class="time-value">
                              {{ task.stats.completedExecutors }}/{{ task.stats.totalExecutors }} 人完成
                              ({{ task.stats.completionRate }}%)
                            </span>
                          </div>
                </div>
              </div>
            </div>

                    <div class="task-actions">
                      <el-button type="success" size="small" @click.stop="handleCompleteTask(task)">
                        完成任务
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="已完成" name="completed">
              <div class="task-list">
                <div v-if="completedTasks.length === 0" class="empty-state">
                  <el-empty description="暂无已完成任务" />
                </div>
                <div v-else>
                  <div
                    v-for="task in completedTasks"
                    :key="task.id"
                    class="task-item completed"
                    @click="handleViewTask(task)"
                  >
                    <div class="task-header">
                      <div class="task-title-info">
                        <h4>{{ task.name }}</h4>
                        <div class="task-meta">
                          <span class="scenario-info">
                            {{ task.scenarioName }} - 步骤{{ task.stepCode }}: {{ task.stepName }}
                          </span>
                          <span class="completion-time" v-if="task.myExecution?.completionTime">
                            我的完成时间：{{ formatDate(task.myExecution.completionTime) }}
                          </span>
                          <div class="collaborators-info" v-if="task.executions && task.executions.length > 0">
                            <span class="collaborators-label">执行人员：</span>
                            <div class="collaborators-list">
                              <span
                                v-for="execution in task.executions"
                                :key="execution.id"
                                class="collaborator-item"
                                :class="getCollaboratorClass(execution)"
                              >
                                {{ execution.assigneeName }}
                                <el-tag
                                  :type="getExecutionStatusType(execution.executionStatus)"
                                  size="small"
                                  class="status-tag"
                                >
                                  {{ getExecutionStatusLabel(execution.executionStatus) }}
                                </el-tag>
                                <span v-if="execution.completionTime" class="completion-time-small">
                                  {{ formatDate(execution.completionTime) }}
                                </span>
                              </span>
                </div>
            </div>
            </div>
          </div>
                      <el-tag type="success" size="small">已完成</el-tag>
          </div>

                    <div class="task-details">
                      <div class="task-desc">{{ task.description }}</div>

                      <div class="task-core-info">
                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">📍 实体触点：</span>
                            <span class="value">{{ task.entityTouchpoint || '未设置' }}</span>
              </div>
                          <div class="info-item">
                            <span class="label">🎯 任务活动：</span>
                            <span class="value">{{ task.taskActivity || '未设置' }}</span>
              </div>
            </div>
            
                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">👤 员工行为：</span>
                            <span class="value">{{ task.employeeBehavior || '未设置' }}</span>
              </div>
                          <div class="info-item">
                            <span class="label">⭐ 工作亮点：</span>
                            <span class="value">{{ task.workHighlight || '未设置' }}</span>
              </div>
            </div>
            
                        <div class="info-row">
                          <div class="info-item">
                            <span class="label">🏷️ 员工角色：</span>
                            <span class="value">{{ task.employeeRole || '未设置' }}</span>
              </div>
                          <div class="info-item">
                            <span class="label">📷 拍照要求：</span>
                            <el-tag :type="task.photoRequired ? 'success' : 'info'" size="small">
                              {{ task.photoRequired ? '需要拍照' : '无需拍照' }}
                            </el-tag>
              </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
            </div>
          </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <div class="section-header">
          <h3>🚀 快捷操作</h3>
          <p>常用功能快速入口</p>
              </div>
        <el-row :gutter="24">
          <el-col :xl="8" :lg="8" :md="12" :sm="12" :xs="24">
            <div class="action-card personal-card" @click="goToMyTasks">
              <div class="action-content">
                <div class="action-icon">📝</div>
                <h4>我的任务</h4>
                <p>查看和管理分配给我的所有任务</p>
                <el-button class="action-btn personal-btn">
                  <el-icon><List /></el-icon>
                  查看任务
                </el-button>
            </div>
          </div>
          </el-col>
          
          <el-col :xl="8" :lg="8" :md="12" :sm="12" :xs="24">
            <div class="action-card report-card" @click="goToReports">
              <div class="action-content">
                <div class="action-icon">📊</div>
                <h4>工作报告</h4>
                <p>查看个人工作统计和绩效报告</p>
                <el-button class="action-btn report-btn">
                  <el-icon><DataAnalysis /></el-icon>
                  查看报告
                </el-button>
            </div>
          </div>
      </el-col>
          
          <el-col :xl="8" :lg="8" :md="12" :sm="12" :xs="24">
            <div class="action-card help-card" @click="goToHelp">
              <div class="action-content">
                <div class="action-icon">❓</div>
                <h4>帮助中心</h4>
                <p>查看使用指南和常见问题解答</p>
                <el-button class="action-btn help-btn">
                  <el-icon><QuestionFilled /></el-icon>
                  获取帮助
                </el-button>
  </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { 
  Clock, Loading, Check, TrendCharts, List, 
  DataAnalysis, QuestionFilled 
} from "@element-plus/icons-vue";
import { useCool } from "/@/cool";

defineOptions({
  name: "sop-personal-workbench"
});

const router = useRouter();
const { service } = useCool();

// 当前用户信息 - 从系统获取
const currentUser = ref({
  id: null,
  name: "加载中..."
});

// 个人任务统计
const personalStats = ref({
  pendingTasks: 0,
  inProgressTasks: 0,
  completedTasks: 0,
  weeklyCompleted: 0,
  completionRate: 0
});

// 任务列表
const pendingTasks = ref([]);
const inProgressTasks = ref([]);
const completedTasks = ref([]);
const activeTab = ref("pending");

// 获取当前用户信息
const getCurrentUser = async () => {
  try {
    // 从用户状态或API获取当前用户信息
    const userInfo = await service.base.comm.person();
    if (userInfo) {
      currentUser.value = {
        id: userInfo.id,
        name: userInfo.name || userInfo.username
      };
    }
  } catch (error) {
    console.error("获取用户信息失败:", error);
    // 使用默认值
    currentUser.value = {
      id: 1,
      name: "当前用户"
    };
  }
};

// 加载个人任务数据
const loadPersonalTasks = async (businessStatus: string | null = null) => {
  try {
    if (!currentUser.value.id) {
      await getCurrentUser();
    }

    console.log("开始加载个人任务，用户ID:", currentUser.value.id, "业务状态:", businessStatus);

    // 构建查询参数
    const queryParams = {
      assigneeId: currentUser.value.id,
      page: 1,
      size: 100
    };

    // 如果指定了业务状态，添加到查询参数
    if (businessStatus) {
      queryParams.businessStatus = businessStatus;
    }

    // 使用AdminTaskInfoController的个人任务接口
    const personalTasksResponse = await service.task.info.request({
      url: '/personal-tasks',
      method: 'GET',
      params: queryParams
    });

    console.log("个人任务响应:", personalTasksResponse);

    // 处理个人任务响应结构
    let personalTasksList = [];
    if (Array.isArray(personalTasksResponse)) {
      personalTasksList = personalTasksResponse;
    } else if (personalTasksResponse && personalTasksResponse.data && Array.isArray(personalTasksResponse.data)) {
      personalTasksList = personalTasksResponse.data;
    } else if (personalTasksResponse && personalTasksResponse.list) {
      personalTasksList = personalTasksResponse.list;
    } else if (personalTasksResponse && personalTasksResponse.records) {
      personalTasksList = personalTasksResponse.records;
    }

    console.log("个人任务列表:", personalTasksList);
    console.log("个人任务数量:", personalTasksList ? personalTasksList.length : 0);

    if (!personalTasksList || personalTasksList.length === 0) {
      console.warn("没有找到任何个人任务");
      // 清空任务列表
      pendingTasks.value = [];
      inProgressTasks.value = [];
      completedTasks.value = [];
      return;
    }

    // 处理任务数据，从executions中获取执行记录
    const tasksWithExecution = personalTasksList.map(task => {
      // 从task.executions中找到当前用户的执行记录（优先使用executions字段）
      const allExecutions = task.executions || task.executionEntitys || [];
      const myExecution = allExecutions.find(exec => exec.assigneeId === currentUser.value.id);

      // 如果没有找到当前用户的执行记录，创建一个默认的
      const userExecution = myExecution || {
        id: null,
        taskId: task.id,
        assigneeId: currentUser.value.id,
        assigneeName: currentUser.value.name,
        executionStatus: 'ASSIGNED', // 默认状态
        assignmentType: 'MANUAL',
        acceptTime: null,
        completionTime: null,
        remark: ''
      };

      return {
        ...task,
        myExecution: userExecution, // 当前用户的执行记录
        executions: allExecutions, // 所有执行记录
        // 根据我的执行状态确定任务状态
        personalStatus: getPersonalTaskStatus(task, userExecution)
      };
    });

      console.log("合并后的任务数据:", tasksWithExecution);
      console.log("任务数量:", tasksWithExecution.length);
      console.log("第一个任务的状态:", tasksWithExecution[0]?.personalStatus);
      console.log("第一个任务的执行记录:", tasksWithExecution[0]?.myExecution);
      console.log("第一个任务的executions:", personalTasksList[0]?.executions);
      console.log("第一个任务的executionEntitys:", personalTasksList[0]?.executionEntitys);
      console.log("当前用户ID:", currentUser.value.id);
      console.log("第一个任务的所有执行人数量:", tasksWithExecution[0]?.executions?.length);

      // 如果没有指定业务状态，按个人执行状态分类任务
      if (!businessStatus) {
        pendingTasks.value = tasksWithExecution.filter(task => task.personalStatus === 'pending');
        inProgressTasks.value = tasksWithExecution.filter(task => task.personalStatus === 'inProgress');
        completedTasks.value = tasksWithExecution.filter(task => task.personalStatus === 'completed');

        // 更新个人任务统计
        await updatePersonalStats();
      } else {
        // 如果指定了业务状态，只更新对应的任务列表
        if (businessStatus === 'pending') {
          pendingTasks.value = tasksWithExecution;
        } else if (businessStatus === 'inProgress') {
          inProgressTasks.value = tasksWithExecution;
        } else if (businessStatus === 'completed') {
          completedTasks.value = tasksWithExecution;
        }
      }

      console.log("任务统计:", personalStats.value);
  } catch (error) {
    console.error("加载个人任务失败:", error);
    ElMessage.error("加载任务数据失败: " + (error.message || "未知错误"));
  }
};

// 更新个人任务统计（优化：减少重复调用）
const updatePersonalStats = async () => {
  console.log("开始更新个人任务统计...");
  console.log("当前用户ID:", currentUser.value.id);

  if (!currentUser.value.id) {
    console.error("用户ID为空，无法获取统计数据");
    return;
  }

  try {
    // 优化：如果已经有任务数据，直接从本地计算统计
    if (pendingTasks.value.length > 0 || inProgressTasks.value.length > 0 || completedTasks.value.length > 0) {
      console.log("使用本地数据计算统计...");
      personalStats.value.pendingTasks = pendingTasks.value.length;
      personalStats.value.inProgressTasks = inProgressTasks.value.length;
      personalStats.value.completedTasks = completedTasks.value.length;
    } else {
      console.log("本地无数据，调用接口获取统计...");
      // 分别获取各状态的任务数量
      const [pendingResponse, inProgressResponse, completedResponse] = await Promise.all([
        service.task.info.request({
          url: '/personal-tasks',
          method: 'GET',
          params: {
            assigneeId: currentUser.value.id,
            businessStatus: 'pending',
            page: 1,
            size: 1 // 只需要获取总数
          }
        }),
        service.task.info.request({
          url: '/personal-tasks',
          method: 'GET',
          params: {
            assigneeId: currentUser.value.id,
            businessStatus: 'inProgress',
            page: 1,
            size: 1
          }
        }),
        service.task.info.request({
          url: '/personal-tasks',
          method: 'GET',
          params: {
            assigneeId: currentUser.value.id,
            businessStatus: 'completed',
            page: 1,
            size: 1
          }
        })
      ]);

      console.log("统计接口返回数据:");
      console.log("pending:", pendingResponse);
      console.log("inProgress:", inProgressResponse);
      console.log("completed:", completedResponse);

      // 更新统计数据 - 从pagination对象中获取total
      personalStats.value.pendingTasks = pendingResponse.pagination?.total || 0;
      personalStats.value.inProgressTasks = inProgressResponse.pagination?.total || 0;
      personalStats.value.completedTasks = completedResponse.pagination?.total || 0;
    }

    // 计算完成率
    const totalTasks = personalStats.value.pendingTasks + personalStats.value.inProgressTasks + personalStats.value.completedTasks;
    personalStats.value.completionRate = totalTasks > 0
      ? Math.round((personalStats.value.completedTasks / totalTasks) * 100)
      : 0;

    // 计算本周完成数量（简化处理，使用已完成任务的一部分）
    personalStats.value.weeklyCompleted = Math.floor(personalStats.value.completedTasks * 0.3);

    console.log("更新后的个人统计:", personalStats.value);
  } catch (error) {
    console.error("更新个人统计失败:", error);
  }
};

// 根据任务和个人执行状态确定个人任务状态
const getPersonalTaskStatus = (task, myExecution) => {
  if (!myExecution) return 'pending';

  switch (myExecution.executionStatus) {
    case 'ASSIGNED':
      return 'pending';
    case 'IN_PROGRESS':
    case 'ACCEPTED':
      return 'inProgress';
    case 'COMPLETED':
      return 'completed';
    default:
      return 'pending';
  }
};

// 计算任务执行统计信息
const calculateTaskStats = (executions) => {
  if (!executions || executions.length === 0) {
    return {
      totalExecutors: 0,
      completedExecutors: 0,
      inProgressExecutors: 0,
      completionRate: 0,
      actualStartTime: null,
      actualEndTime: null
    };
  }

  const totalExecutors = executions.length;
  const completedExecutors = executions.filter(e => e.executionStatus === 'COMPLETED').length;
  const inProgressExecutors = executions.filter(e => e.executionStatus === 'IN_PROGRESS').length;
  const completionRate = totalExecutors > 0 ? Math.round((completedExecutors / totalExecutors) * 100) : 0;

  // 获取最早开始时间和最晚完成时间
  const acceptTimes = executions.map(e => e.acceptTime).filter(t => t);
  const completionTimes = executions.map(e => e.completionTime).filter(t => t);

  const actualStartTime = acceptTimes.length > 0 ? new Date(Math.min(...acceptTimes.map(t => new Date(t).getTime()))) : null;
  const actualEndTime = completionTimes.length > 0 ? new Date(Math.max(...completionTimes.map(t => new Date(t).getTime()))) : null;

  return {
    totalExecutors,
    completedExecutors,
    inProgressExecutors,
    completionRate,
    actualStartTime,
    actualEndTime
  };
};

// 加载指定TAB的数据（懒加载）
const loadTabData = async (tabName: string) => {
  console.log(`加载${tabName}TAB数据...`);

  // 检查是否已经加载过该TAB的数据
  const currentTabTasks = getTasksByTab(tabName);
  if (currentTabTasks.length > 0) {
    console.log(`${tabName}TAB数据已存在，跳过加载`);
    return;
  }

  // 加载该TAB的任务数据
  await loadPersonalTasks(tabName);

  // 如果是第一次加载，同时更新统计数据
  if (personalStats.value.pendingTasks === 0 &&
      personalStats.value.inProgressTasks === 0 &&
      personalStats.value.completedTasks === 0) {
    console.log("首次加载，更新统计数据...");
    await updatePersonalStats();
  }
};

// 根据TAB名称获取对应的任务列表
const getTasksByTab = (tabName: string) => {
  switch (tabName) {
    case 'pending':
      return pendingTasks.value;
    case 'inProgress':
      return inProgressTasks.value;
    case 'completed':
      return completedTasks.value;
    default:
      return [];
  }
};

// 处理标签切换（懒加载）
const handleTabChange = async (tabName: string | number) => {
  const tabNameStr = String(tabName);
  activeTab.value = tabNameStr;
  console.log("切换到Tab:", tabNameStr);

  // 懒加载：只有当前TAB没有数据时才加载
  await loadTabData(tabNameStr);
};

// 处理任务完成
const handleCompleteTask = async (task: any) => {
  try {
    if (!task.myExecution) {
      ElMessage.error("未找到您的任务执行记录");
      return;
    }

    // 设置加载状态
    task.completing = true;

    // 构建完成任务请求数据
    const completionRequest = {
      taskId: task.id,
      assigneeId: currentUser.value.id,
      completionNote: '任务完成',
      attachments: [],
      photos: []
    };

    // 调用AdminTaskStatusController的完成任务接口
    const response = await service.request({
      url: '/admin/task/status/task/execution/complete',
      method: 'POST',
      data: completionRequest
    });

    if (response && response.code === 1000) {
      ElMessage.success('任务已完成');

      // 更新任务状态
      task.personalStatus = 'completed';
      if (task.myExecution) {
        task.myExecution.executionStatus = 'COMPLETED';
        task.myExecution.completionTime = Date.now();
      }

      // 重新加载任务列表
      await loadPersonalTasks();
    } else {
      ElMessage.error(response?.message || '完成任务失败');
    }
  } catch (error: any) {
    console.error("完成任务失败:", error);
    ElMessage.error("完成任务失败: " + (error.message || "未知错误"));
  } finally {
    // 清除加载状态
    task.completing = false;
  }
};

// 处理查看任务详情
const handleViewTask = (task: any) => {
  router.push(`/task/info?id=${task.id}`);
};

// 快捷操作方法
const goToMyTasks = () => {
  router.push("/task/info");
};

const goToReports = () => {
  ElMessage.info("工作报告功能开发中...");
};

const goToHelp = () => {
  ElMessage.info("帮助中心功能开发中...");
};

// 格式化日期
const formatDate = (date: string | Date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化日期时间（更详细）
const formatDateTime = (date: string | Date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取执行状态标签
const getExecutionStatusLabel = (status: string) => {
  const statusMap = {
    'ASSIGNED': '已分配',
    'ACCEPTED': '已接受',
    'IN_PROGRESS': '执行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消',
    'REJECTED': '已拒绝'
  };
  return statusMap[status] || status;
};

// 获取执行状态类型
const getExecutionStatusType = (status: string) => {
  const typeMap = {
    'ASSIGNED': 'info',
    'ACCEPTED': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger',
    'REJECTED': 'danger'
  };
  return typeMap[status] || 'info';
};

// 获取协同人样式类
const getCollaboratorClass = (execution: any) => {
  const isMe = execution.assigneeId === currentUser.value.id;
  return {
    'is-me': isMe,
    'is-completed': execution.executionStatus === 'COMPLETED'
  };
};

onMounted(async () => {
  console.log("个人工作台页面开始加载...");

  try {
    console.log("1. 获取当前用户信息...");
    await getCurrentUser();
    console.log("2. 加载默认TAB数据和统计...");
    // 只加载默认TAB（pending）的数据，同时获取统计信息
    await loadTabData('pending');
    console.log("个人工作台页面加载完成！");
  } catch (error) {
    console.error("个人工作台页面加载失败:", error);
  }
});
</script>

<style scoped>
.personal-workbench-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 60px);
}

.section-header {
  margin-bottom: 24px;
  text-align: center;
}

.section-header h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-header p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 140px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card-1::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card-2::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card-3::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.stat-card-4::before {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.stat-icon {
  font-size: 36px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(var(--el-color-primary-rgb), 0.1) 0%, rgba(var(--el-color-primary-rgb), 0.05) 100%);
}

.stat-content {
  flex: 1;
  margin-left: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-color-success);
  font-size: 14px;
  font-weight: 600;
}

/* 欢迎区域 */
.welcome-section-mini {
  margin-bottom: 32px;
}

.welcome-content-mini {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-dark-2) 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 4px 20px rgba(var(--el-color-primary-rgb), 0.2);
  transition: all 0.3s ease;
}

.welcome-content-mini:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(var(--el-color-primary-rgb), 0.3);
}

.welcome-icon {
  font-size: 32px;
  animation: wave 2s infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-20deg); }
}

.welcome-text h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* 任务列表 */
.my-tasks-section {
  margin-bottom: 32px;
}

.task-filter-tabs {
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--el-border-color-light);
}

.task-list {
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.task-item {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary);
}

.task-item.completed {
  opacity: 0.8;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.task-title-info {
  flex: 1;
}

.task-title-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.task-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.execution-time-info {
  margin-top: 12px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  font-size: 12px;
}

.time-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 4px;
}

.time-row:last-child {
  margin-bottom: 0;
}

.time-item {
  display: flex;
  align-items: center;
  min-width: 0;
}

.time-label {
  color: #666;
  margin-right: 4px;
  white-space: nowrap;
}

.time-value {
  color: #333;
  font-weight: 500;
}

.scenario-info {
  font-size: 13px;
  color: var(--el-color-primary);
  background: rgba(var(--el-color-primary-rgb), 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

.completion-time {
  font-size: 12px;
  color: var(--el-color-success);
  font-weight: 500;
}

.completion-time-small {
  font-size: 11px;
  color: var(--el-color-info);
  margin-left: 8px;
}

/* 协同人信息样式 */
.collaborators-info {
  margin-top: 8px;
  padding: 8px 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.collaborators-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-weight: 500;
  margin-bottom: 6px;
  display: block;
}

.collaborators-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.collaborator-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: var(--el-bg-color);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);
  font-size: 12px;
  transition: all 0.3s ease;
}

.collaborator-item.is-me {
  background: linear-gradient(135deg, rgba(var(--el-color-primary-rgb), 0.1) 0%, rgba(var(--el-color-primary-rgb), 0.05) 100%);
  border-color: var(--el-color-primary);
  font-weight: 600;
}

.collaborator-item.is-completed {
  background: linear-gradient(135deg, rgba(var(--el-color-success-rgb), 0.1) 0%, rgba(var(--el-color-success-rgb), 0.05) 100%);
  border-color: var(--el-color-success);
}

.collaborator-item .status-tag {
  margin-left: 4px;
}

.task-details {
  margin-bottom: 16px;
}

.task-desc {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  font-size: 14px;
  padding: 12px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  border-left: 3px solid var(--el-color-primary);
}

.task-core-info {
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  padding: 16px;
}

.info-row {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.info-item .value {
  font-size: 13px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

/* 快捷操作 */
.quick-actions {
  margin-bottom: 32px;
}

.action-card {
  background: var(--el-bg-color);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
  height: 280px;
  position: relative;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--el-border-color-light);
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.action-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 32px 24px;
  gap: 16px;
}

.action-icon {
  font-size: 48px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.action-card:hover .action-icon {
  transform: scale(1.1) rotate(5deg);
}

.action-content h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.6;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.action-btn {
  margin-top: auto;
  width: 100%;
  height: 44px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.personal-card {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.personal-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.personal-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.report-card {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.report-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.report-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.help-card {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
}

.help-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.help-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .personal-workbench-page {
    padding: 16px;
  }

  .action-card {
    height: 240px;
  }

  .action-content {
    padding: 24px 20px;
    gap: 12px;
  }

  .action-icon {
    font-size: 40px;
  }

  .stat-card {
    height: 120px;
    padding: 20px;
  }

  .stat-icon {
    font-size: 28px;
    width: 50px;
    height: 50px;
  }

  .stat-number {
    font-size: 24px;
  }

  .info-row {
    flex-direction: column;
    gap: 12px;
  }

  .task-meta {
    gap: 6px;
  }

  .scenario-info {
    font-size: 12px;
  }

  .collaborators-list {
  flex-direction: column;
    gap: 6px;
  }

  .collaborator-item {
    font-size: 11px;
    padding: 3px 6px;
  }

  .completion-time-small {
    font-size: 10px;
  }
}

/* 暗黑模式适配 */
html.dark {
  .stat-card {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .stat-card:hover {
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.4);
  }

  .task-filter-tabs {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .task-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  .action-card {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  }

  .action-card:hover {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
  }

  .welcome-content-mini {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }

  .welcome-content-mini:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  }
}
</style> 

package com.cool.modules.organization.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.service.DualDimensionDataPermissionService;
import com.cool.modules.organization.service.OrganizationModeService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 双维度数据权限服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DualDimensionDataPermissionServiceImpl implements DualDimensionDataPermissionService {
    
    private final OrganizationModeService organizationModeService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String CACHE_KEY_PREFIX = "data:permission:";
    private static final int CACHE_EXPIRE_MINUTES = 10;
    
    @Override
    public List<Long> getAccessibleDataIds(Long userId, String dataType) {
        if (userId == null || dataType == null) {
            return new ArrayList<>();
        }
        
        DataPermissionScopeDTO scope = getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            // 系统管理员返回空列表，表示无限制
            return new ArrayList<>();
        }
        
        // 根据当前组织模式返回可访问的组织ID列表
        return scope.getCurrentModeOrganizationIds() != null ? 
               scope.getCurrentModeOrganizationIds() : new ArrayList<>();
    }
    
    @Override
    public void applyDataPermissionFilter(QueryWrapper queryWrapper, Long userId, String entityType) {
        if (queryWrapper == null || userId == null || entityType == null) {
            return;
        }
        
        DataPermissionScopeDTO scope = getUserPermissionScope(userId);
        
        // 系统管理员无需过滤
        if (scope.getIsUnlimited()) {
            log.debug("系统管理员跳过数据权限过滤: entityType={}", entityType);
            return;
        }
        
        if (scope.isDepartmentMode()) {
            applyDepartmentFilter(queryWrapper, scope.getDepartmentIds(), entityType);
        } else if (scope.isProjectMode()) {
            applyProjectFilter(queryWrapper, scope.getProjectIds(), entityType);
        }
        
        log.debug("已应用数据权限过滤: entityType={}, mode={}, userId={}", 
                 entityType, scope.getOrganizationMode(), userId);
    }
    
    private void applyDepartmentFilter(QueryWrapper queryWrapper, List<Long> departmentIds, String entityType) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            // 无部门权限，返回空结果
            queryWrapper.and("1 = 0");
            log.debug("用户无部门权限，应用空结果过滤: entityType={}", entityType);
            return;
        }
        
        switch (entityType) {
            case "TaskPackage":
                queryWrapper.in("department_id", departmentIds);
                break;
            case "TaskInfo":
                queryWrapper.in("department_id", departmentIds);
                break;
            case "TaskExecution":
                queryWrapper.in("department_id", departmentIds);
                break;
            case "WorkOrder":
                queryWrapper.in("applicant_dept_id", departmentIds);
                break;
            default:
                log.warn("未知的实体类型，无法应用部门权限过滤: {}", entityType);
        }
    }
    
    private void applyProjectFilter(QueryWrapper queryWrapper, List<Long> projectIds, String entityType) {
        if (projectIds == null || projectIds.isEmpty()) {
            // 无项目权限，返回空结果
            queryWrapper.and("1 = 0");
            log.debug("用户无项目权限，应用空结果过滤: entityType={}", entityType);
            return;
        }
        
        switch (entityType) {
            case "TaskPackage":
                queryWrapper.in("project_id", projectIds);
                break;
            case "TaskInfo":
                queryWrapper.in("project_id", projectIds);
                break;
            case "TaskExecution":
                queryWrapper.in("project_id", projectIds);
                break;
            case "WorkOrder":
                queryWrapper.in("project_id", projectIds);
                break;
            default:
                log.warn("未知的实体类型，无法应用项目权限过滤: {}", entityType);
        }
    }
    
    @Override
    public boolean hasDataAccess(Long userId, String entityType, Long entityId) {
        if (userId == null || entityType == null || entityId == null) {
            return false;
        }
        
        DataPermissionScopeDTO scope = getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            return true;
        }
        
        // 根据实体类型和ID查询数据归属
        if (scope.isDepartmentMode()) {
            Long departmentId = getEntityDepartmentId(entityType, entityId);
            return departmentId != null && scope.hasDepartmentPermission(departmentId);
        } else if (scope.isProjectMode()) {
            Long projectId = getEntityProjectId(entityType, entityId);
            return projectId != null && scope.hasProjectPermission(projectId);
        }
        
        return false;
    }
    
    @Override
    public Map<Long, Boolean> batchCheckDataAccess(Long userId, String entityType, List<Long> entityIds) {
        Map<Long, Boolean> result = new HashMap<>();
        
        if (CollUtil.isEmpty(entityIds)) {
            return result;
        }
        
        DataPermissionScopeDTO scope = getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            entityIds.forEach(id -> result.put(id, true));
            return result;
        }
        
        // 批量查询实体的归属信息
        Map<Long, Long> entityOrganizationMap = batchGetEntityOrganizationIds(
            entityType, entityIds, scope.getOrganizationMode());
        
        // 批量检查权限
        List<Long> allowedOrganizationIds = scope.getCurrentModeOrganizationIds();
        if (allowedOrganizationIds == null) {
            allowedOrganizationIds = new ArrayList<>();
        }
        
        for (Long entityId : entityIds) {
            Long organizationId = entityOrganizationMap.get(entityId);
            boolean hasAccess = organizationId != null && allowedOrganizationIds.contains(organizationId);
            result.put(entityId, hasAccess);
        }
        
        return result;
    }
    
    @Override
    public DataPermissionScopeDTO getUserPermissionScope(Long userId) {
        return organizationModeService.getUserPermissionScope(userId);
    }
    
    @Override
    public void refreshUserPermissionCache(Long userId) {
        organizationModeService.refreshUserPermissionCache(userId);
        
        // 清理数据权限相关缓存
        if (userId != null) {
            String pattern = CACHE_KEY_PREFIX + userId + ":*";
            redisTemplate.delete(redisTemplate.keys(pattern));
        }
    }
    
    @Override
    public void clearAllPermissionCache() {
        organizationModeService.clearAllPermissionCache();
        
        // 清理数据权限相关缓存
        String pattern = CACHE_KEY_PREFIX + "*";
        redisTemplate.delete(redisTemplate.keys(pattern));
    }
    
    @Override
    public boolean isSystemAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        
        String currentUser = CoolSecurityUtil.getAdminUsername();
        return "admin".equals(currentUser);
    }
    
    @Override
    public Long getEntityDepartmentId(String entityType, Long entityId) {
        // TODO: 根据实体类型查询对应的部门ID
        // 这里需要根据具体的业务实体实现
        return null;
    }
    
    @Override
    public Long getEntityProjectId(String entityType, Long entityId) {
        // TODO: 根据实体类型查询对应的项目ID
        // 这里需要根据具体的业务实体实现
        return null;
    }
    
    @Override
    public Map<Long, Long> batchGetEntityOrganizationIds(String entityType, List<Long> entityIds, String organizationMode) {
        Map<Long, Long> result = new HashMap<>();
        
        if (CollUtil.isEmpty(entityIds)) {
            return result;
        }
        
        // TODO: 根据实体类型和组织模式批量查询归属信息
        // 这里需要根据具体的业务实体实现
        
        return result;
    }
    
    @Override
    public void setEntityOrganizationInfo(Object entity, Long userId) {
        if (entity == null || userId == null) {
            return;
        }
        
        DataPermissionScopeDTO scope = getUserPermissionScope(userId);
        
        if (scope.isDepartmentMode()) {
            // 部门模式：设置部门信息
            setEntityDepartmentInfo(entity, userId, scope);
        } else if (scope.isProjectMode()) {
            // 项目模式：设置项目信息
            setEntityProjectInfo(entity, userId, scope);
        }
    }
    
    private void setEntityDepartmentInfo(Object entity, Long userId, DataPermissionScopeDTO scope) {
        try {
            // 检查实体是否有departmentId字段
            if (ReflectUtil.hasField(entity.getClass(), "departmentId")) {
                Long departmentId = (Long) ReflectUtil.getFieldValue(entity, "departmentId");
                if (departmentId == null && scope.getDepartmentIds() != null && !scope.getDepartmentIds().isEmpty()) {
                    // 设置用户的默认部门ID
                    ReflectUtil.setFieldValue(entity, "departmentId", scope.getDepartmentIds().get(0));
                }
            }
            
            // 清空项目ID字段
            if (ReflectUtil.hasField(entity.getClass(), "projectId")) {
                ReflectUtil.setFieldValue(entity, "projectId", null);
            }
            
        } catch (Exception e) {
            log.warn("设置实体部门信息失败", e);
        }
    }
    
    private void setEntityProjectInfo(Object entity, Long userId, DataPermissionScopeDTO scope) {
        try {
            // 检查实体是否有projectId字段
            if (ReflectUtil.hasField(entity.getClass(), "projectId")) {
                Long projectId = (Long) ReflectUtil.getFieldValue(entity, "projectId");
                if (projectId == null) {
                    throw new RuntimeException("项目模式下创建数据必须指定项目ID");
                }
                
                // 验证用户是否有该项目的权限
                if (scope.getProjectIds() == null || !scope.getProjectIds().contains(projectId)) {
                    throw new RuntimeException("用户无权限在该项目下创建数据");
                }
            }
            
            // 清空部门ID字段
            if (ReflectUtil.hasField(entity.getClass(), "departmentId")) {
                ReflectUtil.setFieldValue(entity, "departmentId", null);
            }
            
        } catch (Exception e) {
            log.warn("设置实体项目信息失败", e);
            throw new RuntimeException("设置实体项目信息失败: " + e.getMessage());
        }
    }
}

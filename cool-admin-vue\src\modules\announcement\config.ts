import { ModuleConfig } from "/@/cool";

export default (): ModuleConfig => {
  return {
    enable: true,
    label: "业主公示",
    description: "业主公示管理，支持公示内容的增删改查和富文本展示",
    author: "Cool Team",
    version: "1.0.0",
    updateTime: "2024-06-01",
    order: 80,
    views: [
      {
        path: "/announcement",
        name: "announcement",
        meta: {
          label: "业主公示",
          icon: "el-icon-document",
          keepAlive: true
        },
        component: () => import("./views/announcement.vue")
      }
    ]
  };
}; 
package com.cool.modules.organization.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class UserCurrentModeEntityTableDef extends TableDef {

    /**
     * 用户当前组织模式实体
 
 <AUTHOR> Admin
 @since 2025-01-17
     */
    public static final UserCurrentModeEntityTableDef USER_CURRENT_MODE_ENTITY = new UserCurrentModeEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 备注
     */
    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    /**
     * 用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 当前组织模式
     */
    public final QueryColumn CURRENT_MODE = new QueryColumn(this, "current_mode");

    /**
     * 切换次数
     */
    public final QueryColumn SWITCH_COUNT = new QueryColumn(this, "switch_count");

    /**
     * 上次切换时间
     */
    public final QueryColumn LAST_SWITCH_TIME = new QueryColumn(this, "last_switch_time");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, REMARK, USER_ID, CREATE_TIME, UPDATE_TIME, CURRENT_MODE, SWITCH_COUNT, LAST_SWITCH_TIME};

    public UserCurrentModeEntityTableDef() {
        super("", "org_user_current_mode");
    }

    private UserCurrentModeEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public UserCurrentModeEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new UserCurrentModeEntityTableDef("", "org_user_current_mode", alias));
    }

}

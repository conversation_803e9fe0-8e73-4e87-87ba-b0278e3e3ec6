package com.cool.modules.dify.dto;

/**
 * Dify Workflow 响应模式枚举
 */
public enum DifyWorkflowResponseModeEnum {
    BLOCKING("blocking"),
    STREAMING("streaming");

    private final String value;

    DifyWorkflowResponseModeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }

    public static DifyWorkflowResponseModeEnum fromValue(String value) {
        for (DifyWorkflowResponseModeEnum mode : values()) {
            if (mode.value.equalsIgnoreCase(value)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("Unknown responseMode: " + value);
    }
} 
package com.cool.modules.task.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class TaskHistoryEntityTableDef extends TableDef {

    /**
     * 任务历史记录实体
     */
    public static final TaskHistoryEntityTableDef TASK_HISTORY_ENTITY = new TaskHistoryEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn NOTE = new QueryColumn(this, "note");

    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    public final QueryColumn ACTION_BY = new QueryColumn(this, "action_by");

    public final QueryColumn EXTRA_DATA = new QueryColumn(this, "extra_data");

    public final QueryColumn NEW_STATUS = new QueryColumn(this, "new_status");

    public final QueryColumn OLD_STATUS = new QueryColumn(this, "old_status");

    public final QueryColumn ACTION_TIME = new QueryColumn(this, "action_time");

    public final QueryColumn ACTION_TYPE = new QueryColumn(this, "action_type");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn ACTION_BY_NAME = new QueryColumn(this, "action_by_name");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, NOTE, TASK_ID, ACTION_BY, EXTRA_DATA, NEW_STATUS, OLD_STATUS, ACTION_TIME, ACTION_TYPE, CREATE_TIME, UPDATE_TIME, ACTION_BY_NAME};

    public TaskHistoryEntityTableDef() {
        super("", "task_history");
    }

    private TaskHistoryEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public TaskHistoryEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new TaskHistoryEntityTableDef("", "task_history", alias));
    }

}

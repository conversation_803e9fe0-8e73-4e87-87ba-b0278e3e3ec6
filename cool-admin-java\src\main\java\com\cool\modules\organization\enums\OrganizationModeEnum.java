package com.cool.modules.organization.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 组织形态枚举
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@AllArgsConstructor
public enum OrganizationModeEnum {
    
    /**
     * 部门维度
     */
    DEPARTMENT("DEPARTMENT", "部门维度", "基于部门层级的组织管理模式"),
    
    /**
     * 项目维度
     */
    PROJECT("PROJECT", "项目维度", "基于项目的组织管理模式");
    
    /**
     * 模式代码
     */
    private final String code;
    
    /**
     * 模式名称
     */
    private final String name;
    
    /**
     * 模式描述
     */
    private final String description;
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 模式代码
     * @return 组织形态枚举
     */
    public static OrganizationModeEnum getByCode(String code) {
        for (OrganizationModeEnum mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        return DEPARTMENT; // 默认返回部门维度
    }
    
    /**
     * 检查是否为有效的组织形态代码
     * 
     * @param code 模式代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        for (OrganizationModeEnum mode : values()) {
            if (mode.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}

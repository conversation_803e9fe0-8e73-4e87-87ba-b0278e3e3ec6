package com.cool.modules.task.service;

import com.cool.modules.task.dto.TaskCompletionRequest;
import com.cool.modules.task.dto.TaskCloseRequest;
import java.util.List;

/**
 * 任务状态管理服务接口
 */
public interface TaskStatusService {

    /**
     * 完成任务执行
     * @param request 完成请求
     * @return 是否成功
     */
    Boolean completeTaskExecution(TaskCompletionRequest request);

    /**
     * 强制完成任务
     * @param taskId 任务ID
     * @param reason 强制完成原因
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    Boolean forceCompleteTask(Long taskId, String reason, Long operatorId);

    /**
     * 关闭任务
     * @param request 关闭请求
     * @return 是否成功
     */
    Boolean closeTask(TaskCloseRequest request);

    /**
     * 重新开启任务
     * @param taskId 任务ID
     * @param reason 重新开启原因
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    Boolean reopenTask(Long taskId, String reason, Long operatorId);

    /**
     * 检查任务是否可以完成
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @return 是否可以完成
     */
    Boolean canCompleteTask(Long taskId, Long assigneeId);

    /**
     * 检查任务是否可以关闭
     * @param taskId 任务ID
     * @param operatorId 操作人ID
     * @return 是否可以关闭
     */
    Boolean canCloseTask(Long taskId, Long operatorId);

    /**
     * 检查所有执行人是否都已完成
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    Boolean areAllExecutionsCompleted(Long taskId);

    /**
     * 批量强制完成任务
     * @param taskIds 任务ID列表
     * @param reason 操作原因
     * @param operatorId 操作人ID
     * @return 成功处理的任务数量
     */
    Integer batchForceCompleteTask(List<Long> taskIds, String reason, Long operatorId);

    /**
     * 批量关闭任务
     * @param taskIds 任务ID列表
     * @param reason 操作原因
     * @param operatorId 操作人ID
     * @return 成功处理的任务数量
     */
    Integer batchCloseTask(List<Long> taskIds, String reason, Long operatorId);

    /**
     * 批量重新开启任务
     * @param taskIds 任务ID列表
     * @param reason 操作原因
     * @param operatorId 操作人ID
     * @return 成功处理的任务数量
     */
    Integer batchReopenTask(List<Long> taskIds, String reason, Long operatorId);
}

package com.cool.modules.dify.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class DifyWorkflowEntityTableDef extends TableDef {

    /**
     * Dify工作流实体
     */
    public static final DifyWorkflowEntityTableDef DIFY_WORKFLOW_ENTITY = new DifyWorkflowEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn URL = new QueryColumn(this, "url");

    public final QueryColumn NAME = new QueryColumn(this, "name");

    public final QueryColumn API_KEY = new QueryColumn(this, "api_key");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn OUTPUT_DESC = new QueryColumn(this, "output_desc");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    public final QueryColumn INPUT_PARAMS = new QueryColumn(this, "input_params");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, URL, NAME, API_KEY, STATUS, CREATE_TIME, OUTPUT_DESC, UPDATE_TIME, DESCRIPTION, INPUT_PARAMS};

    public DifyWorkflowEntityTableDef() {
        super("", "dify_workflow");
    }

    private DifyWorkflowEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public DifyWorkflowEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new DifyWorkflowEntityTableDef("", "dify_workflow", alias));
    }

}

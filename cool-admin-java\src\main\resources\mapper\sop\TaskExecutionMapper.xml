<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.sop.mapper.TaskExecutionMapper">

    <!-- 根据任务ID查询执行人记录 -->
    <select id="selectByTaskId" resultType="com.cool.modules.task.entity.TaskExecutionEntity">
        SELECT * FROM task_execution 
        WHERE task_id = #{taskId} 
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID查询执行记录 -->
    <select id="selectByAssigneeId" resultType="com.cool.modules.task.entity.TaskExecutionEntity">
        SELECT * FROM task_execution 
        WHERE assignee_id = #{assigneeId} 
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据任务ID删除执行记录 -->
    <delete id="deleteByTaskId">
        UPDATE task_execution 
        SET is_deleted = 1, update_time = NOW() 
        WHERE task_id = #{taskId}
    </delete>

    <!-- 查询用户当前任务数量 -->
    <select id="countCurrentTasksByUserId" resultType="int">
        SELECT COUNT(*) FROM task_execution 
        WHERE assignee_id = #{assigneeId} 
        AND execution_status IN ('ASSIGNED', 'IN_PROGRESS')
        AND is_deleted = 0
    </select>

    <!-- 查询用户历史绩效评分 -->
    <select id="selectUserPerformanceScore" resultType="double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE (
                    SUM(CASE WHEN execution_status = 'COMPLETED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)
                )
            END as performance_score
        FROM task_execution 
        WHERE assignee_id = #{assigneeId} 
        AND is_deleted = 0
    </select>

    <!-- 查询待分配的任务列表 -->
    <select id="selectUnassignedTaskIds" resultType="long">
        SELECT DISTINCT ti.id 
        FROM task_info ti 
        LEFT JOIN task_execution te ON ti.id = te.task_id AND te.is_deleted = 0
        WHERE ti.is_deleted = 0 
        AND ti.task_status = 0 
        AND te.id IS NULL
        ORDER BY ti.create_time ASC
    </select>

    <!-- 检查任务是否已分配给指定用户 -->
    <select id="existsByTaskIdAndAssigneeId" resultType="boolean">
        SELECT COUNT(*) > 0 FROM task_execution 
        WHERE task_id = #{taskId} 
        AND assignee_id = #{assigneeId} 
        AND is_deleted = 0
    </select>

</mapper> 
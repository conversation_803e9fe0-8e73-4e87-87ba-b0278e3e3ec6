package com.cool.modules.task.dto;

import lombok.Data;
import java.util.List;

/**
 * 批量任务操作请求
 */
@Data
public class BatchTaskOperationRequest {
    
    /**
     * 任务ID列表
     */
    private List<Long> taskIds;
    
    /**
     * 操作原因
     */
    private String reason;
    
    /**
     * 操作人ID
     */
    private Long operatorId;
    
    /**
     * 操作类型：COMPLETE, CLOSE, REOPEN
     */
    private String operationType;
}

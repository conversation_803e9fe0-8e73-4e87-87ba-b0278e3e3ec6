<template>
  <div class="project-task">
    <!-- 任务看板视图 -->
    <div v-if="viewMode === 'kanban'" class="kanban-view">
      <div class="kanban-header">
        <div class="view-controls">
          <el-button-group>
            <el-button 
              :type="viewMode === 'kanban' ? 'primary' : ''"
              @click="viewMode = 'kanban'"
            >
              <el-icon><Grid /></el-icon>
              看板
            </el-button>
            <el-button 
              :type="viewMode === 'list' ? 'primary' : ''"
              @click="viewMode = 'list'"
            >
              <el-icon><List /></el-icon>
              列表
            </el-button>
          </el-button-group>
        </div>
        
        <div class="task-actions">
          <el-button type="primary" @click="showCreateTaskDialog = true">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
        </div>
      </div>

      <div class="kanban-board">
        <div 
          v-for="status in taskStatuses" 
          :key="status.value"
          class="kanban-column"
        >
          <div class="column-header">
            <div class="column-title">
              <el-tag :type="status.type" size="small">
                {{ status.label }}
              </el-tag>
              <span class="task-count">{{ getTasksByStatus(status.value).length }}</span>
            </div>
            <el-dropdown @command="handleColumnAction">
              <el-icon class="column-menu"><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`add-${status.value}`">
                    添加任务
                  </el-dropdown-item>
                  <el-dropdown-item :command="`clear-${status.value}`">
                    清空列表
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="column-content">
            <draggable
              v-model="tasksByStatus[status.value]"
              group="tasks"
              @change="handleTaskMove"
              item-key="id"
              class="task-list"
            >
              <template #item="{ element: task }">
                <div class="task-card" @click="openTaskDetail(task)">
                  <div class="task-header">
                    <div class="task-title">{{ task.title }}</div>
                    <el-dropdown @command="handleTaskAction">
                      <el-icon class="task-menu"><MoreFilled /></el-icon>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="`edit-${task.id}`">
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item :command="`delete-${task.id}`">
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                  
                  <div class="task-description">
                    {{ task.description }}
                  </div>
                  
                  <div class="task-meta">
                    <div class="task-priority">
                      <el-tag 
                        :type="getPriorityType(task.priority)" 
                        size="small"
                      >
                        {{ getPriorityText(task.priority) }}
                      </el-tag>
                    </div>
                    
                    <div class="task-assignee">
                      <el-avatar 
                        v-if="task.assignee" 
                        :size="24" 
                        :src="task.assignee.avatar"
                      />
                    </div>
                  </div>
                  
                  <div class="task-footer">
                    <div class="task-due-date">
                      <el-icon><Calendar /></el-icon>
                      {{ formatDate(task.dueDate) }}
                    </div>
                    
                    <div class="task-progress">
                      <el-progress 
                        :percentage="task.progress" 
                        :show-text="false"
                        :stroke-width="4"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-else class="list-view">
      <cl-crud ref="Crud">
        <cl-row>
          <div class="view-controls">
            <el-button-group>
              <el-button 
                :type="viewMode === 'kanban' ? 'primary' : ''"
                @click="viewMode = 'kanban'"
              >
                <el-icon><Grid /></el-icon>
                看板
              </el-button>
              <el-button 
                :type="viewMode === 'list' ? 'primary' : ''"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
                列表
              </el-button>
            </el-button-group>
          </div>
          
          <cl-refresh-btn />
          <cl-add-btn @click="showCreateTaskDialog = true">新建任务</cl-add-btn>
          <cl-multi-delete-btn />
          <cl-flex1 />
          <cl-search ref="Search" />
        </cl-row>

        <cl-row>
          <cl-table ref="Table" />
        </cl-row>

        <cl-row>
          <cl-flex1 />
          <cl-pagination />
        </cl-row>
      </cl-crud>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateTaskDialog"
      title="创建任务"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form ref="createTaskFormRef" :model="createTaskForm" label-width="100px">
        <el-form-item label="任务标题" prop="title" required>
          <el-input v-model="createTaskForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input 
            v-model="createTaskForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="任务优先级" prop="priority">
          <el-select v-model="createTaskForm.priority" style="width: 100%">
            <el-option label="低" :value="1" />
            <el-option label="普通" :value="2" />
            <el-option label="中等" :value="3" />
            <el-option label="高" :value="4" />
            <el-option label="紧急" :value="5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截止日期" prop="dueDate">
          <el-date-picker
            v-model="createTaskForm.dueDate"
            type="datetime"
            placeholder="选择截止日期"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="指派给" prop="assigneeId">
          <el-select 
            v-model="createTaskForm.assigneeId" 
            filterable 
            remote
            :remote-method="searchUsers"
            :loading="userSearchLoading"
            placeholder="搜索并选择用户"
            style="width: 100%"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="user.nickName"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateTaskDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            :loading="createTaskLoading"
            @click="handleCreateTask"
          >
            创建
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <task-detail-dialog
      v-model="showTaskDetailDialog"
      :task="selectedTask"
      @updated="refreshTasks"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useCrud, useTable, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage } from 'element-plus';
import { 
  Grid, 
  List, 
  Plus, 
  MoreFilled, 
  Calendar 
} from '@element-plus/icons-vue';
import draggable from 'vuedraggable';
import TaskDetailDialog from '../components/task-detail-dialog.vue';

defineOptions({
  name: "project-task",
});

const { service } = useCool();

// 响应式数据
const viewMode = ref<'kanban' | 'list'>('kanban');
const showCreateTaskDialog = ref(false);
const showTaskDetailDialog = ref(false);
const createTaskLoading = ref(false);
const userSearchLoading = ref(false);
const selectedTask = ref(null);
const userOptions = ref([]);

// 任务状态配置
const taskStatuses = [
  { value: 1, label: '待开始', type: 'info' },
  { value: 2, label: '进行中', type: 'warning' },
  { value: 3, label: '待审核', type: 'primary' },
  { value: 4, label: '已完成', type: 'success' },
  { value: 5, label: '已取消', type: 'danger' }
];

// 任务数据
const tasks = ref([]);

// 创建任务表单
const createTaskForm = reactive({
  title: '',
  description: '',
  priority: 2,
  dueDate: '',
  assigneeId: null
});

// 计算属性
const tasksByStatus = computed(() => {
  const result = {};
  taskStatuses.forEach(status => {
    result[status.value] = tasks.value.filter(task => task.status === status.value);
  });
  return result;
});

// 方法
const getTasksByStatus = (status: number) => {
  return tasks.value.filter(task => task.status === status);
};

const getPriorityType = (priority: number) => {
  const types = ['', 'info', '', 'warning', 'danger', 'danger'];
  return types[priority] || '';
};

const getPriorityText = (priority: number) => {
  const texts = ['', '低', '普通', '中等', '高', '紧急'];
  return texts[priority] || '普通';
};

const formatDate = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString();
};

const handleColumnAction = (command: string) => {
  const [action, status] = command.split('-');
  
  if (action === 'add') {
    createTaskForm.status = parseInt(status);
    showCreateTaskDialog.value = true;
  } else if (action === 'clear') {
    // 清空列表逻辑
  }
};

const handleTaskAction = (command: string) => {
  const [action, taskId] = command.split('-');
  
  if (action === 'edit') {
    const task = tasks.value.find(t => t.id === parseInt(taskId));
    if (task) {
      selectedTask.value = task;
      showTaskDetailDialog.value = true;
    }
  } else if (action === 'delete') {
    // 删除任务逻辑
  }
};

const handleTaskMove = (event: any) => {
  // 处理任务拖拽移动
  console.log('Task moved:', event);
};

const openTaskDetail = (task: any) => {
  selectedTask.value = task;
  showTaskDetailDialog.value = true;
};

const searchUsers = async (query: string) => {
  if (!query) {
    userOptions.value = [];
    return;
  }

  userSearchLoading.value = true;
  try {
    if (service.organization?.project?.task?.searchUsers) {
      const result = await service.organization.project.task.searchUsers({
        keyword: query
      });
      userOptions.value = result;
    } else {
      // 模拟数据
      userOptions.value = [
        { id: 1, nickName: '示例用户' }
      ];
    }
  } catch (error) {
    console.warn('搜索用户失败:', error);
    userOptions.value = [];
  } finally {
    userSearchLoading.value = false;
  }
};

const handleCreateTask = async () => {
  createTaskLoading.value = true;
  try {
    if (service.organization?.project?.task?.create) {
      await service.organization.project.task.create(createTaskForm);
      ElMessage.success('任务创建成功');
    } else {
      console.warn('任务创建服务不可用');
      ElMessage.success('任务创建成功（模拟）');
    }

    showCreateTaskDialog.value = false;

    // 重置表单
    Object.assign(createTaskForm, {
      title: '',
      description: '',
      priority: 2,
      dueDate: '',
      assigneeId: null
    });

    refreshTasks();
  } catch (error) {
    console.warn('任务创建失败:', error);
    ElMessage.error('任务创建失败');
  } finally {
    createTaskLoading.value = false;
  }
};

const refreshTasks = async () => {
  try {
    if (service.organization?.project?.task?.list) {
      const result = await service.organization.project.task.list();
      tasks.value = result;
    } else {
      console.warn('任务列表服务不可用，使用模拟数据');
      tasks.value = [
        {
          id: 1,
          title: '示例任务',
          description: '这是一个示例任务',
          status: 1,
          priority: 2,
          progress: 50,
          assignee: { id: 1, avatar: '', name: '示例用户' },
          dueDate: new Date().toISOString()
        }
      ];
    }
  } catch (error) {
    console.warn('加载任务失败:', error);
    tasks.value = [];
  }
};

// 表格配置
const Table = useTable({
  columns: [
    { type: "selection" },
    { label: "任务标题", prop: "title", minWidth: 200 },
    { label: "任务描述", prop: "description", minWidth: 200, showOverflowTooltip: true },
    { 
      label: "状态", 
      prop: "status", 
      minWidth: 100,
      dict: taskStatuses
    },
    { 
      label: "优先级", 
      prop: "priority", 
      minWidth: 100,
      dict: [
        { label: "低", value: 1, type: "info" },
        { label: "普通", value: 2, type: "" },
        { label: "中等", value: 3, type: "warning" },
        { label: "高", value: 4, type: "danger" },
        { label: "紧急", value: 5, type: "danger" }
      ]
    },
    { label: "指派人", prop: "assigneeName", minWidth: 120 },
    { label: "截止日期", prop: "dueDate", minWidth: 150, component: { name: "cl-date-text" } },
    { label: "创建时间", prop: "createTime", minWidth: 150, component: { name: "cl-date-text" } },
    { type: "op", buttons: ["edit", "delete"] }
  ]
});

const Search = useSearch({
  items: [
    {
      label: "任务标题",
      prop: "title",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: "状态",
      prop: "status",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: taskStatuses
        }
      }
    }
  ]
});

const Crud = useCrud(
  {
    service: service.organization?.project?.task || {
      // 模拟服务，避免报错
      page: () => Promise.resolve({ list: [], pagination: { total: 0 } }),
      add: () => Promise.resolve({}),
      update: () => Promise.resolve({}),
      delete: () => Promise.resolve({}),
      list: () => Promise.resolve([]),
      create: () => Promise.resolve({}),
      searchUsers: () => Promise.resolve([])
    },
  },
  (app) => {
    app.refresh();
  },
);

onMounted(() => {
  refreshTasks();
});
</script>

<style lang="scss" scoped>
.project-task {
  height: 100%;
  
  .kanban-view {
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .kanban-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid var(--el-border-color-light);
      margin-bottom: 16px;
    }
    
    .kanban-board {
      flex: 1;
      display: flex;
      gap: 16px;
      overflow-x: auto;
      padding-bottom: 16px;
      
      .kanban-column {
        min-width: 280px;
        background: var(--el-fill-color-lighter);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        
        .column-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid var(--el-border-color-light);
          
          .column-title {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .task-count {
              background: var(--el-color-primary-light-9);
              color: var(--el-color-primary);
              padding: 2px 6px;
              border-radius: 10px;
              font-size: 12px;
              font-weight: 500;
            }
          }
          
          .column-menu {
            cursor: pointer;
            color: var(--el-text-color-secondary);
            
            &:hover {
              color: var(--el-text-color-primary);
            }
          }
        }
        
        .column-content {
          flex: 1;
          padding: 12px;
          
          .task-list {
            min-height: 200px;
          }
          
          .task-card {
            background: white;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid var(--el-border-color-lighter);
            
            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              border-color: var(--el-color-primary-light-7);
            }
            
            .task-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;
              
              .task-title {
                font-weight: 500;
                color: var(--el-text-color-primary);
                flex: 1;
                line-height: 1.4;
              }
              
              .task-menu {
                cursor: pointer;
                color: var(--el-text-color-secondary);
                font-size: 14px;
                
                &:hover {
                  color: var(--el-text-color-primary);
                }
              }
            }
            
            .task-description {
              font-size: 13px;
              color: var(--el-text-color-regular);
              line-height: 1.4;
              margin-bottom: 12px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            .task-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
            }
            
            .task-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .task-due-date {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: var(--el-text-color-secondary);
                
                .el-icon {
                  font-size: 12px;
                }
              }
              
              .task-progress {
                width: 60px;
              }
            }
          }
        }
      }
    }
  }
  
  .list-view {
    height: 100%;
    
    .view-controls {
      margin-right: 16px;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .project-task {
    .kanban-view {
      .kanban-board {
        .kanban-column {
          min-width: 260px;
        }
      }
    }
  }
}
</style>

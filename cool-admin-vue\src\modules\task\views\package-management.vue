<template>
  <div class="package-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>场景任务包管理</h2>
      <p>管理场景任务包，支持批量智能分配和进度跟踪</p>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="任务包名称">
          <el-input
            v-model="searchForm.packageName"
            placeholder="请输入任务包名称"
            clearable
            @change="loadPackages"
          />
        </el-form-item>
        <el-form-item label="场景名称">
          <el-input
            v-model="searchForm.scenarioName"
            placeholder="请输入场景名称"
            clearable
            @change="loadPackages"
          />
        </el-form-item>
        <el-form-item label="完成状态">
          <el-select
            v-model="searchForm.completionStatus"
            placeholder="请选择完成状态"
            clearable
            @change="loadPackages"
          >
            <el-option label="全部" value="" />
            <el-option label="未开始" value="not_started" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadPackages">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="batchAssignSelected" :disabled="selectedPackages.length === 0">
            <el-icon><Setting /></el-icon>
            批量智能分配 ({{ selectedPackages.length }})
          </el-button>
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-button type="success" @click="createPackage">
            <el-icon><Plus /></el-icon>
            新建任务包
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 任务包列表 -->
    <el-card class="list-card" shadow="never">
      <div v-loading="loading" class="package-list">
        <div
          v-for="packageItem in packageList"
          :key="packageItem.id"
          class="package-item"
          :class="{ 'selected': selectedPackages.includes(packageItem.id) }"
          @click="togglePackageSelection(packageItem.id)"
        >
          <!-- 选择框 -->
          <div class="package-checkbox">
            <el-checkbox
              :model-value="selectedPackages.includes(packageItem.id)"
              @change="togglePackageSelection(packageItem.id)"
              @click.stop
            />
          </div>

          <!-- 任务包基本信息 -->
          <div class="package-info">
            <div class="package-header">
              <div class="package-title">
                <h3>{{ packageItem.packageName }}</h3>
                <el-tag :type="getPackageStatusType(packageItem.packageStatus)" size="small">
                  {{ getPackageStatusLabel(packageItem.packageStatus) }}
                </el-tag>
              </div>
              <div class="package-meta">
                <span class="scenario-name">{{ packageItem.scenarioName }}</span>
                <span class="create-time">{{ formatTime(packageItem.createTime) }}</span>
              </div>
            </div>

            <div class="package-stats">
              <div class="stat-item">
                <span class="stat-label">总任务数</span>
                <span class="stat-value">{{ packageItem.totalTasks || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已完成</span>
                <span class="stat-value completed">{{ packageItem.completedTasks || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">进行中</span>
                <span class="stat-value in-progress">{{ packageItem.inProgressTasks || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">待分配</span>
                <span class="stat-value pending">{{ packageItem.pendingTasks || 0 }}</span>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-section">
              <div class="progress-info">
                <span>完成进度</span>
                <span>{{ packageItem.completionRate || 0 }}%</span>
              </div>
              <el-progress
                :percentage="packageItem.completionRate || 0"
                :color="getProgressColor(packageItem.completionRate || 0)"
                :stroke-width="8"
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="package-actions" @click.stop>
            <el-button
              type="primary"
              size="small"
              @click="showPackageDetail(packageItem)"
              :loading="packageItem.loading"
            >
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="assignPackage(packageItem)"
              :disabled="packageItem.totalTasks === 0"
            >
              <el-icon><Setting /></el-icon>
              智能分配
            </el-button>
            <el-dropdown @command="handlePackageAction" trigger="click">
              <el-button size="small">
                更多 <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'edit', package: packageItem}">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'complete', package: packageItem}">
                    <el-icon><Check /></el-icon>
                    完成任务包
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'close', package: packageItem}">
                    <el-icon><Close /></el-icon>
                    关闭任务包
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', package: packageItem}" divided>
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && packageList.length === 0" description="暂无任务包数据" />
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadPackages"
          @current-change="loadPackages"
        />
      </div>
    </el-card>

    <!-- 任务包详情侧边栏 -->
    <el-drawer
      v-model="detailDrawer.visible"
      :title="`${detailDrawer.package?.packageName} - 任务详情`"
      direction="rtl"
      size="80%"
      class="detail-drawer"
    >
      <PackageDetail
        v-if="detailDrawer.visible"
        :package="detailDrawer.package"
        :tasks="detailDrawer.tasks"
        @task-updated="handleTaskUpdated"
        @assign-task="handleAssignSingleTask"
        @batch-assign="handleBatchAssignTasks"
      />
    </el-drawer>

    <!-- 分配结果弹窗 -->
    <el-dialog
      v-model="assignmentDialog.visible"
      title="分配结果"
      width="80%"
      class="assignment-dialog"
    >
      <AssignmentResult
        v-if="assignmentDialog.visible"
        :result="assignmentDialog.result"
        @confirm-assignment="handleConfirmAssignment"
        @reassign-task="handleReassignTask"
        @manual-assign="handleManualAssign"
        @retry-assignment="handleRetryAssignment"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Search,
  Setting,
  Refresh,
  Plus,
  View,
  Edit,
  Check,
  Close,
  Delete,
  ArrowDown
} from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import AssignmentResult from '../components/assignment/AssignmentResult.vue';
import PackageDetail from '../components/PackageDetail.vue';
import { AssignmentService } from '../services/assignment';

const { service } = useCool();

// 响应式数据
const loading = ref(false);
const packageList = ref([]);
const selectedPackages = ref([]);

// 搜索表单
const searchForm = reactive({
  packageName: '',
  scenarioName: '',
  completionStatus: ''
});

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
});

// 详情侧边栏
const detailDrawer = reactive({
  visible: false,
  package: null,
  tasks: []
});

// 分配结果弹窗
const assignmentDialog = reactive({
  visible: false,
  result: null
});

// 加载任务包数据
const loadPackages = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    };

    const response = await service.task.taskPackage.scenarioPackages(params);

    if (response) {
      packageList.value = response.list || [];
      pagination.total = response.pagination?.total || 0;
    }
  } catch (error) {
    console.error('加载任务包失败:', error);
    ElMessage.error('加载任务包失败');
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const resetSearch = () => {
  searchForm.packageName = '';
  searchForm.scenarioName = '';
  searchForm.completionStatus = '';
  pagination.page = 1;
  loadPackages();
};

// 刷新数据
const refreshData = () => {
  selectedPackages.value = [];
  loadPackages();
};

// 切换任务包选择状态
const togglePackageSelection = (packageId) => {
  const index = selectedPackages.value.indexOf(packageId);
  if (index > -1) {
    selectedPackages.value.splice(index, 1);
  } else {
    selectedPackages.value.push(packageId);
  }
};

// 批量分配选中的任务包
const batchAssignSelected = async () => {
  if (selectedPackages.value.length === 0) {
    ElMessage.warning('请先选择要分配的任务包');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要对选中的 ${selectedPackages.value.length} 个任务包执行智能分配吗？`,
      '批量分配确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    );

    loading.value = true;
    const results = [];

    for (const packageId of selectedPackages.value) {
      try {
        const result = await AssignmentService.assignTaskPackage(packageId, true);
        results.push({
          packageId,
          packageName: packageList.value.find(p => p.id === packageId)?.packageName || `任务包${packageId}`,
          result
        });
      } catch (error) {
        results.push({
          packageId,
          packageName: packageList.value.find(p => p.id === packageId)?.packageName || `任务包${packageId}`,
          error: error.message
        });
      }
    }

    // 显示批量分配结果
    showBatchAssignmentResult(results);

    // 重新加载数据
    await loadPackages();
    selectedPackages.value = [];

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量分配失败: ' + error.message);
    }
  } finally {
    loading.value = false;
  }
};

// 显示批量分配结果
const showBatchAssignmentResult = (results) => {
  const successCount = results.filter(r => r.result?.success).length;
  const failedCount = results.length - successCount;

  if (failedCount === 0) {
    ElMessage.success(`批量分配完成！成功分配 ${successCount} 个任务包`);
  } else {
    ElMessage.warning(`批量分配完成！成功 ${successCount} 个，失败 ${failedCount} 个`);
  }

  // 可以在这里显示详细的批量分配结果
  console.log('批量分配结果:', results);
};

// 单个任务包分配
const assignPackage = async (packageItem) => {
  packageItem.loading = true;
  try {
    const result = await AssignmentService.assignTaskPackage(packageItem.id, true);

    assignmentDialog.result = result;
    assignmentDialog.visible = true;

    // 重新加载数据
    await loadPackages();

  } catch (error) {
    console.error('任务包分配失败:', error);
    ElMessage.error('任务包分配失败: ' + error.message);
  } finally {
    packageItem.loading = false;
  }
};

// 显示任务包详情
const showPackageDetail = async (packageItem) => {
  packageItem.loading = true;
  try {
    const tasks = await service.task.info.list({
      packageId: packageItem.id,
      size: 1000
    });

    detailDrawer.package = packageItem;
    detailDrawer.tasks = tasks.list || [];
    detailDrawer.visible = true;
  } catch (error) {
    console.error('加载任务详情失败:', error);
    ElMessage.error('加载任务详情失败');
  } finally {
    packageItem.loading = false;
  }
};

// 处理任务包操作
const handlePackageAction = async ({ action, package: packageItem }) => {
  switch (action) {
    case 'edit':
      // 编辑任务包
      ElMessage.info('编辑功能开发中...');
      break;
    case 'complete':
      // 完成任务包
      try {
        await ElMessageBox.confirm('确定要完成这个任务包吗？', '完成确认');
        await service.task.taskPackage.complete(packageItem.id);
        ElMessage.success('任务包已完成');
        loadPackages();
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('完成任务包失败');
        }
      }
      break;
    case 'close':
      // 关闭任务包
      try {
        await ElMessageBox.confirm('确定要关闭这个任务包吗？', '关闭确认');
        await service.task.taskPackage.close(packageItem.id);
        ElMessage.success('任务包已关闭');
        loadPackages();
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('关闭任务包失败');
        }
      }
      break;
    case 'delete':
      // 删除任务包
      try {
        await ElMessageBox.confirm('确定要删除这个任务包吗？此操作不可恢复！', '删除确认', {
          type: 'warning'
        });
        await service.task.taskPackage.delete(packageItem.id);
        ElMessage.success('任务包已删除');
        loadPackages();
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除任务包失败');
        }
      }
      break;
  }
};

// 创建新任务包
const createPackage = () => {
  ElMessage.info('创建任务包功能开发中...');
};

// 任务更新回调
const handleTaskUpdated = () => {
  loadPackages();
};

// 分配单个任务
const handleAssignSingleTask = async (taskId) => {
  try {
    const result = await AssignmentService.assignSingleTask(taskId, true);
    assignmentDialog.result = result;
    assignmentDialog.visible = true;
  } catch (error) {
    ElMessage.error('任务分配失败: ' + error.message);
  }
};

// 批量分配任务
const handleBatchAssignTasks = async (taskIds) => {
  try {
    const result = await AssignmentService.executeAssignment({
      assignmentType: 'BATCH',
      taskIds,
      autoAssign: true
    });
    assignmentDialog.result = result;
    assignmentDialog.visible = true;
  } catch (error) {
    ElMessage.error('批量分配失败: ' + error.message);
  }
};

// 分配结果处理方法
const handleConfirmAssignment = (assignment) => {
  ElMessage.success(`已确认分配 ${assignment.taskName}`);
  loadPackages();
};

const handleReassignTask = async (assignment) => {
  try {
    const result = await AssignmentService.assignSingleTask(assignment.taskId, true);
    if (result.success) {
      ElMessage.success('重新分配成功');
      assignmentDialog.result = result;
    } else {
      ElMessage.error('重新分配失败');
    }
  } catch (error) {
    ElMessage.error('重新分配失败: ' + error.message);
  }
};

const handleManualAssign = (failedTask) => {
  ElMessage.info('请在任务详情中手动分配');
  detailDrawer.visible = true;
};

const handleRetryAssignment = async (failedTask) => {
  try {
    const result = await AssignmentService.assignSingleTask(failedTask.taskId, true);
    if (result.success) {
      ElMessage.success('重试分配成功');
      assignmentDialog.result = result;
    } else {
      ElMessage.error('重试分配失败');
    }
  } catch (error) {
    ElMessage.error('重试分配失败: ' + error.message);
  }
};

// 获取任务包状态类型
const getPackageStatusType = (status) => {
  const types = {
    0: 'warning',  // 待分配
    1: 'primary',  // 执行中
    2: 'success',  // 已完成
    3: 'info'      // 已关闭
  };
  return types[status] || 'default';
};

// 获取任务包状态标签
const getPackageStatusLabel = (status) => {
  const labels = {
    0: '待分配',
    1: '执行中',
    2: '已完成',
    3: '已关闭'
  };
  return labels[status] || '未知';
};

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 页面加载
onMounted(() => {
  loadPackages();
});
</script>

<style lang="scss" scoped>
.package-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.search-card, .toolbar-card, .list-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .toolbar-left, .toolbar-right {
    display: flex;
    gap: 12px;
  }
}

.package-list {
  min-height: 400px;
}

.package-item {
  display: flex;
  align-items: center;
  padding: 24px;
  margin-bottom: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.selected {
    border-color: #409eff;
    background: #f0f9ff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.package-checkbox {
  margin-right: 16px;
}

.package-info {
  flex: 1;

  .package-header {
    margin-bottom: 16px;

    .package-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      h3 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .package-meta {
      display: flex;
      gap: 16px;

      .scenario-name {
        color: #409eff;
        font-size: 14px;
        font-weight: 500;
      }

      .create-time {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .package-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 16px;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .stat-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 4px;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 600;

        &.completed {
          color: #67c23a;
        }

        &.in-progress {
          color: #409eff;
        }

        &.pending {
          color: #e6a23c;
        }
      }
    }
  }

  .progress-section {
    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;
      color: #606266;
    }
  }
}

.package-actions {
  margin-left: 24px;
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.detail-drawer {
  :deep(.el-drawer__header) {
    padding: 20px 20px 0 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.el-drawer__body) {
    padding: 0 20px 20px 20px;
  }
}

.assignment-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }
}
</style>
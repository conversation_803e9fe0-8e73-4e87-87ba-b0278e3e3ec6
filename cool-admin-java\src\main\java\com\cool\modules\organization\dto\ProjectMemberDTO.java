package com.cool.modules.organization.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 项目成员DTO
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Data
@Accessors(chain = true)
public class ProjectMemberDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 关系ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 用户头像
     */
    private String userAvatar;
    
    /**
     * 用户邮箱
     */
    private String userEmail;
    
    /**
     * 用户手机号
     */
    private String userPhone;
    
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 角色代码
     */
    @NotBlank(message = "角色代码不能为空")
    private String roleCode;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 加入时间
     */
    private Date joinTime;
    
    /**
     * 过期时间
     */
    private Date expireTime;
    
    /**
     * 分配人ID
     */
    private Long assignerId;
    
    /**
     * 分配人姓名
     */
    private String assignerName;
    
    /**
     * 分配时间
     */
    private Date assignTime;
    
    /**
     * 权限范围
     */
    private String permissionScope;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 是否为主要角色
     */
    private Boolean isPrimary;
    
    /**
     * 排序号
     */
    private Integer orderNum;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}

/**
 * 批量添加项目成员DTO
 */
@Data
@Accessors(chain = true)
class BatchAddProjectMemberDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
    
    /**
     * 用户ID列表
     */
    @NotNull(message = "用户ID列表不能为空")
    private List<Long> userIds;
    
    /**
     * 角色代码
     */
    @NotBlank(message = "角色代码不能为空")
    private String roleCode;
    
    /**
     * 过期时间
     */
    private Date expireTime;
    
    /**
     * 权限范围
     */
    private String permissionScope;
    
    /**
     * 备注
     */
    private String remark;
}

/**
 * 项目成员查询DTO
 */
@Data
@Accessors(chain = true)
class ProjectMemberQueryDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户姓名（模糊查询）
     */
    private String userName;
    
    /**
     * 角色代码
     */
    private String roleCode;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 是否包含过期成员
     */
    private Boolean includeExpired = false;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
}

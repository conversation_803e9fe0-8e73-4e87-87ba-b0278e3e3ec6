{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Launch Cool Admin Java",
            "request": "launch",
            "mainClass": "com.cool.CoolApplication",
            "projectName": "cool-admin-java",
            "vmArgs": [
                "-Dfile.encoding=UTF-8",
                "-Dspring.output.ansi.enabled=ALWAYS",
                "-Dspring.profiles.active=local",
                "-Dserver.port=8001",
                "-Dsun.jnu.encoding=UTF-8",
                "-Dconsole.encoding=UTF-8",
                "-Dlogging.charset.console=UTF-8",
                "-Dlogging.charset.file=UTF-8"
            ],
            "env": {
                "JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"
            },
            "console": "integratedTerminal",
        },
        {
            "type": "java",
            "name": "Debug Cool Admin Java",
            "request": "launch",
            "mainClass": "com.cool.CoolApplication",
            "projectName": "cool-admin-java",
            "vmArgs": [
                "-Dfile.encoding=UTF-8",
                "-Dspring.output.ansi.enabled=ALWAYS",
                "-Dspring.profiles.active=local",
                "-Dserver.port=8001",
                "-Dsun.jnu.encoding=UTF-8",
                "-Dconsole.encoding=UTF-8",
                "-Dlogging.charset.console=UTF-8",
                "-Dlogging.charset.file=UTF-8"
            ],
            "env": {
                "JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"
            },
            "console": "integratedTerminal",
            "terminal.integrated.defaultProfile.windows": "PowerShell"
        }
    ]
} 
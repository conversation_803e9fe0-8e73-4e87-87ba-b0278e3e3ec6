<template>
    <cl-crud ref="Crud" >
    <cl-row >
      <cl-add-btn @click="onCreate">新增APIKEY</cl-add-btn>
    </cl-row>
    <cl-row >
      <cl-table ref="Table" />
    </cl-row>
    <cl-row>
      <cl-pagination />
    </cl-row>
    <cl-upsert ref="Upsert" />
  </cl-crud>
  
</template>

<script setup lang="ts">
import { useCrud, useTable, useUpsert } from "@cool-vue/crud";
import { service } from "/@/cool/service";

const Crud = useCrud(
  { service: service.base.sys.api.key },
  app => app.refresh()
);

const Table = useTable({
  autoHeight: false,
  contextMenu: ["refresh"],
  columns: [
    { prop: "apikey", label: "APIKEY", minWidth: 320 },
    { prop: "status", label: "状态", dict: [
      { label: "启用", value: 1 },
      { label: "禁用", value: 0 }
    ] },
    { prop: "expireTime", label: "过期时间", minWidth: 160 },
    { prop: "remark", label: "备注", minWidth: 120 },
    { prop: "createTime", label: "创建时间", minWidth: 170 },
    { prop: "updateTime", label: "更新时间", minWidth: 170 }
  ]
});

const Upsert = useUpsert({
  items: [
    { label: "APIKEY", prop: "apikey", component: { name: "el-input" } },
    { label: "状态", prop: "status", component: { name: "el-select", options: [
      { label: "启用", value: 1 },
      { label: "禁用", value: 0 }
    ] } },
    { label: "过期时间", prop: "expireTime", component: { name: "el-date-picker" } },
    { label: "备注", prop: "remark", component: { name: "el-input" } }
  ]
});

function onCreate() {
  Upsert.value?.open({});
}
</script>

<script lang="ts">
export default { name: "base-user-apikey" };
</script> 
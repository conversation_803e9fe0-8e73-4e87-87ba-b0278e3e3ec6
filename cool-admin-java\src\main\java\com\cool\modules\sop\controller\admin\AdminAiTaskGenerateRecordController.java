package com.cool.modules.sop.controller.admin;

import static com.cool.modules.sop.entity.table.AiTaskGenerateRecordEntityTableDef.AI_TASK_GENERATE_RECORD_ENTITY;

import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Map;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.modules.sop.entity.AiTaskGenerateRecordEntity;
import com.cool.modules.sop.service.AiTaskGenerateRecordService;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;

/**
 * AI任务生成记录
 */
@Tag(name = "AI任务生成记录", description = "AI任务生成记录")
@CoolRestController(api = { "add", "delete", "update", "page", "list", "info" })
public class AdminAiTaskGenerateRecordController
        extends BaseController<AiTaskGenerateRecordService, AiTaskGenerateRecordEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        QueryWrapper queryWrapper = QueryWrapper.create().orderBy(AI_TASK_GENERATE_RECORD_ENTITY.CREATE_TIME, false);

        setPageOption(createOp()
                .keyWordLikeFields(AI_TASK_GENERATE_RECORD_ENTITY.USER_NAME, AI_TASK_GENERATE_RECORD_ENTITY.TASK_DESC)
                .fieldEq(AI_TASK_GENERATE_RECORD_ENTITY.STATUS, AI_TASK_GENERATE_RECORD_ENTITY.MODE)
                .queryWrapper(queryWrapper));

        setListOption(createOp()
                .keyWordLikeFields(AI_TASK_GENERATE_RECORD_ENTITY.USER_NAME, AI_TASK_GENERATE_RECORD_ENTITY.TASK_DESC)
                .fieldEq(AI_TASK_GENERATE_RECORD_ENTITY.STATUS, AI_TASK_GENERATE_RECORD_ENTITY.MODE)
                .queryWrapper(queryWrapper));
    }

} 
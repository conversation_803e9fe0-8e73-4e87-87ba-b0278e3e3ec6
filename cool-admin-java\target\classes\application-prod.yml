spring:
  datasource:
    url: *****************************************************************************************************
    username: root
    password: A@1234cn
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Redis配置
  data:
    redis:
      host: ${SPRING_DATA_REDIS_HOST:redis}
      port: ${SPRING_DATA_REDIS_PORT:6379}
      password: ${SPRING_DATA_REDIS_PASSWORD:}
      database: 0
      timeout: 5000ms
      
  # 使用Redis缓存
  cache:
    type: redis

# AutoTable配置，根据实体类自动生成表
auto-table:
  # 启用自动维护表功能
  enable: false

# Cool相关配置
cool:
  # 初始化数据
  initData: false

# 文档
springdoc:
  api-docs:
    #是否开启文档功能 本地为了配合eps功能不可关闭
    enabled: true

# 设置日志级别
logging:
  level:
    com.cool: error

# Spring Boot Actuator 配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      enabled: true
      show-details: when-authorized
  health:
    defaults:
      enabled: true
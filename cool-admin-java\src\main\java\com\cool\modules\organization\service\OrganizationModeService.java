package com.cool.modules.organization.service;

import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.dto.OrganizationModeSwitchDTO;

/**
 * 组织形态管理服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface OrganizationModeService {
    
    /**
     * 获取用户当前组织模式
     * 
     * @param userId 用户ID
     * @return 当前组织模式
     */
    String getCurrentMode(Long userId);
    
    /**
     * 切换用户组织模式
     * 
     * @param switchDTO 切换参数
     * @return 是否成功
     */
    boolean switchMode(OrganizationModeSwitchDTO switchDTO);
    
    /**
     * 检查用户是否可以切换到指定模式
     * 
     * @param userId 用户ID
     * @param targetMode 目标模式
     * @return 是否可以切换
     */
    boolean canSwitchToMode(Long userId, String targetMode);
    
    /**
     * 获取用户权限范围
     * 
     * @param userId 用户ID
     * @return 权限范围
     */
    DataPermissionScopeDTO getUserPermissionScope(Long userId);
    
    /**
     * 刷新用户权限缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserPermissionCache(Long userId);
    
    /**
     * 清理所有权限缓存
     */
    void clearAllPermissionCache();
    
    /**
     * 初始化用户组织模式
     * 
     * @param userId 用户ID
     * @param defaultMode 默认模式
     */
    void initUserMode(Long userId, String defaultMode);
    
    /**
     * 获取用户组织模式切换历史
     * 
     * @param userId 用户ID
     * @return 切换次数
     */
    Integer getUserSwitchCount(Long userId);
    
    /**
     * 检查组织模式是否有效
     * 
     * @param mode 组织模式
     * @return 是否有效
     */
    boolean isValidMode(String mode);
}

<template>
    <el-select
        :model-value="props.modelValue"
        @update:model-value="emits('update:modelValue', $event)"
        placeholder="请选择部门（可多选）"
        class="w-full"
        clearable
        multiple
        collapse-tags
        collapse-tags-tooltip
        :max-collapse-tags="3"
        filterable
        remote
        :remote-method="handleSearch"
        @visible-change="handleVisibleChange"
        :loading="loading">
        <el-option
            v-for="item in filteredDepartments"
            :key="item.id"
            :label="item.name"
            :value="item.id" />
    </el-select>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useUserStore } from "/@/modules/base/store/user";

const props = defineProps<{
    modelValue: number[];
}>();

const emits = defineEmits(["update:modelValue"]);

const userStore = useUserStore();
const loading = ref(false);
const searchKeyword = ref('');

// 过滤后的部门列表
const filteredDepartments = computed(() => {
    if (!searchKeyword.value) {
        return userStore.departments;
    }
    return userStore.departments.filter(dept => 
        dept.name && dept.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
});

const handleVisibleChange = (visible: boolean) => {
    if (visible && userStore.departments.length === 0) {
        fetchDepartments();
    }
    if (!visible) {
        searchKeyword.value = '';
    }
};

// 处理搜索
const handleSearch = (query: string) => {
    searchKeyword.value = query;
};

const fetchDepartments = async () => {
    if (loading.value) return;
    loading.value = true;
    try {
        await userStore.getDepartmentList();
    } catch (error) {
        console.error("Failed to fetch departments", error);
    } finally {
        loading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.w-full {
    width: 100%;
}
</style> 
package com.cool.modules.organization.mapper;

import com.cool.modules.organization.entity.UserCurrentModeEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户当前模式Mapper
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Mapper
public interface UserCurrentModeMapper extends BaseMapper<UserCurrentModeEntity> {
    
    /**
     * 根据用户ID获取当前模式
     * 
     * @param userId 用户ID
     * @return 当前模式
     */
    String getCurrentModeByUserId(@Param("userId") Long userId);
    
    /**
     * 更新用户当前模式
     * 
     * @param userId 用户ID
     * @param currentMode 当前模式
     * @return 影响行数
     */
    int updateCurrentModeByUserId(@Param("userId") Long userId, @Param("currentMode") String currentMode);
}

<template>
  <div class="project-template">
    <!-- 模板头部 -->
    <div class="template-header">
      <div class="header-left">
        <h2>项目模板管理</h2>
        <p>创建和管理项目模板，提高项目创建效率</p>
      </div>
      
      <div class="header-right">
        <el-button type="primary" @click="showCreateTemplateDialog = true">
          <el-icon><Plus /></el-icon>
          创建模板
        </el-button>
        
        <el-button @click="importTemplate">
          <el-icon><Upload /></el-icon>
          导入模板
        </el-button>
      </div>
    </div>

    <!-- 模板分类 -->
    <div class="template-categories">
      <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
        <el-tab-pane 
          v-for="category in templateCategories" 
          :key="category.code"
          :label="category.name" 
          :name="category.code"
        >
          <template #label>
            <span class="category-label">
              <el-icon><component :is="category.icon" /></el-icon>
              {{ category.name }}
              <el-badge :value="category.count" class="category-badge" />
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 模板列表 -->
    <div class="template-list">
      <el-row :gutter="20">
        <el-col 
          v-for="template in filteredTemplates" 
          :key="template.id"
          :span="8"
        >
          <el-card class="template-card" shadow="hover">
            <div class="template-preview">
              <div class="template-image">
                <img 
                  v-if="template.thumbnail" 
                  :src="template.thumbnail" 
                  :alt="template.name"
                />
                <div v-else class="default-image">
                  <el-icon><FolderOpened /></el-icon>
                </div>
              </div>
              
              <div class="template-overlay">
                <el-button-group>
                  <el-button size="small" @click="previewTemplate(template)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                  <el-button size="small" @click="useTemplate(template)">
                    <el-icon><Plus /></el-icon>
                    使用
                  </el-button>
                </el-button-group>
              </div>
            </div>
            
            <div class="template-info">
              <div class="template-header">
                <h4 class="template-name">{{ template.name }}</h4>
                <el-dropdown @command="handleTemplateAction">
                  <el-icon class="template-menu"><MoreFilled /></el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`edit-${template.id}`">
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item :command="`copy-${template.id}`">
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item :command="`export-${template.id}`">
                        导出
                      </el-dropdown-item>
                      <el-dropdown-item :command="`delete-${template.id}`" divided>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              
              <p class="template-description">{{ template.description }}</p>
              
              <div class="template-meta">
                <div class="template-tags">
                  <el-tag 
                    v-for="tag in template.tags" 
                    :key="tag"
                    size="small"
                    type="info"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                
                <div class="template-stats">
                  <span class="stat-item">
                    <el-icon><User /></el-icon>
                    {{ template.useCount }}次使用
                  </span>
                  <span class="stat-item">
                    <el-icon><Star /></el-icon>
                    {{ template.rating }}
                  </span>
                </div>
              </div>
              
              <div class="template-footer">
                <div class="template-author">
                  <el-avatar :size="24" :src="template.author.avatar" />
                  <span>{{ template.author.name }}</span>
                </div>
                
                <div class="template-date">
                  {{ formatDate(template.updateTime) }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0" class="empty-state">
        <el-empty description="暂无模板">
          <el-button type="primary" @click="showCreateTemplateDialog = true">
            创建第一个模板
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 创建模板对话框 -->
    <el-dialog
      v-model="showCreateTemplateDialog"
      title="创建项目模板"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form ref="createTemplateFormRef" :model="createTemplateForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name" required>
              <el-input v-model="createTemplateForm.name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="模板分类" prop="category" required>
              <el-select v-model="createTemplateForm.category" style="width: 100%">
                <el-option
                  v-for="category in templateCategories"
                  :key="category.code"
                  :label="category.name"
                  :value="category.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="模板描述" prop="description">
          <el-input 
            v-model="createTemplateForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        
        <el-form-item label="模板标签" prop="tags">
          <el-select
            v-model="createTemplateForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="模板缩略图" prop="thumbnail">
          <el-upload
            class="thumbnail-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleThumbnailSuccess"
            :before-upload="beforeThumbnailUpload"
          >
            <img v-if="createTemplateForm.thumbnail" :src="createTemplateForm.thumbnail" class="thumbnail" />
            <el-icon v-else class="thumbnail-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="模板配置">
          <el-tabs v-model="activeConfigTab" type="border-card">
            <el-tab-pane label="基本信息" name="basic">
              <template-basic-config v-model="createTemplateForm.config.basic" />
            </el-tab-pane>
            
            <el-tab-pane label="任务模板" name="tasks">
              <template-task-config v-model="createTemplateForm.config.tasks" />
            </el-tab-pane>
            
            <el-tab-pane label="成员角色" name="roles">
              <template-role-config v-model="createTemplateForm.config.roles" />
            </el-tab-pane>
            
            <el-tab-pane label="工作流程" name="workflow">
              <template-workflow-config v-model="createTemplateForm.config.workflow" />
            </el-tab-pane>
          </el-tabs>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateTemplateDialog = false">取消</el-button>
          <el-button @click="saveAsDraft">保存草稿</el-button>
          <el-button 
            type="primary" 
            :loading="createTemplateLoading"
            @click="handleCreateTemplate"
          >
            创建模板
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <template-preview-dialog
      v-model="showPreviewDialog"
      :template="selectedTemplate"
    />

    <!-- 使用模板对话框 -->
    <use-template-dialog
      v-model="showUseTemplateDialog"
      :template="selectedTemplate"
      @created="handleProjectCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  Plus, 
  Upload, 
  FolderOpened, 
  View, 
  MoreFilled,
  User,
  Star,
  Briefcase,
  Code,
  Design,
  Management
} from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import TemplatePreviewDialog from '../components/template-preview-dialog.vue';
import UseTemplateDialog from '../components/use-template-dialog.vue';
import TemplateBasicConfig from '../components/template-basic-config.vue';
import TemplateTaskConfig from '../components/template-task-config.vue';
import TemplateRoleConfig from '../components/template-role-config.vue';
import TemplateWorkflowConfig from '../components/template-workflow-config.vue';

defineOptions({
  name: "project-template",
});

const { service } = useCool();

// 响应式数据
const showCreateTemplateDialog = ref(false);
const showPreviewDialog = ref(false);
const showUseTemplateDialog = ref(false);
const createTemplateLoading = ref(false);
const activeCategory = ref('all');
const activeConfigTab = ref('basic');
const selectedTemplate = ref(null);

// 模板分类
const templateCategories = ref([
  { code: 'all', name: '全部', icon: 'FolderOpened', count: 0 },
  { code: 'software', name: '软件开发', icon: 'Code', count: 0 },
  { code: 'design', name: '设计项目', icon: 'Design', count: 0 },
  { code: 'marketing', name: '营销活动', icon: 'Briefcase', count: 0 },
  { code: 'management', name: '项目管理', icon: 'Management', count: 0 }
]);

// 模板数据
const templates = ref([]);

// 常用标签
const commonTags = ref([
  '敏捷开发', 'Web开发', '移动应用', 'UI设计', '品牌设计',
  '数字营销', '内容营销', '项目管理', '团队协作', '创新项目'
]);

// 创建模板表单
const createTemplateForm = reactive({
  name: '',
  category: '',
  description: '',
  tags: [],
  thumbnail: '',
  config: {
    basic: {},
    tasks: [],
    roles: [],
    workflow: {}
  }
});

// 计算属性
const filteredTemplates = computed(() => {
  if (activeCategory.value === 'all') {
    return templates.value;
  }
  return templates.value.filter(template => template.category === activeCategory.value);
});

// 方法
const handleCategoryChange = (category: string) => {
  activeCategory.value = category;
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const previewTemplate = (template: any) => {
  selectedTemplate.value = template;
  showPreviewDialog.value = true;
};

const useTemplate = (template: any) => {
  selectedTemplate.value = template;
  showUseTemplateDialog.value = true;
};

const handleTemplateAction = async (command: string) => {
  const [action, templateId] = command.split('-');
  const template = templates.value.find(t => t.id === parseInt(templateId));
  
  if (action === 'edit') {
    // 编辑模板
  } else if (action === 'copy') {
    // 复制模板
  } else if (action === 'export') {
    // 导出模板
    try {
      const response = await service.organization.project.template.export({
        id: templateId
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.download = `${template.name}_模板.json`;
      link.click();
      window.URL.revokeObjectURL(url);
      
      ElMessage.success('模板导出成功');
    } catch (error) {
      ElMessage.error('模板导出失败');
    }
  } else if (action === 'delete') {
    try {
      await ElMessageBox.confirm(
        `确定要删除模板 "${template.name}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );
      
      await service.organization.project.template.delete({ id: templateId });
      ElMessage.success('删除成功');
      loadTemplates();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败');
      }
    }
  }
};

const importTemplate = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = async (e: any) => {
    const file = e.target.files[0];
    if (!file) return;
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      await service.organization.project.template.import(formData);
      ElMessage.success('模板导入成功');
      loadTemplates();
    } catch (error) {
      ElMessage.error('模板导入失败');
    }
  };
  input.click();
};

const handleThumbnailSuccess = (response: any) => {
  createTemplateForm.thumbnail = response.url;
};

const beforeThumbnailUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }
  return true;
};

const saveAsDraft = async () => {
  try {
    await service.organization.project.template.saveDraft(createTemplateForm);
    ElMessage.success('草稿保存成功');
  } catch (error) {
    ElMessage.error('草稿保存失败');
  }
};

const handleCreateTemplate = async () => {
  createTemplateLoading.value = true;
  try {
    await service.organization.project.template.create(createTemplateForm);
    ElMessage.success('模板创建成功');
    showCreateTemplateDialog.value = false;
    
    // 重置表单
    Object.assign(createTemplateForm, {
      name: '',
      category: '',
      description: '',
      tags: [],
      thumbnail: '',
      config: {
        basic: {},
        tasks: [],
        roles: [],
        workflow: {}
      }
    });
    
    loadTemplates();
  } catch (error) {
    ElMessage.error('模板创建失败');
  } finally {
    createTemplateLoading.value = false;
  }
};

const handleProjectCreated = (project: any) => {
  ElMessage.success(`项目 "${project.name}" 创建成功`);
  // 可以跳转到项目详情页
};

const loadTemplates = async () => {
  try {
    if (service.organization?.project?.template?.list) {
      const data = await service.organization.project.template.list();
      templates.value = data;

      // 更新分类计数
      templateCategories.value.forEach(category => {
        if (category.code === 'all') {
          category.count = data.length;
        } else {
          category.count = data.filter((t: any) => t.category === category.code).length;
        }
      });
    } else {
      console.warn('模板服务不可用，使用模拟数据');
      // 使用模拟数据
      templates.value = [
        {
          id: 1,
          name: '敏捷开发模板',
          description: '适用于敏捷开发项目的标准模板',
          category: 'software',
          tags: ['敏捷开发', 'Scrum'],
          thumbnail: '',
          useCount: 25,
          rating: 4.8,
          author: { name: '系统管理员', avatar: '' },
          updateTime: new Date().toISOString()
        }
      ];

      // 更新分类计数
      templateCategories.value.forEach(category => {
        if (category.code === 'all') {
          category.count = templates.value.length;
        } else {
          category.count = templates.value.filter((t: any) => t.category === category.code).length;
        }
      });
    }
  } catch (error) {
    console.warn('加载模板列表失败:', error);
    templates.value = [];
  }
};

onMounted(() => {
  loadTemplates();
});
</script>

<style lang="scss" scoped>
.project-template {
  padding: 20px;
  
  .template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
    }
  }
  
  .template-categories {
    margin-bottom: 24px;
    
    .category-label {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .category-badge {
        margin-left: 4px;
      }
    }
  }
  
  .template-list {
    .template-card {
      margin-bottom: 20px;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }
      
      .template-preview {
        position: relative;
        height: 160px;
        overflow: hidden;
        border-radius: 4px 4px 0 0;
        
        .template-image {
          width: 100%;
          height: 100%;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .default-image {
            width: 100%;
            height: 100%;
            background: var(--el-fill-color-light);
            display: flex;
            align-items: center;
            justify-content: center;
            
            .el-icon {
              font-size: 48px;
              color: var(--el-text-color-secondary);
            }
          }
        }
        
        .template-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
        }
        
        &:hover .template-overlay {
          opacity: 1;
        }
      }
      
      .template-info {
        padding: 16px;
        
        .template-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
          
          .template-name {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
            flex: 1;
          }
          
          .template-menu {
            cursor: pointer;
            color: var(--el-text-color-secondary);
            
            &:hover {
              color: var(--el-text-color-primary);
            }
          }
        }
        
        .template-description {
          margin: 0 0 12px 0;
          font-size: 13px;
          color: var(--el-text-color-regular);
          line-height: 1.4;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .template-meta {
          margin-bottom: 12px;
          
          .template-tags {
            margin-bottom: 8px;
            
            .el-tag {
              margin-right: 4px;
              margin-bottom: 4px;
            }
          }
          
          .template-stats {
            display: flex;
            gap: 12px;
            
            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: var(--el-text-color-secondary);
              
              .el-icon {
                font-size: 12px;
              }
            }
          }
        }
        
        .template-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .template-author {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
          
          .template-date {
            font-size: 12px;
            color: var(--el-text-color-secondary);
          }
        }
      }
    }
    
    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }
  }
  
  .thumbnail-uploader {
    .thumbnail {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 4px;
    }
    
    .thumbnail-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      line-height: 100px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .project-template {
    padding: 16px;
    
    .template-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }
    
    .template-list {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>

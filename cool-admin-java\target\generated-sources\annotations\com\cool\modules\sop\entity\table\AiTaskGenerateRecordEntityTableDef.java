package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class AiTaskGenerateRecordEntityTableDef extends TableDef {

    /**
     * AI任务生成记录
     */
    public static final AiTaskGenerateRecordEntityTableDef AI_TASK_GENERATE_RECORD_ENTITY = new AiTaskGenerateRecordEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn MODE = new QueryColumn(this, "mode");

    public final QueryColumn PARAMS = new QueryColumn(this, "params");

    public final QueryColumn RESULT = new QueryColumn(this, "result");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    public final QueryColumn PREVIEW = new QueryColumn(this, "preview");

    public final QueryColumn COST_TIME = new QueryColumn(this, "cost_time");

    public final QueryColumn PROGRESS = new QueryColumn(this, "progress");

    public final QueryColumn TASK_DESC = new QueryColumn(this, "task_desc");

    public final QueryColumn USER_NAME = new QueryColumn(this, "user_name");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn FAIL_REASON = new QueryColumn(this, "fail_reason");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn PARENT_RECORD_ID = new QueryColumn(this, "parent_record_id");

    public final QueryColumn PROGRESS_DETAILS = new QueryColumn(this, "progress_details");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, MODE, PARAMS, RESULT, STATUS, USER_ID, PREVIEW, COST_TIME, PROGRESS, TASK_DESC, USER_NAME, CREATE_TIME, FAIL_REASON, UPDATE_TIME, PARENT_RECORD_ID, PROGRESS_DETAILS};

    public AiTaskGenerateRecordEntityTableDef() {
        super("", "ai_task_generate_record");
    }

    private AiTaskGenerateRecordEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public AiTaskGenerateRecordEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new AiTaskGenerateRecordEntityTableDef("", "ai_task_generate_record", alias));
    }

}

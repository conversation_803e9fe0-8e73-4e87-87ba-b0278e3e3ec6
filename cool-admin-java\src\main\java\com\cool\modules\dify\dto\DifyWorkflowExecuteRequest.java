package com.cool.modules.dify.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

import cn.hutool.json.JSONObject;

/**
 * Dify工作流执行请求参数
 */
@Data
public class DifyWorkflowExecuteRequest {
    /** 工作流英文名 */
    @NotBlank
    private String workflowName;
    /** 用户输入内容 */
    @NotBlank
    private String query;
    /** 变量参数（可选） */
    private Map<String, Object> inputs;
    /** 用户标识 */
    private String user;
    /** 会话ID（可选） */
    private String conversationId;
    /**
     * 响应模式（blocking/streaming）
     */
    private String responseMode = DifyWorkflowResponseModeEnum.BLOCKING.getValue();
    /** 文件参数（可选） */
    private List<Map<String, Object>> files;

   
} 
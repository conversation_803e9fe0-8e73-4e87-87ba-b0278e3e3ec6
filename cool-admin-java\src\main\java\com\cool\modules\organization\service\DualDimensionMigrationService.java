package com.cool.modules.organization.service;

/**
 * 双维度权限数据迁移服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface DualDimensionMigrationService {
    
    /**
     * 执行完整的数据迁移
     * 
     * @return 迁移结果报告
     */
    MigrationResult executeFullMigration();
    
    /**
     * 迁移工单部门数据
     * 
     * @return 迁移的记录数
     */
    int migrateWorkOrderDepartments();
    
    /**
     * 初始化项目维度数据
     * 
     * @return 初始化结果
     */
    boolean initializeProjectDimension();
    
    /**
     * 验证数据一致性
     * 
     * @return 验证结果
     */
    ValidationResult validateDataConsistency();
    
    /**
     * 回滚迁移
     * 
     * @return 回滚结果
     */
    boolean rollbackMigration();
    
    /**
     * 获取迁移状态
     * 
     * @return 迁移状态
     */
    MigrationStatus getMigrationStatus();
    
    /**
     * 迁移结果
     */
    class MigrationResult {
        private boolean success;
        private String message;
        private int workOrderMigrated;
        private int taskPackageProcessed;
        private int taskInfoProcessed;
        private int taskExecutionProcessed;
        private long executionTime;
        
        // getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getWorkOrderMigrated() { return workOrderMigrated; }
        public void setWorkOrderMigrated(int workOrderMigrated) { this.workOrderMigrated = workOrderMigrated; }
        
        public int getTaskPackageProcessed() { return taskPackageProcessed; }
        public void setTaskPackageProcessed(int taskPackageProcessed) { this.taskPackageProcessed = taskPackageProcessed; }
        
        public int getTaskInfoProcessed() { return taskInfoProcessed; }
        public void setTaskInfoProcessed(int taskInfoProcessed) { this.taskInfoProcessed = taskInfoProcessed; }
        
        public int getTaskExecutionProcessed() { return taskExecutionProcessed; }
        public void setTaskExecutionProcessed(int taskExecutionProcessed) { this.taskExecutionProcessed = taskExecutionProcessed; }
        
        public long getExecutionTime() { return executionTime; }
        public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
    }
    
    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private String message;
        private int taskPackageIssues;
        private int taskInfoIssues;
        private int taskExecutionIssues;
        private int workOrderIssues;
        
        // getters and setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getTaskPackageIssues() { return taskPackageIssues; }
        public void setTaskPackageIssues(int taskPackageIssues) { this.taskPackageIssues = taskPackageIssues; }
        
        public int getTaskInfoIssues() { return taskInfoIssues; }
        public void setTaskInfoIssues(int taskInfoIssues) { this.taskInfoIssues = taskInfoIssues; }
        
        public int getTaskExecutionIssues() { return taskExecutionIssues; }
        public void setTaskExecutionIssues(int taskExecutionIssues) { this.taskExecutionIssues = taskExecutionIssues; }
        
        public int getWorkOrderIssues() { return workOrderIssues; }
        public void setWorkOrderIssues(int workOrderIssues) { this.workOrderIssues = workOrderIssues; }
    }
    
    /**
     * 迁移状态
     */
    enum MigrationStatus {
        NOT_STARTED("未开始"),
        IN_PROGRESS("进行中"),
        COMPLETED("已完成"),
        FAILED("失败"),
        ROLLED_BACK("已回滚");
        
        private final String description;
        
        MigrationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}

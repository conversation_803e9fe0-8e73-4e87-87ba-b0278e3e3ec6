{ 
    // 编码解决方案
    "files.encoding": "utf8",
    "[java]": {
        "files.encoding": "utf8"
    },
    // 终端配置
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "args": ["-NoExit", "-Command", "chcp 65001"]
        },
        "Command Prompt": {
            "path": "cmd.exe",
            "args": ["/k", "chcp 65001"]
        }
    },
    "terminal.integrated.defaultProfile.windows": "Command Prompt",
    "terminal.integrated.fontFamily": "'Sarasa Mono SC', Consolas, '微软雅黑'",
    // Java调试设置
    "java.debug.settings.onBuildFailureProceed": true,
    // Java项目配置
    "java.configuration.workspaceCacheLimit": 90,
    "java.project.sourcePaths": ["cool-admin-java/src/main/java"],
    "java.project.outputPath": "cool-admin-java/target/classes",
    "java.project.referencedLibraries": [
        "cool-admin-java/target/dependency/*.jar"
    ]
}

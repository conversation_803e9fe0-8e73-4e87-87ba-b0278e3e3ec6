package com.cool.modules.base.entity.sys;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * APIKEY授权表实体
 */
@Getter
@Setter
@Table(value = "base_sys_apikey", comment = "APIKEY授权表")
public class BaseSysApiKeyEntity extends BaseEntity<BaseSysApiKeyEntity> {

    @ColumnDefine(comment = "关联用户ID")
    private Long userId;

    @ColumnDefine(comment = "APIKEY（唯一、加密存储）", length = 64)
    private String apikey;

    @ColumnDefine(comment = "状态", type = "tinyint", defaultValue = "1")
    private Integer status;

    @ColumnDefine(comment = "过期时间（null为永久）")
    private Date expireTime;

    @ColumnDefine(comment = "备注", length = 255)
    private String remark;

    @ColumnDefine(comment = "创建时间")
    private Date createTime;

    @Column(ignore = true)
    private String username; // 业务字段，非表字段
} 
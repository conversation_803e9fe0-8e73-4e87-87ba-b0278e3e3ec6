package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务分配类型枚举
 */
@Getter
@AllArgsConstructor
public enum AssignmentTypeEnum {
    
    /**
     * 手动分配 - 管理员或负责人手动指定执行人
     */
    MANUAL("MANUAL", "手动分配"),
    
    /**
     * 自动分配 - 系统根据算法自动分配执行人
     */
    AUTO("AUTO", "自动分配"),
    
    /**
     * AI分配 - AI智能推荐分配执行人
     */
    AI("AI", "AI分配"),
    
    /**
     * 批量分配 - 批量操作分配多个任务
     */
    BATCH("BATCH", "批量分配");

    private final String code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static AssignmentTypeEnum getByCode(String code) {
        if (code == null || code.isEmpty()) return null;
        for (AssignmentTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(String code) {
        AssignmentTypeEnum type = getByCode(code);
        return type != null ? type.getName() : "未知";
    }
    
    /**
     * 检查是否为有效的分配类型
     */
    public static boolean isValidType(String code) {
        return getByCode(code) != null;
    }
    
    /**
     * 检查是否为自动分配类型（AUTO或AI）
     */
    public static boolean isAutoType(String code) {
        AssignmentTypeEnum type = getByCode(code);
        return type == AUTO || type == AI;
    }
    
    /**
     * 检查是否为手动分配类型
     */
    public static boolean isManualType(String code) {
        AssignmentTypeEnum type = getByCode(code);
        return type == MANUAL;
    }
} 
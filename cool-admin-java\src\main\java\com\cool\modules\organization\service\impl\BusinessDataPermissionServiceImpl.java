package com.cool.modules.organization.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.OrganizationModeService;
import com.cool.modules.sop.entity.WorkOrderEntity;
import com.cool.modules.sop.service.WorkOrderService;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务数据权限服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessDataPermissionServiceImpl {
    
    private final OrganizationModeService organizationModeService;
    private final TaskPackageService taskPackageService;
    private final TaskInfoService taskInfoService;
    private final TaskExecutionService taskExecutionService;
    private final WorkOrderService workOrderService;
    
    /**
     * 为任务包应用权限过滤
     */
    public void applyTaskPackagePermissionFilter(QueryWrapper queryWrapper, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            return;
        }
        
        if (scope.isDepartmentMode()) {
            List<Long> departmentIds = scope.getDepartmentIds();
            if (CollUtil.isEmpty(departmentIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(TaskPackageEntity::getDepartmentId, departmentIds);
            }
        } else if (scope.isProjectMode()) {
            List<Long> projectIds = scope.getProjectIds();
            if (CollUtil.isEmpty(projectIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(TaskPackageEntity::getProjectId, projectIds);
            }
        }
    }
    
    /**
     * 为任务信息应用权限过滤
     */
    public void applyTaskInfoPermissionFilter(QueryWrapper queryWrapper, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            return;
        }
        
        if (scope.isDepartmentMode()) {
            List<Long> departmentIds = scope.getDepartmentIds();
            if (CollUtil.isEmpty(departmentIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(TaskInfoEntity::getDepartmentId, departmentIds);
            }
        } else if (scope.isProjectMode()) {
            List<Long> projectIds = scope.getProjectIds();
            if (CollUtil.isEmpty(projectIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(TaskInfoEntity::getProjectId, projectIds);
            }
        }
    }
    
    /**
     * 为任务执行应用权限过滤
     */
    public void applyTaskExecutionPermissionFilter(QueryWrapper queryWrapper, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            return;
        }
        
        if (scope.isDepartmentMode()) {
            List<Long> departmentIds = scope.getDepartmentIds();
            if (CollUtil.isEmpty(departmentIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(TaskExecutionEntity::getDepartmentId, departmentIds);
            }
        } else if (scope.isProjectMode()) {
            List<Long> projectIds = scope.getProjectIds();
            if (CollUtil.isEmpty(projectIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(TaskExecutionEntity::getProjectId, projectIds);
            }
        }
    }
    
    /**
     * 为工单应用权限过滤
     */
    public void applyWorkOrderPermissionFilter(QueryWrapper queryWrapper, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.getIsUnlimited()) {
            return;
        }
        
        if (scope.isDepartmentMode()) {
            List<Long> departmentIds = scope.getDepartmentIds();
            if (CollUtil.isEmpty(departmentIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(WorkOrderEntity::getApplicantDeptId, departmentIds);
            }
        } else if (scope.isProjectMode()) {
            List<Long> projectIds = scope.getProjectIds();
            if (CollUtil.isEmpty(projectIds)) {
                queryWrapper.and("1 = 0");
            } else {
                queryWrapper.in(WorkOrderEntity::getProjectId, projectIds);
            }
        }
    }
    
    /**
     * 设置任务包的组织归属信息
     */
    public void setTaskPackageOrganizationInfo(TaskPackageEntity entity, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.isDepartmentMode()) {
            // 部门模式：设置部门信息
            if (entity.getDepartmentId() == null && CollUtil.isNotEmpty(scope.getDepartmentIds())) {
                entity.setDepartmentId(scope.getDepartmentIds().get(0));
            }
            entity.setProjectId(null);
        } else if (scope.isProjectMode()) {
            // 项目模式：必须指定项目ID
            if (entity.getProjectId() == null) {
                throw new RuntimeException("项目模式下创建任务包必须指定项目ID");
            }
            
            // 验证用户是否有该项目的权限
            if (CollUtil.isEmpty(scope.getProjectIds()) || !scope.getProjectIds().contains(entity.getProjectId())) {
                throw new RuntimeException("用户无权限在该项目下创建任务包");
            }
            
            entity.setDepartmentId(null);
        }
    }
    
    /**
     * 设置任务信息的组织归属信息
     */
    public void setTaskInfoOrganizationInfo(TaskInfoEntity entity, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.isDepartmentMode()) {
            // 部门模式：设置部门信息
            if (entity.getDepartmentId() == null && CollUtil.isNotEmpty(scope.getDepartmentIds())) {
                entity.setDepartmentId(scope.getDepartmentIds().get(0));
            }
            entity.setProjectId(null);
        } else if (scope.isProjectMode()) {
            // 项目模式：必须指定项目ID
            if (entity.getProjectId() == null) {
                throw new RuntimeException("项目模式下创建任务必须指定项目ID");
            }
            
            // 验证用户是否有该项目的权限
            if (CollUtil.isEmpty(scope.getProjectIds()) || !scope.getProjectIds().contains(entity.getProjectId())) {
                throw new RuntimeException("用户无权限在该项目下创建任务");
            }
            
            entity.setDepartmentId(null);
        }
    }
    
    /**
     * 设置任务执行的组织归属信息
     */
    public void setTaskExecutionOrganizationInfo(TaskExecutionEntity entity, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.isDepartmentMode()) {
            // 部门模式：设置部门信息
            if (entity.getDepartmentId() == null && CollUtil.isNotEmpty(scope.getDepartmentIds())) {
                entity.setDepartmentId(scope.getDepartmentIds().get(0));
            }
            entity.setProjectId(null);
        } else if (scope.isProjectMode()) {
            // 项目模式：必须指定项目ID
            if (entity.getProjectId() == null) {
                throw new RuntimeException("项目模式下创建任务执行必须指定项目ID");
            }
            
            // 验证用户是否有该项目的权限
            if (CollUtil.isEmpty(scope.getProjectIds()) || !scope.getProjectIds().contains(entity.getProjectId())) {
                throw new RuntimeException("用户无权限在该项目下创建任务执行");
            }
            
            entity.setDepartmentId(null);
        }
    }
    
    /**
     * 设置工单的组织归属信息
     */
    public void setWorkOrderOrganizationInfo(WorkOrderEntity entity, Long userId) {
        DataPermissionScopeDTO scope = organizationModeService.getUserPermissionScope(userId);
        
        if (scope.isDepartmentMode()) {
            // 部门模式：设置部门信息
            if (entity.getApplicantDeptId() == null && CollUtil.isNotEmpty(scope.getDepartmentIds())) {
                entity.setApplicantDeptId(scope.getDepartmentIds().get(0));
            }
            entity.setProjectId(null);
        } else if (scope.isProjectMode()) {
            // 项目模式：必须指定项目ID
            if (entity.getProjectId() == null) {
                throw new RuntimeException("项目模式下创建工单必须指定项目ID");
            }
            
            // 验证用户是否有该项目的权限
            if (CollUtil.isEmpty(scope.getProjectIds()) || !scope.getProjectIds().contains(entity.getProjectId())) {
                throw new RuntimeException("用户无权限在该项目下创建工单");
            }
            
            entity.setApplicantDeptId(null);
        }
    }
    
    /**
     * 批量获取实体的组织ID
     */
    public Map<Long, Long> batchGetEntityOrganizationIds(String entityType, List<Long> entityIds, String organizationMode) {
        Map<Long, Long> result = new HashMap<>();
        
        if (CollUtil.isEmpty(entityIds)) {
            return result;
        }
        
        try {
            switch (entityType) {
                case "TaskPackage":
                    List<TaskPackageEntity> taskPackages = taskPackageService.listByIds(entityIds);
                    for (TaskPackageEntity entity : taskPackages) {
                        Long orgId = OrganizationModeEnum.DEPARTMENT.getCode().equals(organizationMode) 
                            ? entity.getDepartmentId() : entity.getProjectId();
                        if (orgId != null) {
                            result.put(entity.getId(), orgId);
                        }
                    }
                    break;
                    
                case "TaskInfo":
                    List<TaskInfoEntity> taskInfos = taskInfoService.listByIds(entityIds);
                    for (TaskInfoEntity entity : taskInfos) {
                        Long orgId = OrganizationModeEnum.DEPARTMENT.getCode().equals(organizationMode) 
                            ? entity.getDepartmentId() : entity.getProjectId();
                        if (orgId != null) {
                            result.put(entity.getId(), orgId);
                        }
                    }
                    break;
                    
                case "TaskExecution":
                    List<TaskExecutionEntity> taskExecutions = taskExecutionService.listByIds(entityIds);
                    for (TaskExecutionEntity entity : taskExecutions) {
                        Long orgId = OrganizationModeEnum.DEPARTMENT.getCode().equals(organizationMode) 
                            ? entity.getDepartmentId() : entity.getProjectId();
                        if (orgId != null) {
                            result.put(entity.getId(), orgId);
                        }
                    }
                    break;
                    
                case "WorkOrder":
                    List<WorkOrderEntity> workOrders = workOrderService.listByIds(entityIds);
                    for (WorkOrderEntity entity : workOrders) {
                        Long orgId = OrganizationModeEnum.DEPARTMENT.getCode().equals(organizationMode) 
                            ? entity.getApplicantDeptId() : entity.getProjectId();
                        if (orgId != null) {
                            result.put(entity.getId(), orgId);
                        }
                    }
                    break;
                    
                default:
                    log.warn("未知的实体类型: {}", entityType);
            }
        } catch (Exception e) {
            log.error("批量获取实体组织ID失败: entityType={}", entityType, e);
        }
        
        return result;
    }
}

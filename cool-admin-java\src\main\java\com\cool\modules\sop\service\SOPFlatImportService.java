package com.cool.modules.sop.service;

import com.cool.modules.sop.dto.SOPImportRequest;
import com.cool.modules.sop.dto.SOPImportResult;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * SOP扁平化导入服务接口
 */
public interface SOPFlatImportService {
    
    /**
     * 创建Excel模板
     * 
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void createTemplate(HttpServletResponse response) throws IOException;
    
    /**
     * 预览导入数据
     * 
     * @param file Excel文件
     * @return 导入预览结果
     */
    SOPImportResult previewImport(MultipartFile file);
    
    /**
     * 执行SOP导入
     * 
     * @param request 导入请求
     * @return 导入结果
     */
    SOPImportResult importSOP(SOPImportRequest request);
    
    /**
     * 检查版本冲突
     * 
     * @param file Excel文件
     * @return 冲突检查结果
     */
    SOPImportResult checkVersionConflicts(MultipartFile file);
    
    /**
     * 恢复备份数据
     * 
     * @param backupId 备份ID
     * @return 恢复结果
     */
    SOPImportResult restoreBackup(String backupId);
    
    /**
     * 生成新版本号
     * 
     * @param currentVersion 当前版本
     * @param strategy 版本策略
     * @return 新版本号
     */
    String generateVersion(String currentVersion, String strategy);
}

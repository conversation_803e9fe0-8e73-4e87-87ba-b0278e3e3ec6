<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.task.mapper.TaskPermissionLogMapper">

    <!-- 查询用户的权限操作统计 -->
    <select id="getUserPermissionStats" resultType="map">
        SELECT operation_type, COUNT(*) as count, 
               SUM(CASE WHEN permission_result = 1 THEN 1 ELSE 0 END) as success_count, 
               SUM(CASE WHEN permission_result = 0 THEN 1 ELSE 0 END) as fail_count 
        FROM task_permission_log 
        WHERE user_id = #{userId} 
        AND operation_time <![CDATA[ >= ]]> #{startTime} 
        AND operation_time <![CDATA[ <= ]]> #{endTime} 
        GROUP BY operation_type 
        ORDER BY count DESC
    </select>

    <!-- 查询部门的权限操作统计 -->
    <select id="getDepartmentPermissionStats" resultType="map">
        SELECT department_id, COUNT(*) as count, 
               SUM(CASE WHEN permission_result = 1 THEN 1 ELSE 0 END) as success_count, 
               SUM(CASE WHEN permission_result = 0 THEN 1 ELSE 0 END) as fail_count 
        FROM task_permission_log 
        WHERE department_id = #{departmentId} 
        AND operation_time <![CDATA[ >= ]]> #{startTime} 
        AND operation_time <![CDATA[ <= ]]> #{endTime} 
        GROUP BY department_id
    </select>

    <!-- 查询权限违规操作记录 -->
    <select id="getViolationLogs" resultType="com.cool.modules.task.entity.TaskPermissionLogEntity">
        SELECT * FROM task_permission_log 
        WHERE permission_result = 0 
        AND operation_time <![CDATA[ >= ]]> #{startTime} 
        ORDER BY operation_time DESC 
        LIMIT #{limit}
    </select>

    <!-- 清理过期日志 -->
    <delete id="cleanExpiredLogs">
        DELETE FROM task_permission_log WHERE operation_time <![CDATA[ < ]]> #{expireTime}
    </delete>

</mapper> 
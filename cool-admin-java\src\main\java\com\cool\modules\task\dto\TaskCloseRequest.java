package com.cool.modules.task.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务关闭请求DTO
 */
@Data
@Schema(description = "任务关闭请求")
public class TaskCloseRequest {

    @Schema(description = "任务ID", required = true)
    private Long taskId;

    @Schema(description = "关闭原因", required = true)
    private String closeReason;

    @Schema(description = "操作人ID", required = true)
    private Long operatorId;

    @Schema(description = "操作人姓名")
    private String operatorName;
}

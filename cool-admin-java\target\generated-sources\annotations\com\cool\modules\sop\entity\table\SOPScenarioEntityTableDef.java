package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class SOPScenarioEntityTableDef extends TableDef {

    /**
     * 场景SOP主表实体
 唯一约束：行业ID + 模块编码 + 场景编码 + 版本
     */
    public static final SOPScenarioEntityTableDef SOPSCENARIO_ENTITY = new SOPScenarioEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn STAGE = new QueryColumn(this, "stage");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn VERSION = new QueryColumn(this, "version");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn INDUSTRY_ID = new QueryColumn(this, "industry_id");

    public final QueryColumn MODULE_CODE = new QueryColumn(this, "module_code");

    public final QueryColumn MODULE_NAME = new QueryColumn(this, "module_name");

    public final QueryColumn RISK_POINTS = new QueryColumn(this, "risk_points");

    public final QueryColumn TOTAL_STEPS = new QueryColumn(this, "total_steps");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    public final QueryColumn INDUSTRY_NAME = new QueryColumn(this, "industry_name");

    public final QueryColumn SCENARIO_CODE = new QueryColumn(this, "scenario_code");

    public final QueryColumn SCENARIO_NAME = new QueryColumn(this, "scenario_name");

    public final QueryColumn APPLICABLE_AREA = new QueryColumn(this, "applicable_area");

    public final QueryColumn EXECUTION_COUNT = new QueryColumn(this, "execution_count");

    public final QueryColumn EXECUTION_CYCLE = new QueryColumn(this, "execution_cycle");

    public final QueryColumn ATTENTION_POINTS = new QueryColumn(this, "attention_points");

    public final QueryColumn DIFFICULTY_LEVEL = new QueryColumn(this, "difficulty_level");

    public final QueryColumn QUALITY_STANDARD = new QueryColumn(this, "quality_standard");

    public final QueryColumn SUCCESS_CRITERIA = new QueryColumn(this, "success_criteria");

    public final QueryColumn ESTIMATED_DURATION = new QueryColumn(this, "estimated_duration");

    public final QueryColumn EXECUTION_FREQUENCY = new QueryColumn(this, "execution_frequency");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, STAGE, STATUS, VERSION, CREATE_TIME, INDUSTRY_ID, MODULE_CODE, MODULE_NAME, RISK_POINTS, TOTAL_STEPS, UPDATE_TIME, DESCRIPTION, INDUSTRY_NAME, SCENARIO_CODE, SCENARIO_NAME, APPLICABLE_AREA, EXECUTION_COUNT, EXECUTION_CYCLE, ATTENTION_POINTS, DIFFICULTY_LEVEL, QUALITY_STANDARD, SUCCESS_CRITERIA, ESTIMATED_DURATION, EXECUTION_FREQUENCY};

    public SOPScenarioEntityTableDef() {
        super("", "sop_scenario");
    }

    private SOPScenarioEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SOPScenarioEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SOPScenarioEntityTableDef("", "sop_scenario", alias));
    }

}

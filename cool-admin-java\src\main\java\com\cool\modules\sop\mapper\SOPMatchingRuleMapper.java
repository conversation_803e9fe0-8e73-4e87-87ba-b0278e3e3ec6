package com.cool.modules.sop.mapper;

import com.cool.modules.sop.entity.SOPMatchingRuleEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 匹配规则配置Mapper
 */
@Mapper
public interface SOPMatchingRuleMapper extends BaseMapper<SOPMatchingRuleEntity> {

    /**
     * 查询所有启用的规则
     */
    List<SOPMatchingRuleEntity> selectEnabledRules();

    /**
     * 根据规则键查询规则
     */
    SOPMatchingRuleEntity selectByRuleKey(@Param("ruleKey") String ruleKey);

    /**
     * 更新规则启用状态
     */
    int updateEnabledStatus(@Param("ruleKey") String ruleKey, @Param("isEnabled") Boolean isEnabled);

    /**
     * 更新规则权重
     */
    int updateWeight(@Param("ruleKey") String ruleKey, @Param("weight") Integer weight);

    /**
     * 批量更新规则配置
     */
    int updateBatch(@Param("rules") List<SOPMatchingRuleEntity> rules);
}

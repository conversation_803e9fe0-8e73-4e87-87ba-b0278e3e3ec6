package com.cool.modules.announcement.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class AnnouncementEntityTableDef extends TableDef {

    /**
     * 公示实体
     */
    public static final AnnouncementEntityTableDef ANNOUNCEMENT_ENTITY = new AnnouncementEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn HTML = new QueryColumn(this, "html");

    public final QueryColumn MONTH = new QueryColumn(this, "month");

    public final QueryColumn TITLE = new QueryColumn(this, "title");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn PROJECT_ID = new QueryColumn(this, "project_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, HTML, MONTH, TITLE, STATUS, PROJECT_ID, CREATE_TIME, UPDATE_TIME};

    public AnnouncementEntityTableDef() {
        super("", "announcement");
    }

    private AnnouncementEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public AnnouncementEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new AnnouncementEntityTableDef("", "announcement", alias));
    }

}

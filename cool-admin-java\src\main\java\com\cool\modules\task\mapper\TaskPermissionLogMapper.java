package com.cool.modules.task.mapper;

import com.cool.modules.task.entity.TaskPermissionLogEntity;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务权限日志Mapper
 */
@Mapper
public interface TaskPermissionLogMapper extends BaseMapper<TaskPermissionLogEntity> {

    /**
     * 查询用户的权限操作统计
     */
    List<Map<String, Object>> getUserPermissionStats(@Param("userId") Long userId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询部门的权限操作统计
     */
    Map<String, Object> getDepartmentPermissionStats(@Param("departmentId") Long departmentId,
                                                     @Param("startTime") LocalDateTime startTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询权限违规操作记录
     */
    List<TaskPermissionLogEntity> getViolationLogs(@Param("startTime") LocalDateTime startTime,
                                                   @Param("limit") Integer limit);

    /**
     * 清理过期日志
     */
    int cleanExpiredLogs(@Param("expireTime") LocalDateTime expireTime);
} 
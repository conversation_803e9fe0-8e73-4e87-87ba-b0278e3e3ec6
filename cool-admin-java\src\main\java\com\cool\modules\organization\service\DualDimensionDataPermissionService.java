package com.cool.modules.organization.service;

import com.cool.modules.organization.dto.DataPermissionScopeDTO;
import com.mybatisflex.core.query.QueryWrapper;

import java.util.List;
import java.util.Map;

/**
 * 双维度数据权限服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface DualDimensionDataPermissionService {
    
    /**
     * 获取用户可访问的数据ID列表
     * 
     * @param userId 用户ID
     * @param dataType 数据类型
     * @return 可访问的数据ID列表
     */
    List<Long> getAccessibleDataIds(Long userId, String dataType);
    
    /**
     * 应用数据权限过滤条件
     * 
     * @param queryWrapper 查询包装器
     * @param userId 用户ID
     * @param entityType 实体类型
     */
    void applyDataPermissionFilter(QueryWrapper queryWrapper, Long userId, String entityType);
    
    /**
     * 检查用户是否有数据访问权限
     * 
     * @param userId 用户ID
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 是否有权限
     */
    boolean hasDataAccess(Long userId, String entityType, Long entityId);
    
    /**
     * 批量检查数据访问权限
     * 
     * @param userId 用户ID
     * @param entityType 实体类型
     * @param entityIds 实体ID列表
     * @return 权限检查结果映射
     */
    Map<Long, Boolean> batchCheckDataAccess(Long userId, String entityType, List<Long> entityIds);
    
    /**
     * 获取用户在当前组织形态下的权限范围
     * 
     * @param userId 用户ID
     * @return 权限范围
     */
    DataPermissionScopeDTO getUserPermissionScope(Long userId);
    
    /**
     * 刷新用户权限缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserPermissionCache(Long userId);
    
    /**
     * 清理所有权限缓存
     */
    void clearAllPermissionCache();
    
    /**
     * 检查用户是否为系统管理员
     * 
     * @param userId 用户ID
     * @return 是否为系统管理员
     */
    boolean isSystemAdmin(Long userId);
    
    /**
     * 获取实体的部门ID
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 部门ID
     */
    Long getEntityDepartmentId(String entityType, Long entityId);
    
    /**
     * 获取实体的项目ID
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 项目ID
     */
    Long getEntityProjectId(String entityType, Long entityId);
    
    /**
     * 批量获取实体的组织ID
     * 
     * @param entityType 实体类型
     * @param entityIds 实体ID列表
     * @param organizationMode 组织模式
     * @return 实体ID到组织ID的映射
     */
    Map<Long, Long> batchGetEntityOrganizationIds(String entityType, List<Long> entityIds, String organizationMode);
    
    /**
     * 设置实体的组织归属信息
     * 
     * @param entity 实体对象
     * @param userId 用户ID
     */
    void setEntityOrganizationInfo(Object entity, Long userId);
}

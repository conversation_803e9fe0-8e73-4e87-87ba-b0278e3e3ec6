<template>
  <div class="department-task-list">
    <!-- 部门信息 -->
    <div v-if="department.departmentName" class="department-info">
      <h4>{{ department.departmentName }}</h4>
      <div class="department-stats">
        <el-tag type="info" size="small">
          共{{ department.tasks?.length || 0 }}个任务
        </el-tag>
        <el-tag type="success" size="small">
          已分配{{ assignedCount }}个
        </el-tag>
        <el-tag type="warning" size="small">
          待分配{{ unassignedCount }}个
        </el-tag>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="tasks-section">
      <div class="tasks-list">
        <div 
          v-for="(task, index) in department.tasks" 
          :key="index"
          class="task-item"
        >
          <el-card>
            <div class="task-header">
              <div class="task-title">
                <h5>{{ task.taskName || task.name }}</h5>
                <el-tag 
                  :type="getPriorityType(task.priority)" 
                  size="small"
                >
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="task-actions">
                <el-button 
                  @click="handleAdjustAssignment(index, task)"
                  type="primary" 
                  size="small"
                  :disabled="!task.canReassign"
                >
                  👤 调整分配
                </el-button>
              </div>
            </div>

            <div class="task-content">
              <div class="task-description">
                <p>{{ task.description || task.taskDescription }}</p>
              </div>

              <div class="task-details">
                <div class="detail-row">
                  <span class="label">步骤:</span>
                  <span>{{ task.stepName }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">角色:</span>
                  <span>{{ task.employeeRole }}</span>
                </div>
                <div class="detail-row">
                  <span class="label">预估时长:</span>
                  <span>{{ task.estimatedDuration || 30 }}分钟</span>
                </div>
              </div>

              <!-- 执行人分配信息 -->
              <div class="assignment-info">
                <div v-if="task.isAssigned" class="assigned">
                  <div class="assignee-info">
                    <el-avatar :size="32">
                      {{ task.assigneeName?.charAt(0) || '?' }}
                    </el-avatar>
                    <div class="assignee-details">
                      <span class="assignee-name">{{ task.assigneeName }}</span>
                      <span class="assignee-role">{{ task.assigneeRole }}</span>
                    </div>
                  </div>
                  <div class="assignment-meta">
                    <el-tag 
                      :type="getAssignmentStatusType(task.assignmentStatus)"
                      size="small"
                    >
                      {{ getAssignmentStatusText(task.assignmentStatus) }}
                    </el-tag>
                    <span class="confidence">
                      置信度: {{ task.assignmentConfidence }}%
                    </span>
                  </div>
                  <div v-if="task.assignmentReason" class="assignment-reason">
                    <span class="reason-label">分配原因:</span>
                    <span class="reason-text">{{ task.assignmentReason }}</span>
                  </div>
                </div>
                <div v-else class="unassigned">
                  <el-alert 
                    title="未分配执行人" 
                    type="warning" 
                    :closable="false"
                    show-icon
                  >
                    <template #default>
                      <div class="unassigned-actions">
                        <span>此任务尚未分配执行人</span>
                        <el-button 
                          @click="handleAdjustAssignment(index, task)"
                          type="primary" 
                          size="small"
                        >
                          立即分配
                        </el-button>
                      </div>
                    </template>
                  </el-alert>
                </div>
              </div>

              <!-- 候选人列表 -->
              <div v-if="task.candidates && task.candidates.length > 0" class="candidates-section">
                <h6>推荐候选人</h6>
                <div class="candidates-list">
                  <div 
                    v-for="candidate in task.candidates.slice(0, 3)" 
                    :key="candidate.userId"
                    class="candidate-item"
                    @click="handleQuickAssign(index, candidate)"
                  >
                    <el-avatar :size="24">
                      {{ candidate.userName?.charAt(0) || '?' }}
                    </el-avatar>
                    <span class="candidate-name">{{ candidate.userName }}</span>
                    <span class="candidate-score">{{ candidate.score || candidate.confidence }}分</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineEmits } from 'vue'

// Props
const props = defineProps({
  department: {
    type: Object,
    required: true
  },
  availableUsers: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['adjust-assignment', 'quick-assign', 'preview-updated'])

// 计算属性
const assignedCount = computed(() => {
  return props.department.tasks?.filter(task => task.isAssigned).length || 0
})

const unassignedCount = computed(() => {
  return props.department.tasks?.filter(task => !task.isAssigned).length || 0
})

// 方法
const handleAdjustAssignment = (taskIndex, task) => {
  emit('adjust-assignment', {
    taskIndex,
    departmentId: props.department.departmentId,
    task
  })
}

const handleQuickAssign = (taskIndex, candidate) => {
  emit('quick-assign', {
    taskIndex,
    departmentId: props.department.departmentId,
    candidate
  })
}

// 工具方法
const getPriorityType = (priority) => {
  const types = { 1: 'info', 2: 'info', 3: 'warning', 4: 'danger', 5: 'danger' }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = { 1: '低', 2: '较低', 3: '中', 4: '较高', 5: '高' }
  return texts[priority] || '中'
}

const getAssignmentStatusType = (status) => {
  const types = { 'SUCCESS': 'success', 'FAILED': 'danger', 'PENDING': 'warning' }
  return types[status] || 'info'
}

const getAssignmentStatusText = (status) => {
  const texts = { 'SUCCESS': '已分配', 'FAILED': '分配失败', 'PENDING': '待分配' }
  return texts[status] || '未知'
}
</script>

<style scoped>
.department-task-list {
  padding: 0;
}

.department-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.department-info h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.department-stats {
  display: flex;
  gap: 10px;
}

.tasks-section {
  margin-top: 10px;
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-item {
  border-radius: 8px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-title h5 {
  margin: 0;
  color: #303133;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-description p {
  margin: 0;
  color: #606266;
  line-height: 1.5;
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  gap: 10px;
}

.detail-row .label {
  font-weight: 500;
  color: #909399;
  min-width: 80px;
}

.assignment-info {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  background-color: #fafafa;
}

.assigned {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.assignee-details {
  display: flex;
  flex-direction: column;
}

.assignee-name {
  font-weight: 500;
  color: #303133;
}

.assignee-role {
  font-size: 12px;
  color: #909399;
}

.assignment-meta {
  display: flex;
  align-items: center;
  gap: 15px;
}

.confidence {
  font-size: 12px;
  color: #606266;
}

.assignment-reason {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.reason-label {
  color: #909399;
  font-weight: 500;
}

.reason-text {
  color: #606266;
}

.unassigned-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.candidates-section {
  margin-top: 10px;
}

.candidates-section h6 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.candidates-list {
  display: flex;
  gap: 10px;
}

.candidate-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.candidate-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.candidate-name {
  font-size: 12px;
  color: #303133;
}

.candidate-score {
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
}
</style> 
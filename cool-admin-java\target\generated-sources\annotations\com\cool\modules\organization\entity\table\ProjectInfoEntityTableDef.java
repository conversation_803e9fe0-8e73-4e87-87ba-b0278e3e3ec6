package com.cool.modules.organization.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class ProjectInfoEntityTableDef extends TableDef {

    /**
     * 项目信息实体
 
 <AUTHOR> Admin
 @since 2025-01-17
     */
    public static final ProjectInfoEntityTableDef PROJECT_INFO_ENTITY = new ProjectInfoEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 项目标签（JSON格式）
     */
    public final QueryColumn TAGS = new QueryColumn(this, "tags");

    /**
     * 项目预算
     */
    public final QueryColumn BUDGET = new QueryColumn(this, "budget");

    /**
     * 备注
     */
    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    /**
     * 项目状态
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 是否启用
     */
    public final QueryColumn ENABLED = new QueryColumn(this, "enabled");

    /**
     * 项目负责人ID
     */
    public final QueryColumn OWNER_ID = new QueryColumn(this, "owner_id");

    /**
     * 排序号
     */
    public final QueryColumn ORDER_NUM = new QueryColumn(this, "order_num");

    /**
     * 项目优先级
     */
    public final QueryColumn PRIORITY = new QueryColumn(this, "priority");

    /**
     * 创建人ID
     */
    public final QueryColumn CREATOR_ID = new QueryColumn(this, "creator_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 项目附件
     */
    public final QueryColumn ATTACHMENTS = new QueryColumn(this, "attachments");

    /**
     * 项目描述
     */
    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 项目编码
     */
    public final QueryColumn PROJECT_CODE = new QueryColumn(this, "project_code");

    /**
     * 项目名称
     */
    public final QueryColumn PROJECT_NAME = new QueryColumn(this, "project_name");

    /**
     * 实际结束时间
     */
    public final QueryColumn ACTUAL_END_TIME = new QueryColumn(this, "actual_end_time");

    /**
     * 计划结束时间
     */
    public final QueryColumn PLANNED_END_TIME = new QueryColumn(this, "planned_end_time");

    /**
     * 实际开始时间
     */
    public final QueryColumn ACTUAL_START_TIME = new QueryColumn(this, "actual_start_time");

    /**
     * 计划开始时间
     */
    public final QueryColumn PLANNED_START_TIME = new QueryColumn(this, "planned_start_time");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, TAGS, BUDGET, REMARK, STATUS, ENABLED, OWNER_ID, ORDER_NUM, PRIORITY, CREATOR_ID, CREATE_TIME, UPDATE_TIME, ATTACHMENTS, DESCRIPTION, PROJECT_CODE, PROJECT_NAME, ACTUAL_END_TIME, PLANNED_END_TIME, ACTUAL_START_TIME, PLANNED_START_TIME};

    public ProjectInfoEntityTableDef() {
        super("", "org_project_info");
    }

    private ProjectInfoEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public ProjectInfoEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new ProjectInfoEntityTableDef("", "org_project_info", alias));
    }

}

package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class TaskExecuteRecordEntityTableDef extends TableDef {

    /**
     * 任务执行记录实体
     */
    public static final TaskExecuteRecordEntityTableDef TASK_EXECUTE_RECORD_ENTITY = new TaskExecuteRecordEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn PROGRESS = new QueryColumn(this, "progress");

    public final QueryColumn TASK_NAME = new QueryColumn(this, "task_name");

    public final QueryColumn SOP_STEP_ID = new QueryColumn(this, "sop_step_id");

    public final QueryColumn ACTUAL_TIME = new QueryColumn(this, "actual_time");

    public final QueryColumn AI_GUIDANCE = new QueryColumn(this, "ai_guidance");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn EXECUTOR_ID = new QueryColumn(this, "executor_id");

    public final QueryColumn SKILL_MATCH = new QueryColumn(this, "skill_match");

    public final QueryColumn SKIP_REASON = new QueryColumn(this, "skip_reason");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn AI_RISK_ALERT = new QueryColumn(this, "ai_risk_alert");

    public final QueryColumn ATTACHMENTS = new QueryColumn(this, "attachments");

    public final QueryColumn REWORK_COUNT = new QueryColumn(this, "rework_count");

    public final QueryColumn WORK_ORDER_ID = new QueryColumn(this, "work_order_id");

    public final QueryColumn AI_PREDICTION = new QueryColumn(this, "ai_prediction");

    public final QueryColumn EXECUTOR_NAME = new QueryColumn(this, "executor_name");

    public final QueryColumn QUALITY_SCORE = new QueryColumn(this, "quality_score");

    public final QueryColumn REWORK_REASON = new QueryColumn(this, "rework_reason");

    public final QueryColumn ACTUAL_END_TIME = new QueryColumn(this, "actual_end_time");

    public final QueryColumn ESTIMATED_TIME = new QueryColumn(this, "estimated_time");

    public final QueryColumn EXCEPTION_INFO = new QueryColumn(this, "exception_info");

    public final QueryColumn PLANNED_END_TIME = new QueryColumn(this, "planned_end_time");

    public final QueryColumn ACTUAL_START_TIME = new QueryColumn(this, "actual_start_time");

    public final QueryColumn EXECUTION_RESULT = new QueryColumn(this, "execution_result");

    public final QueryColumn TASK_DESCRIPTION = new QueryColumn(this, "task_description");

    public final QueryColumn DIFFICULTY_RATING = new QueryColumn(this, "difficulty_rating");

    public final QueryColumn EXECUTION_PROCESS = new QueryColumn(this, "execution_process");

    public final QueryColumn PLANNED_START_TIME = new QueryColumn(this, "planned_start_time");

    public final QueryColumn EXCEPTION_HANDLING = new QueryColumn(this, "exception_handling");

    public final QueryColumn QUALITY_CHECK_RESULT = new QueryColumn(this, "quality_check_result");

    public final QueryColumn SATISFACTION_RATING = new QueryColumn(this, "satisfaction_rating");

    public final QueryColumn IMPROVEMENT_SUGGESTION = new QueryColumn(this, "improvement_suggestion");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, REMARK, STATUS, PROGRESS, TASK_NAME, SOP_STEP_ID, ACTUAL_TIME, AI_GUIDANCE, CREATE_TIME, EXECUTOR_ID, SKILL_MATCH, SKIP_REASON, UPDATE_TIME, AI_RISK_ALERT, ATTACHMENTS, REWORK_COUNT, WORK_ORDER_ID, AI_PREDICTION, EXECUTOR_NAME, QUALITY_SCORE, REWORK_REASON, ACTUAL_END_TIME, ESTIMATED_TIME, EXCEPTION_INFO, PLANNED_END_TIME, ACTUAL_START_TIME, EXECUTION_RESULT, TASK_DESCRIPTION, DIFFICULTY_RATING, EXECUTION_PROCESS, PLANNED_START_TIME, EXCEPTION_HANDLING, QUALITY_CHECK_RESULT, SATISFACTION_RATING, IMPROVEMENT_SUGGESTION};

    public TaskExecuteRecordEntityTableDef() {
        super("", "sop_task_execute_record");
    }

    private TaskExecuteRecordEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public TaskExecuteRecordEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new TaskExecuteRecordEntityTableDef("", "sop_task_execute_record", alias));
    }

}

<template>
  <div class="ai-task-preview-enhanced">
    <!-- 预览头部 -->
    <div class="preview-header">
      <h3>AI任务预览</h3>
      <div class="preview-actions">
        <el-button @click="refreshPreview" :loading="loading">
          🔄 刷新预览
        </el-button>
        <el-button 
          type="primary" 
          @click="acceptPreview" 
          :loading="accepting"
          :disabled="!hasPreviewData"
        >
          ✅ 接受预览并生成任务
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 预览内容 -->
    <div v-else-if="hasPreviewData">
      <!-- 多部门概览 -->
      <div v-if="isMultiDepartment" class="department-overview">
        <el-card class="overview-card">
          <template #header>
            <div class="card-header">
              <span>📊 任务生成概览</span>
            </div>
          </template>
          
          <div class="overview-stats">
            <div class="stat-item">
              <div class="stat-number">{{ departmentSummary.totalDepartments }}</div>
              <div class="stat-label">涉及部门</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ departmentSummary.totalTasks }}</div>
              <div class="stat-label">生成任务</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ assignedTasksCount }}</div>
              <div class="stat-label">已分配任务</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ unassignedTasksCount }}</div>
              <div class="stat-label">待分配任务</div>
            </div>
          </div>

          <!-- 场景信息 -->
          <div class="scenario-info">
            <h4>📋 匹配场景</h4>
            <div class="scenario-details">
              <h5>{{ departmentSummary.scenario?.name }}</h5>
              <p>{{ departmentSummary.scenario?.description }}</p>
              <el-tag size="small">{{ departmentSummary.scenario?.code }}</el-tag>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 单部门场景信息 -->
      <div v-else-if="singleDepartmentData" class="scenario-info">
        <h4>📋 匹配场景</h4>
        <div class="scenario-details">
          <h5>{{ singleDepartmentData.scenario?.name }}</h5>
          <p>{{ singleDepartmentData.scenario?.description }}</p>
          <el-tag size="small">{{ singleDepartmentData.scenario?.code }}</el-tag>
        </div>
      </div>

      <!-- 部门任务展示 -->
      <div class="departments-section">
        <!-- 多部门Tab切换 -->
        <div v-if="isMultiDepartment">
          <el-tabs v-model="activeTab" type="card" class="department-tabs">
            <el-tab-pane 
              v-for="(dept, index) in departmentData" 
              :key="dept.departmentId || index"
              :label="getDepartmentTabLabel(dept)"
              :name="String(dept.departmentId || index)"
            >
              <DepartmentTaskList 
                :department="dept"
                :available-users="availableUsers"
                @adjust-assignment="adjustAssignment"
                @quick-assign="quickAssign"
                @preview-updated="$emit('preview-updated')"
              />
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 单部门任务列表 -->
        <div v-else-if="singleDepartmentData">
          <DepartmentTaskList 
            :department="singleDepartmentData"
            :available-users="availableUsers"
            @adjust-assignment="adjustAssignment"
            @quick-assign="quickAssign"
            @preview-updated="$emit('preview-updated')"
          />
        </div>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data">
      <el-empty description="暂无预览数据" />
    </div>

    <!-- 分配调整对话框 -->
    <el-dialog
      v-model="assignmentDialogVisible"
      title="调整任务分配"
      width="500px"
      class="assignment-dialog"
    >
      <div v-if="currentTask">
        <div class="current-task-info">
          <h5>{{ currentTask.taskName }}</h5>
          <p>{{ currentTask.description }}</p>
        </div>

        <el-form :model="assignmentForm" label-width="80px">
          <el-form-item label="执行人">
            <el-select 
              v-model="assignmentForm.assigneeId" 
              placeholder="请选择执行人"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="user in availableUsers"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              >
                <div class="user-option">
                  <span>{{ user.name }}</span>
                  <span class="user-role">{{ user.role }}</span>
                  <span class="user-workload">负载: {{ user.workload || 0 }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="调整原因">
            <el-input
              v-model="assignmentForm.reason"
              type="textarea"
              placeholder="请输入调整原因（可选）"
              :rows="3"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assignmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAssignment" :loading="adjusting">
            确认调整
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { service } from '/@/cool'
import DepartmentTaskList from './DepartmentTaskList.vue'

// Props
const props = defineProps({
  recordId: {
    type: [String, Number],
    required: true
  }
})

// Emits
const emit = defineEmits(['preview-accepted', 'preview-updated'])

// 响应式数据
const loading = ref(false)
const accepting = ref(false)
const adjusting = ref(false)
const previewData = ref(null)
const availableUsers = ref([])
const assignmentDialogVisible = ref(false)
const currentTask = ref(null)
const currentTaskIndex = ref(-1)
const currentDepartmentId = ref(null)
const activeTab = ref('0')

// 分配表单
const assignmentForm = ref({
  assigneeId: null,
  reason: ''
})

// 计算属性
const hasPreviewData = computed(() => {
  return previewData.value && (
    (previewData.value.multiDepartment && previewData.value.departments?.length > 0) ||
    (!previewData.value.multiDepartment && previewData.value.tasks?.length > 0)
  )
})

const isMultiDepartment = computed(() => {
  return previewData.value?.multiDepartment === true
})

const departmentData = computed(() => {
  if (!isMultiDepartment.value) return []
  return previewData.value?.departments || []
})

const singleDepartmentData = computed(() => {
  if (isMultiDepartment.value) return null
  return previewData.value
})

const departmentSummary = computed(() => {
  if (!isMultiDepartment.value) return null
  return previewData.value?.summary || {}
})

const assignedTasksCount = computed(() => {
  if (isMultiDepartment.value) {
    return departmentData.value.reduce((count, dept) => {
      return count + (dept.tasks?.filter(task => task.isAssigned).length || 0)
    }, 0)
  } else if (singleDepartmentData.value) {
    return singleDepartmentData.value.tasks?.filter(task => task.isAssigned).length || 0
  }
  return 0
})

const unassignedTasksCount = computed(() => {
  if (isMultiDepartment.value) {
    return departmentData.value.reduce((count, dept) => {
      return count + (dept.tasks?.filter(task => !task.isAssigned).length || 0)
    }, 0)
  } else if (singleDepartmentData.value) {
    return singleDepartmentData.value.tasks?.filter(task => !task.isAssigned).length || 0
  }
  return 0
})

// 方法
const getDepartmentTabLabel = (dept) => {
  const taskCount = dept.tasks?.length || 0
  const assignedCount = dept.tasks?.filter(task => task.isAssigned).length || 0
  return `${dept.departmentName || '未知部门'} (${assignedCount}/${taskCount})`
}

// 获取预览数据
const fetchPreviewData = async () => {
  try {
    loading.value = true
    const response = await service.sop.aiTaskGenerator.getTaskRecord(props.recordId)
    if (response.success && response.data?.result) {
      previewData.value = JSON.parse(response.data.result)
      
      // 设置默认活跃Tab
      if (isMultiDepartment.value && departmentData.value.length > 0) {
        activeTab.value = String(departmentData.value[0].departmentId || 0)
      }
    }
  } catch (error) {
    ElMessage.error('获取预览数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 获取可用执行人列表
const fetchAvailableUsers = async () => {
  try {
    const response = await service.sop.aiTaskGenerator.getAvailableAssignees()
    if (response.success) {
      availableUsers.value = response.data
    }
  } catch (error) {
    console.error('获取可用执行人失败:', error)
  }
}

// 刷新预览
const refreshPreview = () => {
  fetchPreviewData()
}

// 接受预览并生成任务
const acceptPreview = async () => {
  try {
    accepting.value = true
    const response = await service.sop.aiTaskGenerator.acceptPreview(props.recordId)
    if (response.success) {
      ElMessage.success('任务生成已开始，请查看任务列表')
      emit('preview-accepted', response.data)
    }
  } catch (error) {
    ElMessage.error('接受预览失败: ' + error.message)
  } finally {
    accepting.value = false
  }
}

// 调整分配
const adjustAssignment = (params) => {
  const { taskIndex, departmentId, task } = params
  currentTaskIndex.value = taskIndex
  currentDepartmentId.value = departmentId
  currentTask.value = task
  assignmentForm.value.assigneeId = task.assigneeId
  assignmentForm.value.reason = ''
  assignmentDialogVisible.value = true
}

// 快速分配
const quickAssign = async (params) => {
  const { taskIndex, departmentId, candidate } = params
  try {
    adjusting.value = true
    const response = await service.sop.aiTaskGenerator.adjustPreviewAssignment({
      recordId: props.recordId,
      taskIndex: taskIndex,
      newAssigneeId: candidate.userId,
      reason: `选择推荐候选人: ${candidate.userName} (评分: ${candidate.score})`
    })
    
    if (response.success) {
      ElMessage.success('分配调整成功')
      await fetchPreviewData()
      emit('preview-updated')
    }
  } catch (error) {
    ElMessage.error('分配调整失败: ' + error.message)
  } finally {
    adjusting.value = false
  }
}

// 确认分配
const confirmAssignment = async () => {
  if (!assignmentForm.value.assigneeId) {
    ElMessage.warning('请选择执行人')
    return
  }

  try {
    adjusting.value = true
    const response = await service.sop.aiTaskGenerator.adjustPreviewAssignment({
      recordId: props.recordId,
      taskIndex: currentTaskIndex.value,
      newAssigneeId: assignmentForm.value.assigneeId,
      reason: assignmentForm.value.reason || '手动调整分配'
    })
    
    if (response.success) {
      ElMessage.success('分配调整成功')
      assignmentDialogVisible.value = false
      await fetchPreviewData()
      emit('preview-updated')
    }
  } catch (error) {
    ElMessage.error('分配调整失败: ' + error.message)
  } finally {
    adjusting.value = false
  }
}

// 组件挂载
onMounted(() => {
  fetchPreviewData()
  fetchAvailableUsers()
})
</script>

<style scoped>
.ai-task-preview-enhanced {
  padding: 20px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-header h3 {
  margin: 0;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

/* 部门概览样式 */
.department-overview {
  margin-bottom: 20px;
}

.overview-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.overview-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.scenario-info {
  margin-bottom: 20px;
}

.scenario-info h4 {
  margin: 0 0 10px 0;
  color: #606266;
}

.scenario-details h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.scenario-details p {
  margin: 0 0 10px 0;
  color: #606266;
}

/* 部门Tab样式 */
.departments-section {
  margin-top: 20px;
}

.department-tabs {
  border-radius: 8px;
}

.department-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
}

.department-tabs :deep(.el-tabs__item) {
  padding: 10px 20px;
  font-weight: 500;
}

.department-tabs :deep(.el-tabs__item.is-active) {
  color: #409eff;
}

/* 对话框样式 */
.assignment-dialog {
  padding: 10px 0;
}

.current-task-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.current-task-info h5 {
  margin: 0 0 8px 0;
  color: #303133;
}

.current-task-info p {
  margin: 0;
  color: #606266;
}

.user-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.user-role {
  font-size: 12px;
  color: #909399;
}

.user-workload {
  font-size: 12px;
  color: #f56c6c;
}

.no-data {
  text-align: center;
  padding: 40px;
}
</style> 
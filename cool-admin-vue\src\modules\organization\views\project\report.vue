<template>
  <div class="project-report">
    <!-- 报表头部 -->
    <div class="report-header">
      <div class="header-left">
        <h2>项目报表分析</h2>
        <p>项目进度、任务统计和团队效率分析</p>
      </div>
      
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
        />
        
        <el-button type="primary" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon projects">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.totalProjects }}</div>
                <div class="card-label">总项目数</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.projectGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon tasks">
                <el-icon><List /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.totalTasks }}</div>
                <div class="card-label">总任务数</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.taskGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon members">
                <el-icon><User /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.totalMembers }}</div>
                <div class="card-label">团队成员</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.memberGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon efficiency">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ overviewData.efficiency }}%</div>
                <div class="card-label">完成效率</div>
                <div class="card-trend">
                  <el-icon class="trend-icon up"><ArrowUp /></el-icon>
                  <span>+{{ overviewData.efficiencyGrowth }}%</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 项目进度图表 -->
        <el-col :span="12">
          <el-card title="项目进度分析">
            <template #header>
              <div class="chart-header">
                <span>项目进度分析</span>
                <el-select v-model="progressChartType" size="small" style="width: 120px;">
                  <el-option label="饼图" value="pie" />
                  <el-option label="柱状图" value="bar" />
                </el-select>
              </div>
            </template>
            
            <div ref="progressChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
        
        <!-- 任务状态分布 -->
        <el-col :span="12">
          <el-card title="任务状态分布">
            <div ref="taskStatusChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 团队效率趋势 -->
        <el-col :span="24">
          <el-card title="团队效率趋势">
            <template #header>
              <div class="chart-header">
                <span>团队效率趋势</span>
                <el-radio-group v-model="trendPeriod" size="small">
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="quarter">季度</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            
            <div ref="trendChartRef" class="chart-container large"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-tables">
      <el-row :gutter="20">
        <!-- 项目排行榜 -->
        <el-col :span="12">
          <el-card title="项目完成度排行">
            <el-table :data="projectRanking" size="small">
              <el-table-column prop="rank" label="排名" width="60" align="center" />
              <el-table-column prop="projectName" label="项目名称" show-overflow-tooltip />
              <el-table-column prop="progress" label="完成度" width="100" align="center">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="row.progress" 
                    :show-text="false"
                    :stroke-width="6"
                  />
                  <span style="margin-left: 8px;">{{ row.progress }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        
        <!-- 成员效率排行 -->
        <el-col :span="12">
          <el-card title="成员效率排行">
            <el-table :data="memberRanking" size="small">
              <el-table-column prop="rank" label="排名" width="60" align="center" />
              <el-table-column prop="memberName" label="成员姓名" show-overflow-tooltip />
              <el-table-column prop="completedTasks" label="完成任务" width="80" align="center" />
              <el-table-column prop="efficiency" label="效率分" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="getEfficiencyType(row.efficiency)" size="small">
                    {{ row.efficiency }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Download, 
  FolderOpened, 
  List, 
  User, 
  TrendCharts,
  ArrowUp
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import { useCool } from '/@/cool';

defineOptions({
  name: "project-report",
});

const { service } = useCool();

// 响应式数据
const dateRange = ref([]);
const progressChartType = ref('pie');
const trendPeriod = ref('month');

// 图表引用
const progressChartRef = ref();
const taskStatusChartRef = ref();
const trendChartRef = ref();

// 图表实例
let progressChart: echarts.ECharts | null = null;
let taskStatusChart: echarts.ECharts | null = null;
let trendChart: echarts.ECharts | null = null;

// 概览数据
const overviewData = reactive({
  totalProjects: 0,
  totalTasks: 0,
  totalMembers: 0,
  efficiency: 0,
  projectGrowth: 0,
  taskGrowth: 0,
  memberGrowth: 0,
  efficiencyGrowth: 0
});

// 排行榜数据
const projectRanking = ref([]);
const memberRanking = ref([]);

// 方法
const handleDateRangeChange = (dates: string[]) => {
  loadReportData();
};

const getEfficiencyType = (efficiency: number) => {
  if (efficiency >= 90) return 'success';
  if (efficiency >= 70) return 'warning';
  return 'danger';
};

const exportReport = async () => {
  try {
    if (service.organization?.project?.report?.export) {
      const response = await service.organization.project.report.export({
        dateRange: dateRange.value
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.download = `项目报表_${new Date().toLocaleDateString()}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);

      ElMessage.success('报表导出成功');
    } else {
      console.warn('报表导出服务不可用');
      ElMessage.info('报表导出功能暂不可用');
    }
  } catch (error) {
    console.warn('报表导出失败:', error);
    ElMessage.error('报表导出失败');
  }
};

const loadReportData = async () => {
  try {
    const params = {
      dateRange: dateRange.value,
      period: trendPeriod.value
    };
    
    // 加载概览数据
    const overviewRes = await service.organization.project.report.overview(params);
    Object.assign(overviewData, overviewRes);
    
    // 加载排行榜数据
    const rankingRes = await service.organization.project.report.ranking(params);
    projectRanking.value = rankingRes.projects;
    memberRanking.value = rankingRes.members;
    
    // 更新图表
    updateCharts();
  } catch (error) {
    ElMessage.error('加载报表数据失败');
  }
};

const initCharts = () => {
  // 初始化项目进度图表
  progressChart = echarts.init(progressChartRef.value);
  
  // 初始化任务状态图表
  taskStatusChart = echarts.init(taskStatusChartRef.value);
  
  // 初始化趋势图表
  trendChart = echarts.init(trendChartRef.value);
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    progressChart?.resize();
    taskStatusChart?.resize();
    trendChart?.resize();
  });
};

const updateCharts = async () => {
  try {
    // 获取图表数据
    const chartData = await service.organization.project.report.charts({
      dateRange: dateRange.value,
      progressType: progressChartType.value,
      trendPeriod: trendPeriod.value
    });
    
    // 更新项目进度图表
    updateProgressChart(chartData.progress);
    
    // 更新任务状态图表
    updateTaskStatusChart(chartData.taskStatus);
    
    // 更新趋势图表
    updateTrendChart(chartData.trend);
  } catch (error) {
    console.error('更新图表失败:', error);
  }
};

const updateProgressChart = (data: any) => {
  const option = progressChartType.value === 'pie' ? {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '项目进度',
      type: 'pie',
      radius: '70%',
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  } : {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.map((item: any) => item.name)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: '项目数量',
      type: 'bar',
      data: data.map((item: any) => item.value),
      itemStyle: {
        color: '#409eff'
      }
    }]
  };
  
  progressChart?.setOption(option);
};

const updateTaskStatusChart = (data: any) => {
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [{
      name: '任务状态',
      type: 'doughnut',
      radius: ['40%', '70%'],
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  
  taskStatusChart?.setOption(option);
};

const updateTrendChart = (data: any) => {
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['完成任务', '新增任务', '团队效率']
    },
    xAxis: {
      type: 'category',
      data: data.dates
    },
    yAxis: [{
      type: 'value',
      name: '任务数量'
    }, {
      type: 'value',
      name: '效率(%)',
      position: 'right'
    }],
    series: [
      {
        name: '完成任务',
        type: 'bar',
        data: data.completed,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '新增任务',
        type: 'bar',
        data: data.created,
        itemStyle: { color: '#409eff' }
      },
      {
        name: '团队效率',
        type: 'line',
        yAxisIndex: 1,
        data: data.efficiency,
        itemStyle: { color: '#e6a23c' }
      }
    ]
  };
  
  trendChart?.setOption(option);
};

onMounted(async () => {
  // 设置默认日期范围（最近30天）
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);
  
  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ];
  
  await nextTick();
  initCharts();
  loadReportData();
});
</script>

<style lang="scss" scoped>
.project-report {
  padding: 20px;
  
  .report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-left {
      h2 {
        margin: 0 0 4px 0;
        color: var(--el-text-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--el-text-color-regular);
        font-size: 14px;
      }
    }
    
    .header-right {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  
  .overview-cards {
    margin-bottom: 24px;
    
    .overview-card {
      .card-content {
        display: flex;
        align-items: center;
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          color: white;
          
          &.projects {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          }
          
          &.tasks {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          }
          
          &.members {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          }
          
          &.efficiency {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
          }
        }
        
        .card-info {
          flex: 1;
          
          .card-value {
            font-size: 28px;
            font-weight: bold;
            color: var(--el-text-color-primary);
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .card-label {
            font-size: 14px;
            color: var(--el-text-color-regular);
            margin-bottom: 8px;
          }
          
          .card-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--el-color-success);
            
            .trend-icon {
              font-size: 14px;
              
              &.up {
                color: var(--el-color-success);
              }
              
              &.down {
                color: var(--el-color-danger);
              }
            }
          }
        }
      }
    }
  }
  
  .charts-section {
    margin-bottom: 24px;
    
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .chart-container {
      height: 300px;
      
      &.large {
        height: 400px;
      }
    }
  }
  
  .data-tables {
    .el-table {
      .el-progress {
        width: 60px;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .project-report {
    padding: 16px;
    
    .report-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      
      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }
    
    .overview-cards {
      .el-col {
        margin-bottom: 16px;
      }
    }
    
    .charts-section {
      .el-col {
        margin-bottom: 20px;
      }
    }
    
    .data-tables {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}
</style>

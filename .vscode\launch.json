{"version": "0.2.0", "configurations": [{"type": "java", "name": "Debug Cool Admin Java (UTF-8 Fixed)", "request": "launch", "mainClass": "com.cool.CoolApplication", "projectName": "cool-admin", "vmArgs": ["-Dfile.encoding=UTF-8", "-Dsun.jnu.encoding=UTF-8", "-Dsun.io.useCanonPrefixCache=false", "-Djava.awt.headless=true", "-Dspring.output.ansi.enabled=ALWAYS", "-Dspring.profiles.active=local", "-Dserver.port=8001", "-Dconsole.encoding=UTF-8", "-Dlogging.charset.console=UTF-8", "-Dlogging.charset.file=UTF-8", "-Djan<PERSON>.force=true", "-<PERSON><PERSON><PERSON>.passthrough=true", "-XX:+UseG1GC", "-Xms512m", "-Xmx1024m"], "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8", "LANG": "zh_CN.UTF-8", "LC_ALL": "zh_CN.UTF-8"}, "console": "integratedTerminal", "encoding": "UTF-8", "preLaunchTask": "compile-java-utf8"}]}
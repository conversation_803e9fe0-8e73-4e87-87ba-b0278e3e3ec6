{"version": "0.2.0", "configurations": [{"type": "java", "name": "CoolApplication", "request": "launch", "mainClass": "com.cool.CoolApplication", "projectName": "cool-admin"}, {"type": "java", "name": "Launch Cool Admin Java", "request": "launch", "mainClass": "com.cool.CoolApplication", "projectName": "cool-admin", "vmArgs": ["-Dfile.encoding=UTF-8", "-Dspring.profiles.active=local", "-Dserver.port=8001", "-Dsun.jnu.encoding=UTF-8", "-Dconsole.encoding=UTF-8", "-Dlogging.charset.console=UTF-8", "-Dlogging.charset.file=UTF-8", "-Djan<PERSON>.force=true", "-Dspring.output.ansi.enabled=ALWAYS"], "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8", "LANG": "zh_CN.UTF-8", "LC_ALL": "zh_CN.UTF-8"}, "console": "integratedTerminal", "encoding": "UTF-8"}, {"type": "java", "name": "Debug Cool Admin Java (UTF-8)", "request": "launch", "mainClass": "com.cool.CoolApplication", "projectName": "cool-admin", "vmArgs": ["-Dfile.encoding=UTF-8", "-Dsun.jnu.encoding=UTF-8", "-Dspring.output.ansi.enabled=ALWAYS", "-Dspring.profiles.active=local", "-Dserver.port=8001", "-Dconsole.encoding=UTF-8", "-Dlogging.charset.console=UTF-8", "-Dlogging.charset.file=UTF-8", "-Djan<PERSON>.force=true", "-<PERSON><PERSON><PERSON>.passthrough=true"], "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8", "LANG": "zh_CN.UTF-8", "LC_ALL": "zh_CN.UTF-8", "CHCP": "65001"}, "console": "integratedTerminal", "encoding": "UTF-8"}]}
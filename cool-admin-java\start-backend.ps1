# Cool Admin Java 后端服务启动脚本 (PowerShell)
# 解决VSCode终端乱码问题

Write-Host "=== Cool Admin Java 后端服务启动脚本 ===" -ForegroundColor Green

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 设置环境变量
$env:SPRING_PROFILES_ACTIVE = "local"
$env:SERVER_PORT = "8001"
$env:JAVA_TOOL_OPTIONS = "-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"

Write-Host "当前目录: $(Get-Location)" -ForegroundColor Yellow

# 检查是否有已编译的jar包
$jarFile = Get-ChildItem -Path "target" -Filter "*.jar" -Exclude "*sources.jar" | Select-Object -First 1

if ($jarFile -and (Test-Path $jarFile.FullName)) {
    Write-Host "找到JAR文件: $($jarFile.FullName)" -ForegroundColor Green
    Write-Host "启动Java应用..." -ForegroundColor Yellow
    
    # 启动应用
    Start-Process -FilePath "java" -ArgumentList "-Dfile.encoding=UTF-8", "-Dspring.output.ansi.enabled=ALWAYS", "-jar", $jarFile.FullName -NoNewWindow
    
    Write-Host "✅ Java应用已启动" -ForegroundColor Green
    Write-Host "应用将在端口8001运行" -ForegroundColor Yellow
    
} else {
    Write-Host "未找到JAR文件，尝试使用Maven启动..." -ForegroundColor Yellow
    
    # 检查Maven是否可用
    if (Get-Command mvn -ErrorAction SilentlyContinue) {
        Write-Host "使用Maven启动开发服务器..." -ForegroundColor Yellow
        
        # 启动Maven
        mvn spring-boot:run -Dspring-boot.run.profiles=local -Dspring-boot.run.jvmArguments="-Dfile.encoding=UTF-8 -Dspring.output.ansi.enabled=ALWAYS"
        
        Write-Host "✅ Maven开发服务器已启动" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到Maven，请先编译项目:" -ForegroundColor Red
        Write-Host "   mvn clean package -DskipTests" -ForegroundColor Yellow
        Write-Host "   然后重新运行此脚本" -ForegroundColor Yellow
        exit 1
    }
}

# 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# 检查服务状态
Write-Host "检查服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8001/admin/base/open/eps" -UseBasicParsing -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端服务启动成功，EPS接口可访问" -ForegroundColor Green
        Write-Host "🌐 服务地址: http://localhost:8001" -ForegroundColor Cyan
        Write-Host "📋 EPS接口: http://localhost:8001/admin/base/open/eps" -ForegroundColor Cyan
    }
} catch {
    Write-Host "⚠️  服务可能还在启动中，请稍等片刻后检查" -ForegroundColor Yellow
    Write-Host "🔍 可以通过以下命令检查日志:" -ForegroundColor Yellow
    Write-Host "   查看控制台输出" -ForegroundColor Yellow
}

Write-Host "=== 启动完成 ===" -ForegroundColor Green 
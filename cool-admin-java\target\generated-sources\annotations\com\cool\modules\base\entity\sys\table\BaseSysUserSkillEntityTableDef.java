package com.cool.modules.base.entity.sys.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class BaseSysUserSkillEntityTableDef extends TableDef {

    /**
     * 用户技能实体
     */
    public static final BaseSysUserSkillEntityTableDef BASE_SYS_USER_SKILL_ENTITY = new BaseSysUserSkillEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    public final QueryColumn SKILL_NAME = new QueryColumn(this, "skill_name");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn SKILL_LEVEL = new QueryColumn(this, "skill_level");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, USER_ID, SKILL_NAME, CREATE_TIME, SKILL_LEVEL, UPDATE_TIME, DESCRIPTION};

    public BaseSysUserSkillEntityTableDef() {
        super("", "base_sys_user_skill");
    }

    private BaseSysUserSkillEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public BaseSysUserSkillEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new BaseSysUserSkillEntityTableDef("", "base_sys_user_skill", alias));
    }

}

import { ref, computed, nextTick } from 'vue';

interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  expiry: number;
  key: string;
}

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  apiCalls: number;
  cacheHits: number;
  cacheMisses: number;
}

/**
 * 缓存管理器
 */
class CacheManager {
  private cache = new Map<string, CacheItem>();
  private maxSize = 100;
  private defaultTTL = 5 * 60 * 1000; // 5分钟

  /**
   * 设置缓存
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const expiry = Date.now() + (ttl || this.defaultTTL);
    
    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry,
      key
    });
  }

  /**
   * 获取缓存
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data as T;
  }

  /**
   * 删除缓存
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    const now = Date.now();
    const validItems = Array.from(this.cache.values()).filter(item => now <= item.expiry);
    
    return {
      total: this.cache.size,
      valid: validItems.length,
      expired: this.cache.size - validItems.length,
      size: this.cache.size,
      maxSize: this.maxSize
    };
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    loadTime: 0,
    renderTime: 0,
    apiCalls: 0,
    cacheHits: 0,
    cacheMisses: 0
  };

  private timers = new Map<string, number>();

  /**
   * 开始计时
   */
  startTimer(name: string): void {
    this.timers.set(name, performance.now());
  }

  /**
   * 结束计时
   */
  endTimer(name: string): number {
    const startTime = this.timers.get(name);
    if (!startTime) return 0;
    
    const duration = performance.now() - startTime;
    this.timers.delete(name);
    
    return duration;
  }

  /**
   * 记录API调用
   */
  recordApiCall(): void {
    this.metrics.apiCalls++;
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit(): void {
    this.metrics.cacheHits++;
  }

  /**
   * 记录缓存未命中
   */
  recordCacheMiss(): void {
    this.metrics.cacheMisses++;
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 重置指标
   */
  reset(): void {
    this.metrics = {
      loadTime: 0,
      renderTime: 0,
      apiCalls: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
    this.timers.clear();
  }

  /**
   * 获取缓存命中率
   */
  getCacheHitRate(): number {
    const total = this.metrics.cacheHits + this.metrics.cacheMisses;
    return total > 0 ? (this.metrics.cacheHits / total) * 100 : 0;
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 延迟加载工具
 */
export function lazyLoad<T>(
  loader: () => Promise<T>,
  fallback?: T
): {
  data: T | null;
  loading: boolean;
  error: Error | null;
  load: () => Promise<void>;
} {
  const data = ref<T | null>(fallback || null);
  const loading = ref(false);
  const error = ref<Error | null>(null);

  const load = async () => {
    if (loading.value) return;
    
    loading.value = true;
    error.value = null;
    
    try {
      data.value = await loader();
    } catch (err) {
      error.value = err as Error;
    } finally {
      loading.value = false;
    }
  };

  return {
    data: data.value,
    loading: loading.value,
    error: error.value,
    load
  };
}

/**
 * 虚拟滚动工具
 */
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const scrollTop = ref(0);
  
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return { start, end };
  });
  
  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value;
    return items.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      top: (start + index) * itemHeight
    }));
  });
  
  const totalHeight = computed(() => items.length * itemHeight);
  
  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    scrollTop.value = target.scrollTop;
  };
  
  return {
    visibleItems,
    totalHeight,
    handleScroll
  };
}

/**
 * 批量更新工具
 */
export function batchUpdate<T>(
  updates: (() => void)[],
  batchSize = 10
): Promise<void> {
  return new Promise((resolve) => {
    let index = 0;
    
    const processBatch = async () => {
      const batch = updates.slice(index, index + batchSize);
      
      for (const update of batch) {
        update();
      }
      
      index += batchSize;
      
      if (index < updates.length) {
        await nextTick();
        requestAnimationFrame(processBatch);
      } else {
        resolve();
      }
    };
    
    processBatch();
  });
}

/**
 * 内存使用监控
 */
export function getMemoryUsage() {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
    };
  }
  
  return null;
}

// 全局实例
export const cacheManager = new CacheManager();
export const performanceMonitor = new PerformanceMonitor();

/**
 * 性能优化组合式函数
 */
export function usePerformanceOptimization() {
  // 定期清理缓存
  setInterval(() => {
    cacheManager.cleanup();
  }, 60000); // 每分钟清理一次

  return {
    cacheManager,
    performanceMonitor,
    debounce,
    throttle,
    lazyLoad,
    useVirtualScroll,
    batchUpdate,
    getMemoryUsage
  };
}

/**
 * API缓存装饰器
 */
export function withCache<T extends (...args: any[]) => Promise<any>>(
  apiFunction: T,
  cacheKey: string,
  ttl?: number
): T {
  return (async (...args: Parameters<T>) => {
    const key = `${cacheKey}_${JSON.stringify(args)}`;
    
    // 尝试从缓存获取
    const cached = cacheManager.get(key);
    if (cached) {
      performanceMonitor.recordCacheHit();
      return cached;
    }
    
    // 缓存未命中，调用API
    performanceMonitor.recordCacheMiss();
    performanceMonitor.recordApiCall();
    
    try {
      const result = await apiFunction(...args);
      cacheManager.set(key, result, ttl);
      return result;
    } catch (error) {
      throw error;
    }
  }) as T;
}

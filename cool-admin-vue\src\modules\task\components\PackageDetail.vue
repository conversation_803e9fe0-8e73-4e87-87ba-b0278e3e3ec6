<template>
  <div class="package-detail">
    <!-- 任务包概览 -->
    <div class="package-overview">
      <div class="overview-header">
        <h3>{{ package.packageName }}</h3>
        <div class="overview-actions">
          <el-button type="primary" @click="batchAssignAll">
            <el-icon><Setting /></el-icon>
            批量智能分配
          </el-button>
          <el-button @click="refreshTasks">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
      
      <div class="overview-stats">
        <div class="stat-card">
          <div class="stat-number">{{ package.totalTasks || 0 }}</div>
          <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-card completed">
          <div class="stat-number">{{ package.completedTasks || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-card in-progress">
          <div class="stat-number">{{ package.inProgressTasks || 0 }}</div>
          <div class="stat-label">进行中</div>
        </div>
        <div class="stat-card pending">
          <div class="stat-number">{{ package.pendingTasks || 0 }}</div>
          <div class="stat-label">待分配</div>
        </div>
      </div>

      <div class="overall-progress">
        <div class="progress-label">
          <span>整体进度</span>
          <span class="progress-text">{{ package.completionRate || 0 }}%</span>
        </div>
        <el-progress 
          :percentage="package.completionRate || 0" 
          :stroke-width="12"
          :color="getProgressColor(package.completionRate || 0)"
        />
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="section-header">
        <h4>任务列表</h4>
        <div class="list-actions">
          <el-button 
            size="small" 
            @click="selectAllTasks"
            :disabled="tasks.length === 0"
          >
            {{ selectedTasks.length === tasks.length ? '取消全选' : '全选' }}
          </el-button>
          <el-button 
            type="primary" 
            size="small"
            @click="batchAssignSelected"
            :disabled="selectedTasks.length === 0"
          >
            分配选中 ({{ selectedTasks.length }})
          </el-button>
        </div>
      </div>

      <div class="task-list">
        <div 
          v-for="task in tasks" 
          :key="task.id"
          class="task-item"
          :class="{ 'selected': selectedTasks.includes(task.id) }"
        >
          <div class="task-checkbox">
            <el-checkbox 
              :model-value="selectedTasks.includes(task.id)"
              @change="toggleTaskSelection(task.id)"
            />
          </div>

          <div class="task-content">
            <div class="task-header">
              <div class="task-title">
                <h5>{{ task.name }}</h5>
                <el-tag :type="getTaskStatusType(task.taskStatus)" size="small">
                  {{ getTaskStatusLabel(task.taskStatus) }}
                </el-tag>
              </div>
              <div class="task-meta">
                <span v-if="task.stepName" class="step-name">{{ task.stepName }}</span>
                <span v-if="task.employeeRole" class="employee-role">{{ task.employeeRole }}</span>
              </div>
            </div>

            <div v-if="task.description" class="task-description">
              {{ task.description }}
            </div>

            <!-- 执行人信息 -->
            <div v-if="task.executions && task.executions.length > 0" class="task-assignees">
              <div class="assignees-label">执行人:</div>
              <div class="assignees-list">
                <el-tag 
                  v-for="execution in task.executions" 
                  :key="execution.id"
                  :type="getExecutionStatusType(execution.executionStatus)"
                  size="small"
                >
                  {{ execution.assigneeName }}
                </el-tag>
              </div>
            </div>

            <div class="task-time">
              <span v-if="task.createTime" class="time-item">
                创建: {{ formatTime(task.createTime) }}
              </span>
              <span v-if="task.startTime" class="time-item">
                开始: {{ formatTime(task.startTime) }}
              </span>
              <span v-if="task.endTime" class="time-item">
                结束: {{ formatTime(task.endTime) }}
              </span>
            </div>
          </div>

          <div class="task-actions">
            <el-button
              size="small"
              type="primary"
              @click="assignSingleTask(task)"
              :disabled="task.taskStatus === 3"
            >
              <el-icon><Setting /></el-icon>
              智能分配
            </el-button>
            <el-button 
              size="small"
              @click="viewTaskDetail(task)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="tasks.length === 0" description="暂无任务数据" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Setting, Refresh, View } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  package: {
    type: Object,
    required: true
  },
  tasks: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits([
  'task-updated',
  'assign-task',
  'batch-assign'
]);

// 响应式数据
const selectedTasks = ref([]);

// 切换任务选择状态
const toggleTaskSelection = (taskId) => {
  const index = selectedTasks.value.indexOf(taskId);
  if (index > -1) {
    selectedTasks.value.splice(index, 1);
  } else {
    selectedTasks.value.push(taskId);
  }
};

// 全选/取消全选
const selectAllTasks = () => {
  if (selectedTasks.value.length === props.tasks.length) {
    selectedTasks.value = [];
  } else {
    selectedTasks.value = props.tasks.map(task => task.id);
  }
};

// 批量分配所有任务
const batchAssignAll = () => {
  const taskIds = props.tasks.map(task => task.id);
  emit('batch-assign', taskIds);
};

// 批量分配选中任务
const batchAssignSelected = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要分配的任务');
    return;
  }
  emit('batch-assign', selectedTasks.value);
};

// 分配单个任务
const assignSingleTask = (task) => {
  emit('assign-task', task.id);
};

// 查看任务详情
const viewTaskDetail = (task) => {
  ElMessage.info('任务详情功能开发中...');
};

// 刷新任务列表
const refreshTasks = () => {
  emit('task-updated');
};

// 获取任务状态类型
const getTaskStatusType = (status) => {
  const types = {
    0: 'warning',  // 待分配
    1: 'info',     // 已分配
    2: 'primary',  // 执行中
    3: 'success',  // 已完成
    4: 'info'      // 已关闭
  };
  return types[status] || 'default';
};

// 获取任务状态标签
const getTaskStatusLabel = (status) => {
  const labels = {
    0: '待分配',
    1: '已分配',
    2: '执行中',
    3: '已完成',
    4: '已关闭'
  };
  return labels[status] || '未知';
};

// 获取执行状态类型
const getExecutionStatusType = (status) => {
  const types = {
    'ASSIGNED': 'primary',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'CANCELLED': 'info'
  };
  return types[status] || 'default';
};

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 30) return '#f56c6c';
  if (percentage < 70) return '#e6a23c';
  return '#67c23a';
};

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};
</script>

<style lang="scss" scoped>
.package-detail {
  .package-overview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 24px;
    color: white;
    margin-bottom: 24px;
    
    .overview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }
      
      .overview-actions {
        display: flex;
        gap: 12px;
      }
    }
    
    .overview-stats {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      margin-bottom: 20px;
      
      .stat-card {
        text-align: center;
        padding: 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        
        .stat-number {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 12px;
          opacity: 0.8;
        }
        
        &.completed .stat-number {
          color: #67c23a;
        }
        
        &.in-progress .stat-number {
          color: #409eff;
        }
        
        &.pending .stat-number {
          color: #e6a23c;
        }
      }
    }
    
    .overall-progress {
      .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 14px;
        
        .progress-text {
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
  }
  
  .task-list-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }
      
      .list-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .task-list {
      .task-item {
        display: flex;
        align-items: flex-start;
        padding: 16px;
        margin-bottom: 12px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        border: 1px solid #ebeef5;
        
        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        &.selected {
          border-color: #409eff;
          background: #f0f9ff;
        }
        
        .task-checkbox {
          margin-right: 12px;
          margin-top: 4px;
        }
        
        .task-content {
          flex: 1;
          
          .task-header {
            margin-bottom: 8px;
            
            .task-title {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;
              
              h5 {
                margin: 0;
                color: #303133;
                font-size: 14px;
                font-weight: 600;
              }
            }
            
            .task-meta {
              display: flex;
              gap: 8px;
              
              .step-name, .employee-role {
                font-size: 12px;
                color: #909399;
                background: #f5f7fa;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }
          }
          
          .task-description {
            color: #606266;
            font-size: 13px;
            line-height: 1.5;
            margin-bottom: 8px;
          }
          
          .task-assignees {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            
            .assignees-label {
              font-size: 12px;
              color: #909399;
            }
            
            .assignees-list {
              display: flex;
              gap: 4px;
            }
          }
          
          .task-time {
            display: flex;
            gap: 12px;
            
            .time-item {
              font-size: 11px;
              color: #909399;
            }
          }
        }
        
        .task-actions {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }
      }
    }
  }
}
</style>

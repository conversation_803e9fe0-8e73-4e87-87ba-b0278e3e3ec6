<template>
  <el-scrollbar>
    <div class="sun-fund-income-expense-page">
      <el-card class="main-card">
        <div class="title">物业服务资金收支情况表（酬金制）</div>
        <el-row :gutter="16" class="section-row">
          <el-col :xs="24" :md="12">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="物业服务区域">{{ baseInfo.area }}</el-descriptions-item>
              <el-descriptions-item label="物业服务人（盖章）">{{ baseInfo.provider }}</el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <el-card shadow="never" class="sub-card mt24">
          <el-table :data="tableData" border style="width: 100%">
            <el-table-column prop="project" label="项目" />
            <el-table-column prop="serial" label="序列" width="70" align="center" />
            <el-table-column prop="budget" label="预算 (元)" width="120" align="center" />
            <el-table-column prop="current" label="当季 (元)" width="120" align="center" />
            <el-table-column prop="total" label="累计 (元)" width="120" align="center" />
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </el-card>
        <el-row class="footer-row">
          <el-col :xs="24" :md="8"><span class="footer-label">制表：</span></el-col>
          <el-col :xs="24" :md="8"><span class="footer-label">审核：</span></el-col>
          <el-col :xs="24" :md="8"><span class="footer-label">日期：</span></el-col>
        </el-row>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

defineOptions({
  name: 'sun-fund-income-expense'
});

// 基础信息（动态数据接口预留）
const baseInfo = ref({
  area: '',
  provider: ''
});

// 表格数据（动态数据接口预留）
const tableData = ref([
  { project: '1.预收物业服务资金收入', serial: '1', budget: '', current: '', total: '', remark: '' },
  { project: '1.1住宅', serial: '2', budget: '', current: '', total: '', remark: '' },
  { project: '1.2商业', serial: '3', budget: '', current: '', total: '', remark: '' },
  { project: '1.3其他业态', serial: '4', budget: '', current: '', total: '', remark: '' },
  { project: '1.4车位', serial: '5', budget: '', current: '', total: '', remark: '' },
  { project: '1.5其他', serial: '6', budget: '', current: '', total: '', remark: '' },
  { project: '2.物业服务支出', serial: '7', budget: '', current: '', total: '', remark: '' },
  { project: '2.1物业服务人员费用', serial: '8', budget: '', current: '', total: '', remark: '' },
  { project: '2.1.1人员工资', serial: '9', budget: '', current: '', total: '', remark: '' },
  { project: '2.1.2社会保险费用', serial: '10', budget: '', current: '', total: '', remark: '' },
  { project: '2.1.3住房公积金', serial: '11', budget: '', current: '', total: '', remark: '' },
  { project: '2.1.4福利费', serial: '12', budget: '', current: '', total: '', remark: '' },
  { project: '2.1.5其他（工会经费、职工教育经费等）', serial: '13', budget: '', current: '', total: '', remark: '' },
  { project: '2.2共用部位、共用设施设备日常运行和维护费用', serial: '14', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.1电梯系统', serial: '15', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.2消防系统', serial: '16', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.3二次供水', serial: '17', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.4供配电(发电机)', serial: '18', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.5监控系统', serial: '19', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.6日常维修', serial: '20', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.7设施设备运行及公共区域能耗', serial: '21', budget: '', current: '', total: '', remark: '' },
  { project: '2.2.8工具耗材', serial: '22', budget: '', current: '', total: '', remark: '' },
  { project: '2.3.1外包服务费', serial: '24', budget: '', current: '', total: '', remark: '' },
  { project: '2.3.2消杀防疫', serial: '25', budget: '', current: '', total: '', remark: '' },
  { project: '2.3.3化粪池（排污管网）清理', serial: '26', budget: '', current: '', total: '', remark: '' },
  { project: '2.3.4大件垃圾清运', serial: '27', budget: '', current: '', total: '', remark: '' },
  { project: '2.3.5工具耗材', serial: '28', budget: '', current: '', total: '', remark: '' },
  { project: '2.4绿化养护费用', serial: '29', budget: '', current: '', total: '', remark: '' },
  { project: '2.4.1外包服务费', serial: '30', budget: '', current: '', total: '', remark: '' },
  { project: '2.4.2绿化垃圾清运', serial: '31', budget: '', current: '', total: '', remark: '' },
  { project: '2.4.3补苗', serial: '32', budget: '', current: '', total: '', remark: '' },
  { project: '2.4.4工具耗材药剂', serial: '33', budget: '', current: '', total: '', remark: '' },
  { project: '2.5秩序维护费用', serial: '34', budget: '', current: '', total: '', remark: '' },
  { project: '2.5.1外包服务费', serial: '35', budget: '', current: '', total: '', remark: '' },
  { project: '2.5.2工具耗材', serial: '36', budget: '', current: '', total: '', remark: '' },
  { project: '2.6办公费用', serial: '37', budget: '', current: '', total: '', remark: '' },
  { project: '2.7管理费分摊', serial: '38', budget: '', current: '', total: '', remark: '' },
  { project: '2.8固定资产折旧', serial: '39', budget: '', current: '', total: '', remark: '' },
  { project: '2.9保险费用', serial: '40', budget: '', current: '', total: '', remark: '' },
  { project: '2.9.1公众责任保险', serial: '41', budget: '', current: '', total: '', remark: '' },
  { project: '2.9.2 ××保险', serial: '42', budget: '', current: '', total: '', remark: '' },
  { project: '2.10税金及附加', serial: '43', budget: '', current: '', total: '', remark: '' },
  { project: '2.11经业主同意的其他支出', serial: '44', budget: '', current: '', total: '', remark: '' },
  { project: '3.物业服务人酬金', serial: '45', budget: '', current: '', total: '', remark: '' },
  { project: '4.物业服务资金结余 (当期)', serial: '46', budget: '', current: '', total: '', remark: '' },
  { project: '5.物业服务资金结余 (往期)', serial: '47', budget: '', current: '', total: '', remark: '' },
  { project: '6.物业服务资金结余 (累计)', serial: '48', budget: '', current: '', total: '', remark: '' },
]);

// 说明内容（完全按主题定制）
const usageNotes = [
  '本表适用于酬金制收费方式公开物业服务资金的收支情况。物业服务资金收支情况应以资金预算为基础，至少每半年公开一次。',
  '物业服务资金结余（当期）序列 46 = 1 - 7 - 45；物业服务资金结余（累计）序列 48 = 46 + 47。',
  '各项费用应在“备注”栏内列明具体收支依据、标准，支出与预算差异较大的，应在 “备注”栏内予以说明。',
  '本模板未罗列穷尽的其他收入及支出项应当在本表中合适位置增加栏目予以公开。除非业主共同决定，物业服务支出项不应超出物业服务合同约定范围。',
  '本表应当由物业项目经理审核签字，并加盖公章后向业主公开。',
  '本模板及其列举项目、填写范例供参考使用，物业服务企业可结合实际作相应调整。',
  '公开的文本应保持清晰醒目、便于阅览，字号以小四或12号及以上为宜。',
];
</script>

<style scoped>
/*
  主题色适配说明
  - 页面主题色：#e67e22（橙色，资金/流转/透明）
  - 变量 --theme-color：主色，用于标题、分区、表格头等
  - 变量 --theme-bg：分区背景色
  - 支持自动适配 dark/light 主题
  - 如需切换主题色，仅需修改 .sun-fund-income-expense-page 内的变量
*/
.sun-fund-income-expense-page {
  --theme-color: #e67e22;
  --theme-bg: #fff7ec;
  --card-bg: #fff;
  --text-color: #222;
  --table-th-bg: var(--theme-color);
  --table-th-color: #fff;
  background: #f8f9fa;
  min-height: 100vh;
  padding: 24px 0;
  color: var(--text-color);
}
.main-card {
  max-width: 1100px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(80,120,200,0.08);
  background: var(--card-bg);
  padding: 32px 18px 24px 18px;
}
.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--theme-color);
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 2px;
  border-bottom: 3px solid var(--theme-color);
  padding-bottom: 8px;
}
.section-row {
  margin-bottom: 24px;
}
.sub-card {
  border-radius: 12px;
  margin-bottom: 0;
  background: var(--theme-bg);
  border-left: 4px solid var(--theme-color);
}
.sub-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--theme-color);
  margin-bottom: 16px;
}
.mt24 {
  margin-top: 24px;
}
.note-alert {
  margin-bottom: 8px;
}
.el-table th {
  background: var(--table-th-bg) !important;
  color: var(--table-th-color) !important;
}
.footer-row {
  margin-top: 24px;
  text-align: center;
  color: #888;
  font-size: 15px;
}
.footer-label {
  margin-right: 12px;
}
@media (max-width: 900px) {
  .main-card { padding: 12px 2px; }
  .title { font-size: 1.3rem; }
}
@media (max-width: 600px) {
  .main-card { padding: 2px 0; }
  .title { font-size: 1.1rem; }
}
@media (prefers-color-scheme: dark) {
  .sun-fund-income-expense-page {
    --theme-color: #ffb155;
    --theme-bg: #2a211a;
    --card-bg: #2b231e;
    --text-color: #f3ede6;
    --table-th-bg: #e67e22;
    --table-th-color: #fff;
    background: #181511;
  }
  .main-card {
    box-shadow: 0 4px 24px rgba(120,80,40,0.18);
  }
}
</style> 
package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class SOPSchedulePermissionEntityTableDef extends TableDef {

    /**
     * 调度权限配置实体
     */
    public static final SOPSchedulePermissionEntityTableDef SOPSCHEDULE_PERMISSION_ENTITY = new SOPSchedulePermissionEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn ROLE_ID = new QueryColumn(this, "role_id");

    public final QueryColumn IS_ENABLED = new QueryColumn(this, "is_enabled");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn PERMISSION_TYPE = new QueryColumn(this, "permission_type");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, ROLE_ID, IS_ENABLED, CREATE_TIME, UPDATE_TIME, PERMISSION_TYPE};

    public SOPSchedulePermissionEntityTableDef() {
        super("", "sop_schedule_permission");
    }

    private SOPSchedulePermissionEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SOPSchedulePermissionEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SOPSchedulePermissionEntityTableDef("", "sop_schedule_permission", alias));
    }

}

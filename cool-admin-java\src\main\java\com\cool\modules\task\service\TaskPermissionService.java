package com.cool.modules.task.service;

/**
 * 任务权限服务接口
 */
public interface TaskPermissionService {

    /**
     * 检查用户是否可以完成指定任务的执行
     * @param taskId 任务ID
     * @param assigneeId 执行人ID
     * @param currentUserId 当前用户ID
     * @return 是否有权限
     */
    Boolean canCompleteTaskExecution(Long taskId, Long assigneeId, Long currentUserId);

    /**
     * 检查用户是否可以强制完成任务
     * @param taskId 任务ID
     * @param operatorId 操作人ID
     * @return 是否有权限
     */
    Boolean canForceCompleteTask(Long taskId, Long operatorId);

    /**
     * 检查用户是否可以关闭任务
     * @param taskId 任务ID
     * @param operatorId 操作人ID
     * @return 是否有权限
     */
    Boolean canCloseTask(Long taskId, Long operatorId);

    /**
     * 检查用户是否可以重新开启任务
     * @param taskId 任务ID
     * @param operatorId 操作人ID
     * @return 是否有权限
     */
    Boolean canReopenTask(Long taskId, Long operatorId);

    /**
     * 检查用户是否是管理员
     * @param userId 用户ID
     * @return 是否是管理员
     */
    Boolean isAdmin(Long userId);

    /**
     * 检查用户是否是任务负责人
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否是任务负责人
     */
    Boolean isTaskOwner(Long taskId, Long userId);

    /**
     * 检查用户是否是项目经理
     * @param userId 用户ID
     * @return 是否是项目经理
     */
    Boolean isProjectManager(Long userId);
}

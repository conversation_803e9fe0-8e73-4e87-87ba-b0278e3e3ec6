package com.cool.modules.organization.controller.admin;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.request.R;
import com.cool.modules.organization.service.DualDimensionMigrationService;
import com.cool.modules.organization.service.DualDimensionMigrationService.MigrationResult;
import com.cool.modules.organization.service.DualDimensionMigrationService.MigrationStatus;
import com.cool.modules.organization.service.DualDimensionMigrationService.ValidationResult;

import cn.hutool.core.lang.Dict;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * 双维度权限数据迁移控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "双维度权限数据迁移", description = "数据迁移、验证、回滚等功能")
@CoolRestController
@RequiredArgsConstructor
public class AdminDualDimensionMigrationController {
    
    private final DualDimensionMigrationService migrationService;
    
    @PostMapping("/execute")
    @Operation(summary = "执行完整数据迁移")
    public R<Object> executeFullMigration() {
        MigrationResult result = migrationService.executeFullMigration();
        
        return R.ok(Dict.create()
            .set("success", result.isSuccess())
            .set("message", result.getMessage())
            .set("workOrderMigrated", result.getWorkOrderMigrated())
            .set("taskPackageProcessed", result.getTaskPackageProcessed())
            .set("taskInfoProcessed", result.getTaskInfoProcessed())
            .set("taskExecutionProcessed", result.getTaskExecutionProcessed())
            .set("executionTime", result.getExecutionTime()));
    }
    
    @PostMapping("/migrate-work-orders")
    @Operation(summary = "迁移工单部门数据")
    public R<Object> migrateWorkOrderDepartments() {
        try {
            int migratedCount = migrationService.migrateWorkOrderDepartments();
            
            return R.ok(Dict.create()
                .set("success", true)
                .set("message", "工单部门数据迁移成功")
                .set("migratedCount", migratedCount));

        } catch (Exception e) {
            return R.ok(Dict.create()
                .set("success", false)
                .set("message", "工单部门数据迁移失败: " + e.getMessage()));
        }
    }
    
    @PostMapping("/initialize-project")
    @Operation(summary = "初始化项目维度数据")
    public R<Object> initializeProjectDimension() {
        try {
            boolean success = migrationService.initializeProjectDimension();
            
            return R.ok(Dict.create()
                .set("success", success)
                .set("message", success ? "项目维度数据初始化成功" : "项目维度数据初始化失败"));

        } catch (Exception e) {
            return R.ok(Dict.create()
                .set("success", false)
                .set("message", "项目维度数据初始化失败: " + e.getMessage()));
        }
    }
    
    @GetMapping("/validate")
    @Operation(summary = "验证数据一致性")
    public Dict validateDataConsistency() {
        ValidationResult result = migrationService.validateDataConsistency();
        
        return Dict.create()
            .set("valid", result.isValid())
            .set("message", result.getMessage())
            .set("taskPackageIssues", result.getTaskPackageIssues())
            .set("taskInfoIssues", result.getTaskInfoIssues())
            .set("taskExecutionIssues", result.getTaskExecutionIssues())
            .set("workOrderIssues", result.getWorkOrderIssues())
            .set("totalIssues", result.getTaskPackageIssues() + result.getTaskInfoIssues() 
                              + result.getTaskExecutionIssues() + result.getWorkOrderIssues());
    }
    
    @PostMapping("/rollback")
    @Operation(summary = "回滚数据迁移")
    public Dict rollbackMigration() {
        try {
            boolean success = migrationService.rollbackMigration();
            
            return Dict.create()
                .set("success", success)
                .set("message", success ? "数据迁移回滚成功" : "数据迁移回滚失败");
                
        } catch (Exception e) {
            return Dict.create()
                .set("success", false)
                .set("message", "数据迁移回滚失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/status")
    @Operation(summary = "获取迁移状态")
    public Dict getMigrationStatus() {
        MigrationStatus status = migrationService.getMigrationStatus();
        
        return Dict.create()
            .set("status", status.name())
            .set("description", status.getDescription())
            .set("timestamp", System.currentTimeMillis());
    }
    
    @GetMapping("/health-check")
    @Operation(summary = "健康检查")
    public Dict healthCheck() {
        try {
            // 执行快速的数据一致性检查
            ValidationResult validation = migrationService.validateDataConsistency();
            MigrationStatus status = migrationService.getMigrationStatus();
            
            boolean healthy = validation.isValid() && 
                             (status == MigrationStatus.COMPLETED || status == MigrationStatus.NOT_STARTED);
            
            return Dict.create()
                .set("healthy", healthy)
                .set("migrationStatus", status.name())
                .set("dataConsistent", validation.isValid())
                .set("message", healthy ? "系统状态正常" : "系统存在问题")
                .set("details", Dict.create()
                    .set("migrationCompleted", status == MigrationStatus.COMPLETED)
                    .set("dataValid", validation.isValid())
                    .set("totalIssues", validation.getTaskPackageIssues() + validation.getTaskInfoIssues() 
                                      + validation.getTaskExecutionIssues() + validation.getWorkOrderIssues())
                );
                
        } catch (Exception e) {
            return Dict.create()
                .set("healthy", false)
                .set("message", "健康检查失败: " + e.getMessage());
        }
    }
}

package com.cool.modules.organization.controller.admin;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.core.util.CoolSecurityUtil;
import com.cool.modules.organization.entity.ProjectInfoEntity;
import com.cool.modules.organization.service.DualDimensionDataPermissionService;
import com.cool.modules.organization.service.ProjectInfoService;

import cn.hutool.core.lang.Dict;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 项目信息管理控制器
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Tag(name = "项目信息管理", description = "项目CRUD、项目统计等功能")
@CoolRestController(api = {"add", "delete", "update", "page", "list", "info"})
@RequiredArgsConstructor
public class AdminProjectInfoController extends BaseController<ProjectInfoService, ProjectInfoEntity> {

    private final DualDimensionDataPermissionService permissionService;

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 应用双维度权限过滤
        Long userId = CoolSecurityUtil.getCurrentUserId();
        // 这里可以设置一些基础的查询选项
    }
    
    @PostMapping("/create")
    @Operation(summary = "创建项目")
    public R<ProjectInfoEntity> createProject(@Valid @RequestBody ProjectInfoEntity project) {
        // 设置组织归属信息
        Long userId = CoolSecurityUtil.getCurrentUserId();
        permissionService.setEntityOrganizationInfo(project, userId);

        boolean success = service.createProject(project);

        if (success) {
            return R.ok(project);
        } else {
            return R.error("项目创建失败");
        }
    }
    
    @GetMapping("/my-projects")
    @Operation(summary = "获取我的项目列表")
    public R<List<ProjectInfoEntity>> getMyProjects() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        List<ProjectInfoEntity> projects = service.getUserProjects(userId);
        
        return R.ok(projects);
    }
    
    @GetMapping("/by-owner/{ownerId}")
    @Operation(summary = "根据负责人获取项目列表")
    public R<List<ProjectInfoEntity>> getProjectsByOwner(@PathVariable Long ownerId) {
        List<ProjectInfoEntity> projects = service.getByOwnerId(ownerId);
        
        return R.ok(projects);
    }
    
    @GetMapping("/by-creator/{creatorId}")
    @Operation(summary = "根据创建人获取项目列表")
    public R<List<ProjectInfoEntity>> getProjectsByCreator(@PathVariable Long creatorId) {
        List<ProjectInfoEntity> projects = service.getByCreatorId(creatorId);
        
        return R.ok(projects);
    }
    
    @GetMapping("/detail/{projectId}")
    @Operation(summary = "获取项目详情（包含统计信息）")
    public R<ProjectInfoEntity> getProjectDetail(@PathVariable Long projectId) {
        // 检查权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectAccess(userId, projectId)) {
            return R.error("无权限访问此项目");
        }
        
        ProjectInfoEntity project = service.getProjectWithStats(projectId);
        
        if (project != null) {
            return R.ok(project);
        } else {
            return R.error("项目不存在");
        }
    }
    
    @PutMapping("/status/{projectId}")
    @Operation(summary = "更新项目状态")
    public R<String> updateProjectStatus(@PathVariable Long projectId, 
                                        @RequestParam Integer status) {
        // 检查权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectAccess(userId, projectId)) {
            return R.error("无权限操作此项目");
        }
        
        boolean success = service.updateStatus(projectId, status);
        
        if (success) {
            return R.ok("项目状态更新成功");
        } else {
            return R.error("项目状态更新失败");
        }
    }
    
    @PutMapping("/batch-status")
    @Operation(summary = "批量更新项目状态")
    public R<String> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> projectIds = (List<Long>) params.get("projectIds");
        Integer status = (Integer) params.get("status");
        
        if (projectIds == null || projectIds.isEmpty() || status == null) {
            return R.error("参数不能为空");
        }
        
        int successCount = service.batchUpdateStatus(projectIds, status);
        
        return R.ok("成功更新 " + successCount + " 个项目状态");
    }
    
    @DeleteMapping("/soft-delete/{projectId}")
    @Operation(summary = "软删除项目")
    public R<String> softDeleteProject(@PathVariable Long projectId) {
        // 检查权限
        Long userId = CoolSecurityUtil.getCurrentUserId();
        if (!service.hasProjectAccess(userId, projectId)) {
            return R.error("无权限删除此项目");
        }
        
        boolean success = service.deleteProject(projectId);
        
        if (success) {
            return R.ok("项目删除成功");
        } else {
            return R.error("项目删除失败");
        }
    }
    
    @GetMapping("/check-code/{projectCode}")
    @Operation(summary = "检查项目编码是否可用")
    public R<Dict> checkProjectCode(@PathVariable String projectCode, 
                                   @RequestParam(required = false) Long excludeId) {
        boolean exists = service.existsByProjectCode(projectCode, excludeId);
        
        return R.ok(Dict.create()
            .set("projectCode", projectCode)
            .set("exists", exists)
            .set("available", !exists)
        );
    }
    
    @GetMapping("/statistics")
    @Operation(summary = "获取项目统计信息")
    public R<Dict> getProjectStatistics() {
        Long userId = CoolSecurityUtil.getCurrentUserId();
        
        // 获取用户参与的项目
        List<ProjectInfoEntity> userProjects = service.getUserProjects(userId);
        
        // 统计各状态项目数量
        long planningCount = userProjects.stream().filter(p -> p.getStatus() == 0).count();
        long inProgressCount = userProjects.stream().filter(p -> p.getStatus() == 1).count();
        long completedCount = userProjects.stream().filter(p -> p.getStatus() == 2).count();
        long pausedCount = userProjects.stream().filter(p -> p.getStatus() == 3).count();
        long cancelledCount = userProjects.stream().filter(p -> p.getStatus() == 4).count();
        
        // 统计各优先级项目数量
        long lowPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 1).count();
        long mediumPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 2).count();
        long highPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 3).count();
        long urgentPriorityCount = userProjects.stream().filter(p -> p.getPriority() == 4).count();
        
        return R.ok(Dict.create()
            .set("totalCount", userProjects.size())
            .set("statusStats", Dict.create()
                .set("planning", planningCount)
                .set("inProgress", inProgressCount)
                .set("completed", completedCount)
                .set("paused", pausedCount)
                .set("cancelled", cancelledCount)
            )
            .set("priorityStats", Dict.create()
                .set("low", lowPriorityCount)
                .set("medium", mediumPriorityCount)
                .set("high", highPriorityCount)
                .set("urgent", urgentPriorityCount)
            )
        );
    }
}

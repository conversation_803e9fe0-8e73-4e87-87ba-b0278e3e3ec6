package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class SOPTaskScheduleConfigEntityTableDef extends TableDef {

    /**
     * 任务调度配置实体
     */
    public static final SOPTaskScheduleConfigEntityTableDef SOPTASK_SCHEDULE_CONFIG_ENTITY = new SOPTaskScheduleConfigEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn CONFIG_KEY = new QueryColumn(this, "config_key");

    public final QueryColumn IS_ENABLED = new QueryColumn(this, "is_enabled");

    public final QueryColumn CONFIG_NAME = new QueryColumn(this, "config_name");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn CONFIG_VALUE = new QueryColumn(this, "config_value");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, CONFIG_KEY, IS_ENABLED, CONFIG_NAME, CREATE_TIME, UPDATE_TIME, CONFIG_VALUE, DESCRIPTION};

    public SOPTaskScheduleConfigEntityTableDef() {
        super("", "sop_task_schedule_config");
    }

    private SOPTaskScheduleConfigEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SOPTaskScheduleConfigEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SOPTaskScheduleConfigEntityTableDef("", "sop_task_schedule_config", alias));
    }

}

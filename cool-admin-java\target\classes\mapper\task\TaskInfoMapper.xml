<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cool.modules.task.mapper.TaskInfoMapper">

    <!-- 通用的任务信息字段映射 -->
    <sql id="taskInfoColumns">
        ti.id,
        ti.name,
        ti.description,
        ti.task_status,
        ti.task_category,
        ti.completion_time,
        ti.close_reason,
        ti.closed_by,
        ti.close_time,
        ti.scenario_id,
        ti.package_id,
        ti.scenario_code,
        ti.scenario_name,
        ti.step_id,
        ti.step_code,
        ti.step_name,
        ti.entity_touchpoint,
        ti.task_activity,
        ti.employee_behavior,
        ti.work_highlight,
        ti.employee_role,
        ti.photo_required,
        ti.attachment_required,
        ti.remark,
        ti.job_id,
        ti.repeat_count,
        ti.every,
        ti.schedule_status,
        ti.service,
        ti.schedule_type,
        ti.type,
        ti.data,
        ti.cron,
        ti.next_run_time,
        ti.start_time,
        ti.end_time,
        ti.department_id,
        ti.creator_department_id,
        ti.create_time,
        ti.update_time,
        dept1.name AS department_name,
        dept2.name AS creator_department_name,
        tp.package_name,
        tp.scenario_name as package_scenario_name
    </sql>

    <!-- 通用的表关联 -->
    <sql id="taskInfoJoins">
        FROM task_info ti
        LEFT JOIN base_sys_department dept1 ON ti.department_id = dept1.id
        LEFT JOIN base_sys_department dept2 ON ti.creator_department_id = dept2.id
        LEFT JOIN task_package tp ON ti.package_id = tp.id
    </sql>

    <!-- 通用的查询条件 -->
    <sql id="taskInfoConditions">
        <where>
            ti.is_deleted = 0
            <if test="params.packageId != null">
                AND ti.package_id = #{params.packageId}
            </if>
            <if test="params.taskStatus != null">
                AND ti.task_status = #{params.taskStatus}
            </if>
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach collection="params.departmentIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    ti.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.description LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.scenario_name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.step_name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR dept1.name LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
            <if test="params.scenarioId != null">
                AND ti.scenario_id = #{params.scenarioId}
            </if>
            <if test="params.stepId != null">
                AND ti.step_id = #{params.stepId}
            </if>
            <if test="params.taskCategory != null and params.taskCategory != ''">
                AND ti.task_category = #{params.taskCategory}
            </if>
            <if test="params.scheduleStatus != null">
                AND ti.schedule_status = #{params.scheduleStatus}
            </if>
            <if test="params.startTime != null and params.endTime != null">
                AND ti.create_time BETWEEN #{params.startTime} AND #{params.endTime}
            </if>
        </where>
    </sql>

    <!-- 分页查询任务信息列表（关联部门信息） -->
    <select id="selectTaskInfoWithDepartment" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT 
        <include refid="taskInfoColumns"/>
        <include refid="taskInfoJoins"/>
        <include refid="taskInfoConditions"/>
        ORDER BY ti.create_time DESC
        <if test="params.offset != null and params.limit != null">
            LIMIT #{params.offset}, #{params.limit}
        </if>
    </select>

    <!-- 查询任务信息列表总数（关联部门信息） -->
    <select id="countTaskInfoWithDepartment" resultType="int">
        SELECT COUNT(*)
        <include refid="taskInfoJoins"/>
        <include refid="taskInfoConditions"/>
    </select>

    <!-- 根据ID获取任务信息详情（关联部门信息） -->
    <select id="selectTaskInfoWithDepartmentById" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT 
        <include refid="taskInfoColumns"/>
        <include refid="taskInfoJoins"/>
        WHERE ti.id = #{taskId} AND ti.is_deleted = 0
    </select>

    <!-- 根据任务包ID查询任务列表（关联部门信息） -->
    <select id="selectTaskInfoByPackageId" resultType="com.cool.modules.task.entity.TaskInfoEntity">
        SELECT 
        <include refid="taskInfoColumns"/>
        <include refid="taskInfoJoins"/>
        WHERE ti.package_id = #{packageId} AND ti.is_deleted = 0
        ORDER BY ti.create_time DESC
    </select>

    <!-- 查询用户有权限的任务列表（关联部门和执行信息） -->
    <select id="selectUserTasksWithDepartment" resultType="map">
        SELECT 
        <include refid="taskInfoColumns"/>,
        te.assignee_id,
        te.assignee_name,
        te.execution_status,
        te.accept_time,
        te.start_time as execution_start_time,
        te.completion_time as execution_completion_time,
        te.actual_duration,
        te.quality_score,
        te.performance_rating
        FROM task_info ti
        LEFT JOIN base_sys_department dept1 ON ti.department_id = dept1.id
        LEFT JOIN base_sys_department dept2 ON ti.creator_department_id = dept2.id
        LEFT JOIN task_package tp ON ti.package_id = tp.id
        LEFT JOIN task_execution te ON ti.id = te.task_id AND te.is_deleted = 0
        <where>
            ti.is_deleted = 0
            <if test="params.assigneeId != null">
                AND te.assignee_id = #{params.assigneeId}
            </if>
            <if test="params.executionStatus != null and params.executionStatus != ''">
                AND te.execution_status = #{params.executionStatus}
            </if>
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach collection="params.departmentIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="params.keyWord != null and params.keyWord != ''">
                AND (
                    ti.name LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR ti.description LIKE CONCAT('%', #{params.keyWord}, '%')
                    OR dept1.name LIKE CONCAT('%', #{params.keyWord}, '%')
                )
            </if>
        </where>
        ORDER BY ti.create_time DESC
        <if test="params.offset != null and params.limit != null">
            LIMIT #{params.offset}, #{params.limit}
        </if>
    </select>

    <!-- 查询用户任务总数 -->
    <select id="countUserTasksWithDepartment" resultType="int">
        SELECT COUNT(*)
        FROM task_info ti
        LEFT JOIN task_execution te ON ti.id = te.task_id AND te.is_deleted = 0
        <where>
            ti.is_deleted = 0
            <if test="params.assigneeId != null">
                AND te.assignee_id = #{params.assigneeId}
            </if>
            <if test="params.executionStatus != null and params.executionStatus != ''">
                AND te.execution_status = #{params.executionStatus}
            </if>
            <if test="params.departmentIds != null and params.departmentIds.size() > 0">
                AND ti.department_id IN
                <foreach collection="params.departmentIds" item="deptId" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </select>

</mapper> 
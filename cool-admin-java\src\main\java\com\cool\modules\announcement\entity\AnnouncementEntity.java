package com.cool.modules.announcement.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公示实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "announcement", comment = "公示表")
public class AnnouncementEntity extends BaseEntity<AnnouncementEntity> {
    @ColumnDefine(comment = "项目ID")
    private Long projectId;
    @ColumnDefine(comment = "公示名称", length = 100)
    private String title;
    @ColumnDefine(comment = "月份", length = 20)
    private String month;
    @ColumnDefine(comment = "公示内容HTML", type = "text")
    private String html;
    @ColumnDefine(comment = "状态(0草稿/1已发布/2已撤销)", type = "tinyint")
    private Integer status;
} 
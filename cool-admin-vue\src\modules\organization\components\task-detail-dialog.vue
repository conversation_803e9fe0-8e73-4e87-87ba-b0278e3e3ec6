<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    title="任务详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="task" class="task-detail">
      <el-form :model="task" label-width="100px">
        <el-form-item label="任务标题">
          <el-input v-model="task.title" />
        </el-form-item>
        
        <el-form-item label="任务描述">
          <el-input v-model="task.description" type="textarea" :rows="3" />
        </el-form-item>
        
        <el-form-item label="任务状态">
          <el-select v-model="task.status">
            <el-option label="待开始" :value="1" />
            <el-option label="进行中" :value="2" />
            <el-option label="待审核" :value="3" />
            <el-option label="已完成" :value="4" />
            <el-option label="已取消" :value="5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select v-model="task.priority">
            <el-option label="低" :value="1" />
            <el-option label="普通" :value="2" />
            <el-option label="中等" :value="3" />
            <el-option label="高" :value="4" />
            <el-option label="紧急" :value="5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="进度">
          <el-slider v-model="task.progress" :max="100" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';

interface Props {
  modelValue: boolean;
  task: any;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  updated: [];
}>();

const handleSave = () => {
  ElMessage.success('任务更新成功');
  emit('updated');
  emit('update:modelValue', false);
};
</script>

<style lang="scss" scoped>
.task-detail {
  padding: 20px 0;
}
</style>

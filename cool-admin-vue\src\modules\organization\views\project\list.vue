<template>
  <div class="project-list">
    <cl-crud ref="Crud">
      <cl-row>
        <!-- 刷新按钮 -->
        <cl-refresh-btn />
        <!-- 新增按钮 -->
        <cl-add-btn>创建项目</cl-add-btn>
        <!-- 删除按钮 -->
        <cl-multi-delete-btn />
        <cl-flex1 />
        <!-- 条件搜索 -->
        <cl-search ref="Search" />
      </cl-row>

      <cl-row>
        <!-- 数据表格 -->
        <cl-table ref="Table" />
      </cl-row>

      <cl-row>
        <cl-flex1 />
        <!-- 分页控件 -->
        <cl-pagination />
      </cl-row>

      <!-- 新增、编辑 -->
      <cl-upsert ref="Upsert" />
    </cl-crud>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: "project-list",
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { useI18n } from "vue-i18n";

const { service } = useCool();
const { t } = useI18n();

// cl-upsert
const Upsert = useUpsert({
  items: [
    {
      label: t("项目名称"),
      prop: "projectName",
      component: { name: "el-input", props: { clearable: true } },
      span: 12,
      required: true,
    },
    {
      label: t("项目编码"),
      prop: "projectCode",
      component: { name: "el-input", props: { clearable: true } },
      span: 12,
      required: true,
    },
    {
      label: t("项目描述"),
      prop: "description",
      component: { 
        name: "el-input", 
        props: { 
          type: "textarea",
          rows: 3,
          clearable: true 
        } 
      },
      span: 24,
    },
    {
      label: t("项目状态"),
      prop: "status",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: [
            { label: "进行中", value: 1 },
            { label: "已完成", value: 2 },
            { label: "已暂停", value: 3 },
            { label: "已取消", value: 4 }
          ]
        }
      },
      span: 12,
    },
    {
      label: t("项目优先级"),
      prop: "priority",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: [
            { label: "低", value: 1 },
            { label: "普通", value: 2 },
            { label: "中等", value: 3 },
            { label: "高", value: 4 },
            { label: "紧急", value: 5 }
          ]
        }
      },
      span: 12,
    },
    {
      label: t("计划开始时间"),
      prop: "plannedStartTime",
      component: {
        name: "el-date-picker",
        props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      },
      span: 12,
    },
    {
      label: t("计划结束时间"),
      prop: "plannedEndTime",
      component: {
        name: "el-date-picker",
        props: { type: "datetime", valueFormat: "YYYY-MM-DD HH:mm:ss" },
      },
      span: 12,
    },
    {
      label: t("项目预算"),
      prop: "budget",
      component: { 
        name: "el-input-number", 
        props: { 
          min: 0,
          precision: 2,
          style: "width: 100%"
        } 
      },
      span: 12,
    },
    {
      label: t("项目标签"),
      prop: "tags",
      component: { 
        name: "el-input", 
        props: { 
          clearable: true,
          placeholder: "多个标签用逗号分隔"
        } 
      },
      span: 12,
    },
  ],
});

// cl-table
const Table = useTable({
  columns: [
    { type: "selection" },
    { 
      label: t("项目名称"), 
      prop: "projectName", 
      minWidth: 150,
      showOverflowTooltip: true
    },
    { 
      label: t("项目编码"), 
      prop: "projectCode", 
      minWidth: 120,
      showOverflowTooltip: true
    },
    { 
      label: t("项目描述"), 
      prop: "description", 
      minWidth: 200,
      showOverflowTooltip: true
    },
    { 
      label: t("项目状态"), 
      prop: "status", 
      minWidth: 100,
      dict: [
        { label: "进行中", value: 1, type: "success" },
        { label: "已完成", value: 2, type: "info" },
        { label: "已暂停", value: 3, type: "warning" },
        { label: "已取消", value: 4, type: "danger" }
      ]
    },
    { 
      label: t("项目优先级"), 
      prop: "priority", 
      minWidth: 100,
      dict: [
        { label: "低", value: 1, type: "info" },
        { label: "普通", value: 2, type: "" },
        { label: "中等", value: 3, type: "warning" },
        { label: "高", value: 4, type: "danger" },
        { label: "紧急", value: 5, type: "danger" }
      ]
    },
    {
      label: t("计划开始时间"),
      prop: "plannedStartTime",
      minWidth: 170,
      sortable: "custom",
      component: { name: "cl-date-text" },
    },
    {
      label: t("计划结束时间"),
      prop: "plannedEndTime",
      minWidth: 170,
      sortable: "custom",
      component: { name: "cl-date-text" },
    },
    { 
      label: t("项目预算"), 
      prop: "budget", 
      minWidth: 120,
      align: "right"
    },
    {
      label: t("创建时间"),
      prop: "createTime",
      minWidth: 170,
      sortable: "desc",
      component: { name: "cl-date-text" },
    },
    { 
      type: "op", 
      buttons: [
        "edit", 
        "delete",
        {
          label: "成员管理",
          type: "primary",
          onClick: ({ scope }) => {
            // 跳转到成员管理页面
            window.open(`/project/member/${scope.row.id}`, '_blank');
          }
        },
        {
          label: "查看详情",
          type: "success",
          onClick: ({ scope }) => {
            // 跳转到项目详情页面
            window.open(`/project/detail/${scope.row.id}`, '_blank');
          }
        }
      ]
    },
  ],
});

// cl-search
const Search = useSearch({
  items: [
    {
      label: t("项目名称"),
      prop: "projectName",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: t("项目编码"),
      prop: "projectCode",
      component: { name: "el-input", props: { clearable: true } }
    },
    {
      label: t("项目状态"),
      prop: "status",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: [
            { label: "进行中", value: 1 },
            { label: "已完成", value: 2 },
            { label: "已暂停", value: 3 },
            { label: "已取消", value: 4 }
          ]
        }
      }
    },
    {
      label: t("项目优先级"),
      prop: "priority",
      component: { 
        name: "el-select",
        props: {
          clearable: true,
          options: [
            { label: "低", value: 1 },
            { label: "普通", value: 2 },
            { label: "中等", value: 3 },
            { label: "高", value: 4 },
            { label: "紧急", value: 5 }
          ]
        }
      }
    }
  ]
});

// cl-crud
const Crud = useCrud(
  {
    service: service.organization?.project?.info || {
      // 模拟服务，避免报错
      page: () => Promise.resolve({ list: [], pagination: { total: 0 } }),
      add: () => Promise.resolve({}),
      update: () => Promise.resolve({}),
      delete: () => Promise.resolve({})
    },
  },
  (app) => {
    app.refresh();
  },
);

// 刷新
function refresh(params?: any) {
  Crud.value?.refresh(params);
}
</script>

<style lang="scss" scoped>
.project-list {
  height: 100%;
}
</style>

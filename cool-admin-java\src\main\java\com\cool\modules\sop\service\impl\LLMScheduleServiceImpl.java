package com.cool.modules.sop.service.impl;

import com.cool.core.ai.OpenAIService;
import com.cool.modules.sop.dto.TaskAssignmentDTO;
import com.cool.modules.sop.service.LLMScheduleService;
import com.cool.modules.task.entity.TaskInfoEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * LLM任务调度服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LLMScheduleServiceImpl implements LLMScheduleService {

    private final OpenAIService openAIService;

    @Override
    public TaskAssignmentDTO.AssignmentResult llmAssignTask(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config) {
        
        try {
            // 1. 预筛选候选人
            List<TaskAssignmentDTO.UserProfile> filteredCandidates = preFilterCandidates(taskInfo, candidates, config);
            
            if (filteredCandidates.isEmpty()) {
                return createFailureResult(taskInfo.getId(), "没有符合条件的候选人");
            }

            // 2. 基于规则评分
            List<TaskAssignmentDTO.UserProfile> scoredCandidates = scoreByRules(taskInfo, filteredCandidates, config);

            // 3. 构建LLM提示词
            String prompt = buildPrompt(taskInfo, scoredCandidates, config);

            // 4. 调用LLM
            String systemPrompt = "你是一个专业的任务调度专家，需要根据任务要求和人员情况，智能分配最合适的执行人员。";
            String response = openAIService.chat(systemPrompt, prompt);

            // 5. 解析响应
            return parseResponse(response, scoredCandidates);

        } catch (Exception e) {
            log.error("LLM任务分配失败，降级到规则引擎: {}", e.getMessage(), e);
            return fallbackToRules(taskInfo, candidates, config);
        }
    }

    @Override
    public String buildPrompt(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config) {
        
        StringBuilder prompt = new StringBuilder();
        
        // 任务信息
        prompt.append("请为以下任务分配最合适的执行人员：\n\n");
        prompt.append("任务信息：\n");
        prompt.append("- 名称：").append(taskInfo.getName()).append("\n");
        prompt.append("- 描述：").append(taskInfo.getDescription()).append("\n");
        prompt.append("- 步骤：").append(taskInfo.getStepName()).append(" (").append(taskInfo.getStepCode()).append(")\n");
        prompt.append("- 执行地点：").append("不限").append("\n");
        prompt.append("- 特殊要求：").append("无").append("\n\n");

        // 候选人信息
        prompt.append("候选人员（已按规则评分排序）：\n");
        for (int i = 0; i < candidates.size(); i++) {
            TaskAssignmentDTO.UserProfile user = candidates.get(i);
            prompt.append(String.format("%d. %s (ID: %d)\n", i + 1, user.getName(), user.getId()));
            prompt.append("   - 角色：").append(String.join(", ", user.getRoles())).append("\n");
            
            if (user.getSkills() != null && !user.getSkills().isEmpty()) {
                String skills = user.getSkills().stream()
                    .map(skill -> skill.getSkillName() + "(L" + skill.getSkillLevel() + ")")
                    .collect(Collectors.joining(", "));
                prompt.append("   - 技能：").append(skills).append("\n");
            }
            
            prompt.append("   - 部门：").append(user.getDepartment() != null ? user.getDepartment() : "未知").append("\n");
            prompt.append("   - 地点：").append(user.getLocation() != null ? user.getLocation() : "未知").append("\n");
            prompt.append("   - 当前工作量：").append(user.getCurrentWorkload() != null ? user.getCurrentWorkload() : 0).append("%\n");
            prompt.append("   - 历史绩效：").append(user.getPerformance() != null ? user.getPerformance() : 0).append("/100\n");
            prompt.append("   - 可用性：").append(user.getAvailable() != null && user.getAvailable() ? "可用" : "不可用").append("\n\n");
        }

        // 考虑因素
        prompt.append("请综合考虑以下因素：\n");
        if (config.getRoleMatch()) {
            prompt.append("1. 角色匹配度和专业能力\n");
        }
        if (config.getSkillMatch()) {
            prompt.append("2. 技能匹配度和熟练程度\n");
        }
        if (config.getLocationMatch()) {
            prompt.append("3. 地理位置和执行便利性\n");
        }
        if (config.getPerformanceMatch()) {
            prompt.append("4. 历史绩效和工作质量\n");
        }
        if (config.getAvailabilityCheck()) {
            prompt.append("5. 当前工作量和时间安排\n");
        }

        // 返回格式
        prompt.append("\n请返回JSON格式的分配结果：\n");
        prompt.append("{\n");
        prompt.append("  \"recommendedUserId\": 推荐人员ID,\n");
        prompt.append("  \"confidence\": 置信度(0-100),\n");
        prompt.append("  \"reasons\": [\"推荐理由1\", \"推荐理由2\", ...],\n");
        prompt.append("  \"alternatives\": [\n");
        prompt.append("    {\"userId\": 备选人员ID, \"confidence\": 置信度},\n");
        prompt.append("    ...\n");
        prompt.append("  ]\n");
        prompt.append("}");

        return prompt.toString();
    }

    @Override
    public TaskAssignmentDTO.AssignmentResult parseResponse(
            String response,
            List<TaskAssignmentDTO.UserProfile> candidates) {
        
        try {
            // 简单的JSON解析（实际项目中应该使用JSON库）
            String jsonContent = extractJsonFromResponse(response);
            
            // 这里应该使用JSON库解析，暂时用简单的字符串匹配
            Long recommendedUserId = extractLongValue(jsonContent, "recommendedUserId");
            Integer confidence = extractIntValue(jsonContent, "confidence");
            List<String> reasons = extractStringArray(jsonContent, "reasons");
            
            TaskAssignmentDTO.UserProfile recommendedUser = candidates.stream()
                .filter(u -> u.getId().equals(recommendedUserId))
                .findFirst()
                .orElse(candidates.get(0)); // 降级到第一个候选人

            TaskAssignmentDTO.AssignmentResult result = new TaskAssignmentDTO.AssignmentResult();
            result.setUserId(recommendedUser.getId());
            result.setUserName(recommendedUser.getName());
            result.setConfidence(confidence != null ? confidence : 80);
            result.setReasons(reasons != null ? reasons : List.of("LLM智能推荐"));
            result.setSuccess(true);

            // 设置备选人员
            List<TaskAssignmentDTO.AlternativeUser> alternatives = candidates.stream()
                .filter(u -> !u.getId().equals(recommendedUser.getId()))
                .limit(2)
                .map(u -> {
                    TaskAssignmentDTO.AlternativeUser alt = new TaskAssignmentDTO.AlternativeUser();
                    alt.setUserId(u.getId());
                    alt.setUserName(u.getName());
                    alt.setConfidence(75); // 默认置信度
                    return alt;
                })
                .collect(Collectors.toList());
            result.setAlternatives(alternatives);

            return result;

        } catch (Exception e) {
            log.error("解析LLM响应失败: {}", e.getMessage(), e);
            // 降级到第一个候选人
            TaskAssignmentDTO.UserProfile topCandidate = candidates.get(0);
            return createSuccessResult(topCandidate, 70, List.of("LLM解析失败，使用规则引擎推荐"));
        }
    }

    @Override
    public List<TaskAssignmentDTO.UserProfile> preFilterCandidates(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> allUsers,
            TaskAssignmentDTO.AssignmentConfig config) {
        
        return allUsers.stream()
            .filter(user -> {
                // 基础可用性检查
                if (config.getAvailabilityCheck() && (user.getAvailable() == null || !user.getAvailable())) {
                    return false;
                }
                
                // 工作量检查
                if (config.getAvailabilityCheck() && user.getCurrentWorkload() != null && user.getCurrentWorkload() > 90) {
                    return false;
                }
                
                // 角色匹配检查（默认启用）
                if (config.getRoleMatch() && (user.getRoles() == null || user.getRoles().isEmpty())) {
                    return false;
                }
                
                return true;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<TaskAssignmentDTO.UserProfile> scoreByRules(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config) {
        
        // 简单的评分逻辑，实际项目中应该更复杂
        return candidates.stream()
            .sorted(Comparator.comparingInt(user -> {
                int score = 0;
                
                // 角色匹配评分
                if (config.getRoleMatch() && user.getRoles() != null && !user.getRoles().isEmpty()) {
                    score += 40;
                }
                
                // 工作量评分
                if (user.getCurrentWorkload() != null) {
                    score += (100 - user.getCurrentWorkload()) / 4; // 最多25分
                }
                
                // 绩效评分
                if (user.getPerformance() != null) {
                    score += user.getPerformance() / 5; // 最多20分
                }
                
                // 可用性评分
                if (user.getAvailable() != null && user.getAvailable()) {
                    score += 15;
                }
                
                return -score; // 降序排列
            }))
            .collect(Collectors.toList());
    }

    @Override
    public TaskAssignmentDTO.AssignmentResult fallbackToRules(
            TaskInfoEntity taskInfo,
            List<TaskAssignmentDTO.UserProfile> candidates,
            TaskAssignmentDTO.AssignmentConfig config) {
        
        List<TaskAssignmentDTO.UserProfile> filtered = preFilterCandidates(taskInfo, candidates, config);
        
        if (filtered.isEmpty()) {
            return createFailureResult(taskInfo.getId(), "没有可用的执行人员");
        }
        
        List<TaskAssignmentDTO.UserProfile> scored = scoreByRules(taskInfo, filtered, config);
        TaskAssignmentDTO.UserProfile best = scored.get(0);
        
        return createSuccessResult(best, 75, List.of("基于规则引擎的智能分配"));
    }

    // 辅助方法
    private TaskAssignmentDTO.AssignmentResult createFailureResult(Long taskId, String errorMessage) {
        TaskAssignmentDTO.AssignmentResult result = new TaskAssignmentDTO.AssignmentResult();
        result.setTaskId(taskId);
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

    private TaskAssignmentDTO.AssignmentResult createSuccessResult(
            TaskAssignmentDTO.UserProfile user, 
            Integer confidence, 
            List<String> reasons) {
        
        TaskAssignmentDTO.AssignmentResult result = new TaskAssignmentDTO.AssignmentResult();
        result.setUserId(user.getId());
        result.setUserName(user.getName());
        result.setConfidence(confidence);
        result.setReasons(reasons);
        result.setSuccess(true);
        result.setAlternatives(new ArrayList<>());
        return result;
    }

    // 简单的JSON解析方法（实际项目中应该使用JSON库）
    private String extractJsonFromResponse(String response) {
        int start = response.indexOf("{");
        int end = response.lastIndexOf("}");
        if (start >= 0 && end > start) {
            return response.substring(start, end + 1);
        }
        return "{}";
    }

    private Long extractLongValue(String json, String key) {
        try {
            String pattern = "\"" + key + "\"\\s*:\\s*(\\d+)";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return Long.parseLong(m.group(1));
            }
        } catch (Exception e) {
            log.warn("提取Long值失败: {}", e.getMessage());
        }
        return null;
    }

    private Integer extractIntValue(String json, String key) {
        try {
            String pattern = "\"" + key + "\"\\s*:\\s*(\\d+)";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return Integer.parseInt(m.group(1));
            }
        } catch (Exception e) {
            log.warn("提取Integer值失败: {}", e.getMessage());
        }
        return null;
    }

    private List<String> extractStringArray(String json, String key) {
        try {
            String pattern = "\"" + key + "\"\\s*:\\s*\\[(.*?)\\]";
            Pattern p = Pattern.compile(pattern, Pattern.DOTALL);
            Matcher m = p.matcher(json);
            if (m.find()) {
                String arrayContent = m.group(1);
                return java.util.Arrays.stream(arrayContent.split(","))
                    .map(s -> s.trim().replaceAll("^\"|\"$", ""))
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("提取字符串数组失败: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public double calculateSkillMatch(List<String> requiredSkills, List<TaskAssignmentDTO.UserSkill> userSkills) {
        if (requiredSkills == null || requiredSkills.isEmpty()) return 1.0;
        if (userSkills == null || userSkills.isEmpty()) return 0.0;
        
        List<String> userSkillNames = userSkills.stream()
            .map(TaskAssignmentDTO.UserSkill::getSkillName)
            .collect(Collectors.toList());
        
        long matches = requiredSkills.stream()
            .mapToLong(skill -> userSkillNames.contains(skill) ? 1 : 0)
            .sum();
        
        return (double) matches / requiredSkills.size();
    }

    @Override
    public double calculateLocationScore(String taskLocation, String userLocation) {
        if (taskLocation == null || userLocation == null) return 0.5;
        return taskLocation.equals(userLocation) ? 1.0 : 0.3;
    }

    @Override
    public double calculateWorkloadScore(Integer currentWorkload) {
        if (currentWorkload == null) return 0.5;
        return (100.0 - currentWorkload) / 100.0;
    }

    @Override
    public double calculatePerformanceScore(Integer performance) {
        if (performance == null) return 0.5;
        return performance / 100.0;
    }
}

<template>
  <div class="template-role-config">
    <div class="role-list">
      <div v-for="(role, index) in modelValue" :key="index" class="role-item">
        <el-input v-model="role.name" placeholder="角色名称" />
        <el-button @click="removeRole(index)" type="danger" size="small">删除</el-button>
      </div>
    </div>
    
    <el-button @click="addRole" type="primary">添加角色</el-button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: any[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:modelValue': [value: any[]];
}>();

const addRole = () => {
  const newRoles = [...props.modelValue, { name: '', permissions: [] }];
  emit('update:modelValue', newRoles);
};

const removeRole = (index: number) => {
  const newRoles = props.modelValue.filter((_, i) => i !== index);
  emit('update:modelValue', newRoles);
};
</script>

<style lang="scss" scoped>
.template-role-config {
  padding: 20px;
  
  .role-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
  }
}
</style>

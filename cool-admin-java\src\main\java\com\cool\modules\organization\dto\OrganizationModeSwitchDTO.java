package com.cool.modules.organization.dto;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 组织形态切换DTO
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Data
@Accessors(chain = true)
public class OrganizationModeSwitchDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 目标组织模式
     */
    @NotBlank(message = "目标组织模式不能为空")
    private String targetMode;
    
    /**
     * 切换原因
     */
    private String reason;
    
    /**
     * 是否强制切换（忽略权限检查）
     */
    private Boolean forceSwitch = false;
    
    /**
     * 客户端信息
     */
    private String clientInfo;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
}

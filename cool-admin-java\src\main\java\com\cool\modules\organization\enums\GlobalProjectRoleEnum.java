package com.cool.modules.organization.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 全局项目角色枚举
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Getter
@AllArgsConstructor
public enum GlobalProjectRoleEnum {
    
    /**
     * 项目负责人
     */
    PROJECT_OWNER("PROJECT_OWNER", "项目负责人", "项目完全控制权限", 1),
    
    /**
     * 项目管理员
     */
    PROJECT_ADMIN("PROJECT_ADMIN", "项目管理员", "项目管理权限", 2),
    
    /**
     * 项目成员
     */
    PROJECT_MEMBER("PROJECT_MEMBER", "项目成员", "项目参与权限", 3),
    
    /**
     * 项目观察者
     */
    PROJECT_VIEWER("PROJECT_VIEWER", "项目观察者", "项目只读权限", 4);
    
    /**
     * 角色代码
     */
    private final String code;
    
    /**
     * 角色名称
     */
    private final String name;
    
    /**
     * 角色描述
     */
    private final String description;
    
    /**
     * 角色级别（数字越小权限越高）
     */
    private final Integer level;
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 角色代码
     * @return 项目角色枚举
     */
    public static GlobalProjectRoleEnum getByCode(String code) {
        for (GlobalProjectRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为有效的项目角色代码
     * 
     * @param code 角色代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        for (GlobalProjectRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查角色是否有管理权限
     * 
     * @param code 角色代码
     * @return 是否有管理权限
     */
    public static boolean hasManagePermission(String code) {
        GlobalProjectRoleEnum role = getByCode(code);
        return role != null && (role == PROJECT_OWNER || role == PROJECT_ADMIN);
    }
    
    /**
     * 检查角色是否有编辑权限
     * 
     * @param code 角色代码
     * @return 是否有编辑权限
     */
    public static boolean hasEditPermission(String code) {
        GlobalProjectRoleEnum role = getByCode(code);
        return role != null && role != PROJECT_VIEWER;
    }
    
    /**
     * 获取角色级别
     * 
     * @param code 角色代码
     * @return 角色级别
     */
    public static Integer getRoleLevel(String code) {
        GlobalProjectRoleEnum role = getByCode(code);
        return role != null ? role.getLevel() : Integer.MAX_VALUE;
    }
}

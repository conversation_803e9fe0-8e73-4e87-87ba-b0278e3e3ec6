package com.cool.modules.task.entity;

import com.cool.core.base.BaseEntity;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.tangzc.mybatisflex.autotable.annotation.ColumnDefine;
import lombok.Getter;
import lombok.Setter;
import org.dromara.autotable.annotation.Index;

import java.time.LocalDateTime;

/**
 * 任务权限操作日志实体
 */
@Getter
@Setter
@Table(value = "task_permission_log", comment = "任务权限操作日志表")
public class TaskPermissionLogEntity extends BaseEntity<TaskPermissionLogEntity> {

    @Id(keyType = KeyType.Auto)
    private Long id;

    @Index
    @ColumnDefine(comment = "操作用户ID", notNull = true, type = "bigint")
    private Long userId;

    @ColumnDefine(comment = "操作用户名", notNull = true, length = 255)
    private String username;

    @Index
    @ColumnDefine(comment = "操作类型", notNull = true, length = 50)
    private String operationType; // VIEW, EDIT, DELETE, ASSIGN等

    @Index
    @ColumnDefine(comment = "任务类型", notNull = true, length = 50)
    private String taskType; // package, info, execution

    @Index
    @ColumnDefine(comment = "任务ID", notNull = true, type = "bigint")
    private Long taskId;

    @Index
    @ColumnDefine(comment = "涉及部门ID", type = "bigint")
    private Long departmentId;

    @Index
    @ColumnDefine(comment = "权限验证结果", notNull = true, type = "tinyint")
    private Integer permissionResult; // 0:失败, 1:成功

    @Index
    @ColumnDefine(comment = "操作时间", notNull = true)
    private LocalDateTime operationTime;

    @ColumnDefine(comment = "客户端IP", length = 50)
    private String clientIp;

    @ColumnDefine(comment = "用户代理", type = "text")
    private String userAgent;

    @ColumnDefine(comment = "操作备注", type = "text")
    private String remark;
} 
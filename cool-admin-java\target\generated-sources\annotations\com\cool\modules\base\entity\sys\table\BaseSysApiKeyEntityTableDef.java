package com.cool.modules.base.entity.sys.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class BaseSysApiKeyEntityTableDef extends TableDef {

    /**
     * APIKEY授权表实体
     */
    public static final BaseSysApiKeyEntityTableDef BASE_SYS_API_KEY_ENTITY = new BaseSysApiKeyEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn APIKEY = new QueryColumn(this, "apikey");

    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn EXPIRE_TIME = new QueryColumn(this, "expire_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, APIKEY, REMARK, STATUS, USER_ID, CREATE_TIME, EXPIRE_TIME, UPDATE_TIME};

    public BaseSysApiKeyEntityTableDef() {
        super("", "base_sys_apikey");
    }

    private BaseSysApiKeyEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public BaseSysApiKeyEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new BaseSysApiKeyEntityTableDef("", "base_sys_apikey", alias));
    }

}

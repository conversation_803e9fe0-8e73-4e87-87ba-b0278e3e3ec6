import { useOrganizationStore } from '../store/organization';

/**
 * 初始化组织模块
 * 确保在Pinia可用后再初始化store
 */
export function initOrganizationModule() {
  try {
    // 检查Pinia是否可用
    const store = useOrganizationStore();
    
    // 初始化组织信息
    if (store.init && typeof store.init === 'function') {
      store.init();
    }
    
    console.log('组织模块初始化成功');
    return true;
  } catch (error) {
    console.warn('组织模块初始化失败:', error);
    return false;
  }
}

/**
 * 安全地获取组织store
 */
export function getOrganizationStore() {
  try {
    return useOrganizationStore();
  } catch (error) {
    console.warn('无法获取组织store:', error);
    return null;
  }
}

/**
 * 检查组织模块是否可用
 */
export function isOrganizationModuleAvailable() {
  try {
    const store = useOrganizationStore();
    return !!store;
  } catch (error) {
    return false;
  }
}

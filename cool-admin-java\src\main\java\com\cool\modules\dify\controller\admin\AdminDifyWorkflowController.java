package com.cool.modules.dify.controller.admin;

import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.modules.dify.entity.DifyWorkflowEntity;
import com.cool.modules.dify.service.DifyWorkflowService;
import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import com.cool.modules.dify.entity.table.DifyWorkflowEntityTableDef;
import jakarta.servlet.http.HttpServletRequest;
import com.cool.modules.dify.dto.DifyWorkflowExecuteRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import jakarta.servlet.http.HttpServletResponse;
import io.swagger.v3.oas.annotations.Operation;

/**
 * Dify工作流管理
 */
@Tag(name = "Dify工作流管理", description = "Dify工作流统一管理")
@CoolRestController(api = { "add", "delete", "update", "info", "page" })
public class AdminDifyWorkflowController extends BaseController<DifyWorkflowService, DifyWorkflowEntity> {

    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        setListOption(
            createOp()
                .fieldEq(DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.STATUS, DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.NAME)
                .keyWordLikeFields(DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.NAME, DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.DESCRIPTION, DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.URL, DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.API_KEY, DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.INPUT_PARAMS, DifyWorkflowEntityTableDef.DIFY_WORKFLOW_ENTITY.OUTPUT_DESC)
        );
    }

    /**
     * 按英文名执行Dify对话型工作流
     */
    @PostMapping("/executeByName")
    @Operation(summary = "按英文名执行Dify对话型工作流")
    public void executeByName(@RequestBody DifyWorkflowExecuteRequest request, HttpServletResponse response) {
        service.executeWorkflowByName(request, response);
    }
} 
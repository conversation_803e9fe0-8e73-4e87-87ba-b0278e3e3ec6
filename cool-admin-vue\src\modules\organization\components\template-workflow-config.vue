<template>
  <div class="template-workflow-config">
    <el-form :model="modelValue" label-width="120px">
      <el-form-item label="工作流类型">
        <el-select v-model="modelValue.type">
          <el-option label="敏捷开发" value="agile" />
          <el-option label="瀑布模型" value="waterfall" />
          <el-option label="自定义" value="custom" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="审批流程">
        <el-checkbox v-model="modelValue.requireApproval">需要审批</el-checkbox>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: any;
}

defineProps<Props>();
defineEmits<{
  'update:modelValue': [value: any];
}>();
</script>

<style lang="scss" scoped>
.template-workflow-config {
  padding: 20px;
}
</style>

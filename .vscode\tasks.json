{"version": "2.0.0", "tasks": [{"label": "compile-java-utf8", "type": "shell", "command": "mvn", "args": ["clean", "compile", "-DskipTests", "-Dfile.encoding=UTF-8", "-Dmaven.compiler.encoding=UTF-8"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}/cool-admin-java", "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"}}, "problemMatcher": "$javac"}, {"label": "run-java-utf8", "type": "shell", "command": "java", "args": ["-Dfile.encoding=UTF-8", "-Dsun.jnu.encoding=UTF-8", "-Dspring.output.ansi.enabled=ALWAYS", "-Dspring.profiles.active=local", "-Dserver.port=8001", "-cp", "target/classes:target/dependency/*", "com.cool.CoolApplication"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"cwd": "${workspaceFolder}/cool-admin-java", "env": {"JAVA_TOOL_OPTIONS": "-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8", "LANG": "zh_CN.UTF-8", "LC_ALL": "zh_CN.UTF-8"}}, "dependsOn": "compile-java-utf8"}]}
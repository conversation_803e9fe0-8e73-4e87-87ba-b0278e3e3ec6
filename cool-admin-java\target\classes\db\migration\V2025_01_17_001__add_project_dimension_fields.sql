-- 双维度权限扩展：为业务实体添加项目维度字段
-- 执行时间：2025-01-17
-- 作者：Cool Admin

-- 1. 为任务包表添加项目维度字段
ALTER TABLE task_package 
ADD COLUMN project_id BIGINT COMMENT '关联项目ID';

-- 为项目ID字段创建索引
CREATE INDEX idx_task_package_project_id ON task_package(project_id);

-- 2. 为任务信息表添加项目维度字段
ALTER TABLE task_info 
ADD COLUMN project_id BIGINT COMMENT '关联项目ID';

-- 为项目ID字段创建索引
CREATE INDEX idx_task_info_project_id ON task_info(project_id);

-- 3. 为任务执行表添加项目维度字段
ALTER TABLE task_execution 
ADD COLUMN project_id BIGINT COMMENT '关联项目ID';

-- 为项目ID字段创建索引
CREATE INDEX idx_task_execution_project_id ON task_execution(project_id);

-- 4. 为工单表添加项目维度字段和规范化部门ID字段
ALTER TABLE sop_work_order 
ADD COLUMN project_id BIGINT COMMENT '关联项目ID',
ADD COLUMN applicant_dept_id BIGINT COMMENT '申请部门ID';

-- 为项目ID和部门ID字段创建索引
CREATE INDEX idx_work_order_project_id ON sop_work_order(project_id);
CREATE INDEX idx_work_order_applicant_dept_id ON sop_work_order(applicant_dept_id);

-- 5. 创建复合索引以优化双维度查询性能
-- 任务包复合索引
CREATE INDEX idx_task_package_dept_project ON task_package(department_id, project_id);

-- 任务信息复合索引
CREATE INDEX idx_task_info_dept_project ON task_info(department_id, project_id);

-- 任务执行复合索引
CREATE INDEX idx_task_execution_dept_project ON task_execution(department_id, project_id);

-- 工单复合索引
CREATE INDEX idx_work_order_dept_project ON sop_work_order(applicant_dept_id, project_id);

-- 6. 数据迁移：将现有工单的申请部门字符串转换为部门ID
-- 注意：这个脚本假设部门名称在base_sys_department表中是唯一的
UPDATE sop_work_order wo
SET applicant_dept_id = (
    SELECT d.id 
    FROM base_sys_department d 
    WHERE d.name = wo.applicant_dept 
    LIMIT 1
)
WHERE wo.applicant_dept IS NOT NULL 
AND wo.applicant_dept != ''
AND wo.applicant_dept_id IS NULL;

-- 7. 为权限过滤创建视图（可选）
-- 创建任务包权限视图
CREATE OR REPLACE VIEW v_task_package_permission AS
SELECT 
    tp.*,
    CASE 
        WHEN tp.department_id IS NOT NULL THEN 'DEPARTMENT'
        WHEN tp.project_id IS NOT NULL THEN 'PROJECT'
        ELSE 'UNKNOWN'
    END AS organization_mode,
    COALESCE(tp.department_id, tp.project_id) AS organization_id
FROM task_package tp;

-- 创建任务信息权限视图
CREATE OR REPLACE VIEW v_task_info_permission AS
SELECT 
    ti.*,
    CASE 
        WHEN ti.department_id IS NOT NULL THEN 'DEPARTMENT'
        WHEN ti.project_id IS NOT NULL THEN 'PROJECT'
        ELSE 'UNKNOWN'
    END AS organization_mode,
    COALESCE(ti.department_id, ti.project_id) AS organization_id
FROM task_info ti;

-- 创建任务执行权限视图
CREATE OR REPLACE VIEW v_task_execution_permission AS
SELECT 
    te.*,
    CASE 
        WHEN te.department_id IS NOT NULL THEN 'DEPARTMENT'
        WHEN te.project_id IS NOT NULL THEN 'PROJECT'
        ELSE 'UNKNOWN'
    END AS organization_mode,
    COALESCE(te.department_id, te.project_id) AS organization_id
FROM task_execution te;

-- 创建工单权限视图
CREATE OR REPLACE VIEW v_work_order_permission AS
SELECT 
    wo.*,
    CASE 
        WHEN wo.applicant_dept_id IS NOT NULL THEN 'DEPARTMENT'
        WHEN wo.project_id IS NOT NULL THEN 'PROJECT'
        ELSE 'UNKNOWN'
    END AS organization_mode,
    COALESCE(wo.applicant_dept_id, wo.project_id) AS organization_id
FROM sop_work_order wo;

-- 8. 添加约束确保数据一致性
-- 任务包：部门ID和项目ID不能同时为空（在应用层控制）
-- 任务信息：部门ID和项目ID不能同时为空（在应用层控制）
-- 任务执行：部门ID和项目ID不能同时为空（在应用层控制）
-- 工单：申请部门ID和项目ID不能同时为空（在应用层控制）

-- 9. 创建数据一致性检查函数（可选）
DELIMITER //
CREATE FUNCTION check_dual_dimension_consistency()
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE result TEXT DEFAULT '';
    DECLARE task_package_issues INT DEFAULT 0;
    DECLARE task_info_issues INT DEFAULT 0;
    DECLARE task_execution_issues INT DEFAULT 0;
    DECLARE work_order_issues INT DEFAULT 0;
    
    -- 检查任务包数据一致性
    SELECT COUNT(*) INTO task_package_issues
    FROM task_package 
    WHERE (department_id IS NULL AND project_id IS NULL)
       OR (department_id IS NOT NULL AND project_id IS NOT NULL);
    
    -- 检查任务信息数据一致性
    SELECT COUNT(*) INTO task_info_issues
    FROM task_info 
    WHERE (department_id IS NULL AND project_id IS NULL)
       OR (department_id IS NOT NULL AND project_id IS NOT NULL);
    
    -- 检查任务执行数据一致性
    SELECT COUNT(*) INTO task_execution_issues
    FROM task_execution 
    WHERE (department_id IS NULL AND project_id IS NULL)
       OR (department_id IS NOT NULL AND project_id IS NOT NULL);
    
    -- 检查工单数据一致性
    SELECT COUNT(*) INTO work_order_issues
    FROM sop_work_order 
    WHERE (applicant_dept_id IS NULL AND project_id IS NULL)
       OR (applicant_dept_id IS NOT NULL AND project_id IS NOT NULL);
    
    SET result = CONCAT(
        'TaskPackage issues: ', task_package_issues, '; ',
        'TaskInfo issues: ', task_info_issues, '; ',
        'TaskExecution issues: ', task_execution_issues, '; ',
        'WorkOrder issues: ', work_order_issues
    );
    
    RETURN result;
END //
DELIMITER ;

-- 10. 插入迁移记录
INSERT INTO migration_log (version, description, executed_at, status) 
VALUES ('V2025_01_17_001', '添加双维度权限项目字段', NOW(), 'SUCCESS')
ON DUPLICATE KEY UPDATE 
    executed_at = NOW(), 
    status = 'SUCCESS';

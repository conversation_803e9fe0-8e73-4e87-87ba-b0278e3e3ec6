<template>
  <cl-crud ref="Crud">
    <cl-row>
      <cl-add-btn @click="upsert().open()" />
      <cl-refresh-btn />
      <cl-multi-delete-btn />
      <cl-flex1 />
      
      <!-- 部门权限状态显示 -->
      <el-tag type="info" style="margin-right: 10px;">
        权限部门: {{ departmentNames }}
      </el-tag>
      
      <!-- 部门筛选器 -->
      <el-select 
        v-model="selectedDepartmentId"
        placeholder="选择部门"
        clearable
        style="width: 200px; margin-right: 10px;"
        @change="refresh"
      >
        <el-option
          v-for="dept in userDepartments"
          :key="dept.id"
          :label="dept.name"
          :value="dept.id"
        />
      </el-select>
      
      <cl-search-key />
    </cl-row>
    
    <cl-row>
      <cl-table ref="Table" />
    </cl-row>
    
    <cl-row>
      <cl-flex1 />
      <cl-pagination />
    </cl-row>
    
    <cl-upsert ref="Upsert" />
  </cl-crud>
</template>

<script setup lang="ts">
defineOptions({
  name: "task-permission-demo"
});

import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

const { service } = useCool();

// 权限相关状态
const userDepartments = ref([]);
const departmentNames = ref('');
const selectedDepartmentId = ref(null);

const Crud = useCrud({
  service: service.task.taskPackage
}, (app) => {
  app.refresh();
  loadUserDepartments();
});

const Table = useTable({
  columns: [
    { type: 'selection' },
    { 
      label: '任务包名称', 
      prop: 'packageName', 
      minWidth: 180,
      showOverflowTooltip: true
    },
    { 
      label: '所属部门', 
      prop: 'departmentName', 
      width: 120,
      dict: {
        url: '/admin/base/sys/department/list'
      }
    },
    {
      label: '创建者部门',
      prop: 'creatorDepartmentName',
      width: 120
    },
    {
      label: '跨部门',
      prop: 'isCrossDepartment',
      width: 80,
      dict: [
        { label: '是', value: true, type: 'warning' },
        { label: '否', value: false, type: 'success' }
      ]
    },
    { 
      label: '状态', 
      prop: 'packageStatus', 
      width: 100,
      dict: [
        { label: '待分配', value: 0, type: 'info' },
        { label: '执行中', value: 1, type: 'primary' },
        { label: '已完成', value: 2, type: 'success' },
        { label: '已关闭', value: 3, type: 'danger' }
      ]
    },
    { 
      label: '优先级', 
      prop: 'priority',
      width: 100,
      dict: [
        { label: '低', value: 1, type: 'info' },
        { label: '中', value: 2, type: 'primary' },
        { label: '高', value: 3, type: 'warning' },
        { label: '紧急', value: 4, type: 'danger' }
      ]
    },
    { 
      label: '总任务数', 
      prop: 'totalTasks', 
      width: 100
    },
    { 
      label: '完成率', 
      prop: 'completionRate', 
      width: 100,
      render: (scope) => `${scope.row.completionRate || 0}%`
    },
    { 
      label: '创建时间', 
      prop: 'createTime', 
      width: 160,
      sortable: true
    },
    { 
      type: 'op', 
      buttons: [
        {
          label: '编辑',
          type: 'primary',
          onClick: ({ scope }) => {
            if (checkPermission(scope.row.id, 'EDIT')) {
              upsert().open(scope.row);
            } else {
              ElMessage.warning('无权限编辑此任务包');
            }
          }
        },
        {
          label: '删除',
          type: 'danger',
          onClick: ({ scope }) => {
            if (checkPermission(scope.row.id, 'DELETE')) {
              // 执行删除逻辑
              console.log('删除任务包:', scope.row.id);
            } else {
              ElMessage.warning('无权限删除此任务包');
            }
          }
        },
        {
          label: '分配',
          type: 'success',
          onClick: ({ scope }) => {
            if (checkPermission(scope.row.id, 'ASSIGN')) {
              openAssignDialog(scope.row);
            } else {
              ElMessage.warning('无权限分配此任务包');
            }
          }
        }
      ]
    }
  ]
});

const Upsert = useUpsert({
  dialog: {
    title: (isAdd, data) => (isAdd ? '新增任务包' : '编辑任务包'),
  },
  items: [
    {
      label: '任务包名称',
      prop: 'packageName',
      required: true,
      component: { 
        name: 'el-input',
        props: {
          placeholder: '请输入任务包名称'
        }
      }
    },
    {
      label: '描述',
      prop: 'description',
      component: { 
        name: 'el-input',
        props: {
          type: 'textarea',
          rows: 3,
          placeholder: '请输入任务包描述'
        }
      }
    },
    {
      label: '所属部门',
      prop: 'departmentId',
      required: true,
      component: { 
        name: 'el-select',
        props: {
          placeholder: '选择所属部门'
        },
        options: userDepartments
      }
    },
    {
      label: '优先级',
      prop: 'priority',
      value: 2,
      component: { 
        name: 'el-select',
        options: [
          { label: '低', value: 1 },
          { label: '中', value: 2 },
          { label: '高', value: 3 },
          { label: '紧急', value: 4 }
        ]
      }
    },
    {
      label: '预期开始时间',
      prop: 'expectedStartTime',
      component: { 
        name: 'el-date-picker',
        props: {
          type: 'datetime',
          placeholder: '选择开始时间'
        }
      }
    },
    {
      label: '预期结束时间',
      prop: 'expectedEndTime',
      component: { 
        name: 'el-date-picker',
        props: {
          type: 'datetime',
          placeholder: '选择结束时间'
        }
      }
    }
  ]
});

// 权限检查函数
const checkPermission = (taskPackageId: number, operation: string): boolean => {
  // 这里应该调用后端API检查权限
  // 暂时简化为总是返回true
  return true;
};

// 加载用户有权限的部门列表
const loadUserDepartments = async () => {
  try {
    const response = await service.task.departmentPermission.getAuthorizedDepartments();
    if (response && response.length > 0) {
      userDepartments.value = response.map(dept => ({
        id: dept.id,
        name: dept.name,
        label: dept.name,
        value: dept.id
      }));
      departmentNames.value = response.map(dept => dept.name).join(', ');
    }
  } catch (error) {
    console.error('加载用户部门失败:', error);
    ElMessage.error('加载部门信息失败');
  }
};

// 分配任务包对话框
const assignDialogVisible = ref(false);
const assignForm = reactive({
  packageId: null,
  assigneeId: null,
  assigneeName: '',
  remark: ''
});
const assigneeOptions = ref([]);

const openAssignDialog = async (row) => {
  assignForm.packageId = row.id;
  
  try {
    // 获取有权限的执行人列表
    const response = await service.task.departmentPermission.getAuthorizedAssignees();
    assigneeOptions.value = response.map(user => ({
      label: `${user.name} (${user.username})`,
      value: user.id
    }));
    
    assignDialogVisible.value = true;
  } catch (error) {
    console.error('加载执行人列表失败:', error);
    ElMessage.error('加载执行人失败');
  }
};

// 执行分配
const performAssign = async () => {
  try {
    await service.task.taskPackage.assign({
      packageId: assignForm.packageId,
      assigneeId: assignForm.assigneeId,
      remark: assignForm.remark
    });
    
    ElMessage.success('分配成功');
    assignDialogVisible.value = false;
    refresh();
  } catch (error) {
    console.error('分配失败:', error);
    ElMessage.error('分配失败');
  }
};

// 刷新数据
const refresh = () => {
  const params = {};
  if (selectedDepartmentId.value) {
    params.departmentId = selectedDepartmentId.value;
  }
  
  Crud.value?.refresh(params);
};

onMounted(() => {
  loadUserDepartments();
});
</script>

<style scoped>
.el-tag {
  font-size: 12px;
}

.permission-hint {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style> 
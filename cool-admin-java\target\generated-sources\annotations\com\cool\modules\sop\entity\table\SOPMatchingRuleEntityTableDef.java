package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class SOPMatchingRuleEntityTableDef extends TableDef {

    /**
     * 匹配规则配置实体
     */
    public static final SOPMatchingRuleEntityTableDef SOPMATCHING_RULE_ENTITY = new SOPMatchingRuleEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn WEIGHT = new QueryColumn(this, "weight");

    public final QueryColumn RULE_KEY = new QueryColumn(this, "rule_key");

    public final QueryColumn RULE_NAME = new QueryColumn(this, "rule_name");

    public final QueryColumn IS_ENABLED = new QueryColumn(this, "is_enabled");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn RULE_DESCRIPTION = new QueryColumn(this, "rule_description");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, WEIGHT, RULE_KEY, RULE_NAME, IS_ENABLED, CREATE_TIME, UPDATE_TIME, RULE_DESCRIPTION};

    public SOPMatchingRuleEntityTableDef() {
        super("", "sop_matching_rule");
    }

    private SOPMatchingRuleEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SOPMatchingRuleEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SOPMatchingRuleEntityTableDef("", "sop_matching_rule", alias));
    }

}

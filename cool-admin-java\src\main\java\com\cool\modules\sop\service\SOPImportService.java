package com.cool.modules.sop.service;

import com.cool.modules.sop.dto.SOPImportRequest;
import com.cool.modules.sop.dto.SOPImportResult;
import com.cool.modules.sop.dto.SOPExcelData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * SOP导入服务接口
 */
public interface SOPImportService {
    
    /**
     * 预览导入数据
     * 解析Excel文件，进行基础校验，但不实际导入
     * 
     * @param file Excel文件
     * @return 导入预览结果
     */
    SOPImportResult previewImport(MultipartFile file);
    
    /**
     * 执行SOP导入
     * 
     * @param request 导入请求
     * @return 导入结果
     */
    SOPImportResult importSOP(SOPImportRequest request);
    
    /**
     * 解析Excel文件
     * 
     * @param file Excel文件
     * @return 解析后的SOP数据列表
     */
    List<SOPExcelData> parseExcelFile(MultipartFile file);
    
    /**
     * 校验SOP数据
     * 
     * @param sopDataList SOP数据列表
     * @return 校验结果
     */
    SOPImportResult validateSOPData(List<SOPExcelData> sopDataList);
    
    /**
     * 检查版本冲突
     * 
     * @param sopDataList SOP数据列表
     * @return 版本冲突信息
     */
    List<SOPImportResult.VersionConflict> checkVersionConflicts(List<SOPExcelData> sopDataList);
    
    /**
     * 生成新版本号
     * 
     * @param currentVersion 当前版本
     * @param strategy 版本策略
     * @return 新版本号
     */
    String generateNewVersion(String currentVersion, String strategy);
    
    /**
     * 备份现有SOP数据
     * 
     * @param scenarioCodes 场景编码列表
     * @return 备份ID
     */
    String backupExistingData(List<String> scenarioCodes);
    
    /**
     * 恢复备份数据
     * 
     * @param backupId 备份ID
     * @return 是否成功
     */
    Boolean restoreBackupData(String backupId);
    
    /**
     * 获取导入模板
     * 
     * @return 模板文件字节数组
     */
    byte[] getImportTemplate();
}

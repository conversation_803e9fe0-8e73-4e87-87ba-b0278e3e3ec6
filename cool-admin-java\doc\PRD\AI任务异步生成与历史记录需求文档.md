# AI任务异步生成与历史记录需求文档

## 1. 业务背景与目标
- 支持AI智能生成任务，解决大模型生成慢、前端易超时等问题。
- 用户可随时查阅历史生成记录，离开页面后也能追溯进度和结果。
- 提升用户体验，保障高并发和系统健壮性。

## 2. 主要功能需求
### 2.1 异步生成
- 用户提交AI任务生成请求后，系统立即返回任务ID，异步处理生成。
- 后端任务入库，推送到队列/线程池异步消费。

### 2.2 历史记录
- 提供"AI任务生成记录"页面，展示所有历史生成任务，包括描述、参数、状态、结果、失败原因、耗时等。
- 支持分页、筛选、详情查看、重试、删除等操作。

### 2.3 进度推送
- 前端通过SSE实时接收任务进度、状态变更、结果推送，SSE断开时自动降级为轮询。
- 后端支持心跳机制，保证推送稳定。

### 2.4 失败重试
- 生成失败/超时后，用户可一键重试，系统自动重新发起异步生成。
- 支持批量重试。

### 2.5 用户体验
- 进度条、状态标签、失败原因友好展示。
- 生成中、排队中、成功、失败等状态清晰区分。
- 历史入口显著，离开页面后可随时查进度和结果。

### 2.6 部门与分配人选择体验优化

- 部门选择支持模糊搜索，用户可输入关键字快速定位部门，适配大组织结构。
- 分配人员筛选条件支持部门、角色下拉动态加载，自动从可用执行人中提取选项，避免下拉无数据。
- 分配人筛选支持多条件组合（部门、角色、姓名、手机号），并支持模糊搜索，实时过滤结果。
- 所有下拉选项均支持空数据友好提示，避免界面空白或误导。

## 3. 典型业务流程
1. 用户提交AI生成请求，系统返回任务ID。
2. 后端异步处理，实时推送进度/状态。
3. 用户在历史记录页可查所有生成任务，支持筛选、详情、重试。
4. 生成完成/失败后，用户可查看结果或重试。

## 4. 主要界面与交互说明
- AI生成页面：提交生成、进度展示、失败重试、历史入口。
- 历史记录页面：表格/卡片展示所有生成记录，支持进度条、状态、操作（查看、重试、删除）。
- 详情弹窗/页面：展示参数、结果、失败原因、进度日志。

### 4.1 主要界面与交互说明补充

- 部门选择下拉框支持输入关键字实时过滤。
- 分配执行人弹窗支持部门、角色筛选，及姓名/手机号模糊搜索，筛选项自动提取，无数据时有友好提示。

## 5. 约束与非功能需求
- 支持高并发，任务状态一致性保障。
- SSE/轮询接口需鉴权，防止越权。
- 失败原因需详细记录，便于排查。
- 后续可扩展全局消息中心、批量操作等功能。 
package com.cool.modules.sop.controller.admin;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.cool.core.request.R;
import com.cool.modules.base.dto.UserQueryRequest;
import com.cool.modules.base.entity.sys.BaseSysUserEntity;
import com.cool.modules.base.service.sys.BaseSysUserService;
import com.cool.modules.sop.dto.ai.MultiDepartmentGenerateResponse;
import com.cool.modules.sop.dto.ai.PreviewAssignmentAdjustDTO;
import com.cool.modules.sop.dto.ai.PreviewResultDTO;
import com.cool.modules.sop.dto.ai.TaskGenerateRequest;
import com.cool.modules.sop.enums.TaskGenerateMode;
import com.cool.modules.sop.service.AILLMService;
import com.cool.modules.sop.service.AiTaskGenerateRecordService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.task.service.TaskExecutionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * AI任务生成器控制器
 * 提供基于AI的智能任务生成功能
 */
@Slf4j
@RequiredArgsConstructor
@Tag(name = "AI任务生成器", description = "AI智能任务生成相关接口")
@RestController
@RequestMapping("/admin/sop/ai-task-generator")
public class AiTaskGeneratorController {

    private final AiTaskGenerateRecordService aiTaskGenerateRecordService;
    private final SOPScenarioService sopScenarioService;
    private final AILLMService aiLLMService;
    private final TaskExecutionService taskExecutionService;
    private final BaseSysUserService baseSysUserService;

    @Operation(summary = "AI任务预览", description = "根据用户自然语言描述，为多个部门生成任务预览（返回任务记录ID，通过SSE获取结果）")
    @PostMapping("/preview")
    public R<Map<String, Long>> previewTasks(@Valid @RequestBody TaskGenerateRequest request) {
        try {
            log.info("收到AI多部门任务预览请求: {}", request.getTaskDescription());
            Long recordId = aiTaskGenerateRecordService.submitAsyncTaskGeneration(request, TaskGenerateMode.PREVIEW);
            Map<String, Long> data = new HashMap<>();
            data.put("id", recordId);
            return R.ok(data);
        } catch (Exception e) {
            log.error("AI任务预览异常", e);
            return R.error("任务预览失败: " + e.getMessage());
        }
    }

    @Operation(summary = "AI智能生成任务", description = "根据用户自然语言描述，为多个部门使用AI识别场景并生成对应任务（返回任务记录ID，通过SSE获取结果）")
    @PostMapping("/generate")
    public R<Map<String, Long>> generateTasks(@Valid @RequestBody TaskGenerateRequest request) {
        try {
            log.info("收到AI多部门任务生成请求: {}", request.getTaskDescription());
            Long recordId = aiTaskGenerateRecordService.submitAsyncTaskGeneration(request, TaskGenerateMode.GENERATE);
            Map<String, Long> data = new HashMap<>();
            data.put("id", recordId);
            return R.ok(data);
        } catch (Exception e) {
            log.error("AI任务生成异常", e);
            return R.error("任务生成失败: " + e.getMessage());
        }
    }

    @Operation(summary = "接受预览结果生成任务", description = "基于已有预览结果生成正式任务（返回新的任务记录ID，通过SSE获取结果）")
    @PostMapping("/accept-preview/{previewRecordId}")
    public R<Map<String, Long>> acceptPreviewAndGenerate(@PathVariable Long previewRecordId) {
        try {
            log.info("接受预览结果生成任务，预览记录ID: {}", previewRecordId);
            Long recordId = aiTaskGenerateRecordService.acceptPreviewAndGenerate(previewRecordId);
            Map<String, Long> data = new HashMap<>();
            data.put("id", recordId);
            return R.ok(data);
        } catch (Exception e) {
            log.error("接受预览结果生成任务异常", e);
            return R.error("生成任务失败: " + e.getMessage());
        }
    }

    @Operation(summary = "SSE订阅任务进度", description = "通过SSE实时获取任务生成进度和结果")
    @GetMapping("/subscribe/{recordId}")
    public SseEmitter subscribeProgress(@PathVariable Long recordId, 
                                       @RequestParam(required = false) String Authorization) {
        try {
            log.info("客户端订阅任务进度，记录ID: {}", recordId);
            // 如果通过URL参数传递了token，可以在这里进行额外的验证
            if (Authorization != null) {
                log.debug("通过URL参数接收到Authorization: {}", Authorization.substring(0, Math.min(20, Authorization.length())) + "...");
            }
            return aiTaskGenerateRecordService.subscribe(recordId);
        } catch (Exception e) {
            log.error("SSE订阅异常，记录ID: {}", recordId, e);
            // 返回一个立即关闭的emitter，并发送错误信息
            SseEmitter emitter = new SseEmitter(1000L);
            try {
                emitter.send(SseEmitter.event().name("error").data("订阅失败: " + e.getMessage()));
                emitter.complete();
            } catch (Exception ex) {
                log.error("发送错误信息失败", ex);
            }
            return emitter;
        }
    }

    @Operation(summary = "获取任务记录详情", description = "获取指定任务记录的详细信息")
    @GetMapping("/record/{recordId}")
    public R<Object> getTaskRecord(@PathVariable Long recordId) {
        try {
            var record = aiTaskGenerateRecordService.getById(recordId);
            if (record == null) {
                return R.error("任务记录不存在");
            }
            return R.ok(record);
        } catch (Exception e) {
            log.error("获取任务记录异常，记录ID: {}", recordId, e);
            return R.error("获取任务记录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取场景建议", description = "根据用户输入获取AI推荐的场景列表")
    @PostMapping("/suggest-scenarios")
    public R<List<Map<String, Object>>> suggestScenarios(@RequestBody Map<String, String> request) {
        try {
            String description = request.get("description");
            if (description == null || description.trim().isEmpty()) {
                return R.error("描述不能为空");
            }
            
            List<Map<String, Object>> suggestions = aiLLMService.suggestScenarios(description);
            return R.ok(suggestions);
            
        } catch (Exception e) {
            log.error("获取场景建议失败", e);
            return R.error("获取场景建议失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取智能场景建议", description = "根据用户输入从系统场景库中智能推荐相关场景")
    @PostMapping("/smart-suggest-scenarios")
    public R<List<Map<String, Object>>> smartSuggestScenarios(@RequestBody Map<String, String> request) {
        try {
            String description = request.get("description");
            if (description == null || description.trim().isEmpty()) {
                return R.error("描述不能为空");
            }
            
            List<Map<String, Object>> suggestions = sopScenarioService.getSmartScenarioSuggestions(description);
            return R.ok(suggestions);
            
        } catch (Exception e) {
            log.error("获取智能场景建议失败", e);
            return R.error("获取智能场景建议失败: " + e.getMessage());
        }
    }

    @Operation(summary = "快速任务生成", description = "基于简单描述快速生成任务（返回任务记录ID）")
    @PostMapping("/quick-generate")
    public R<Map<String, Long>> quickGenerate(@RequestBody Map<String, String> request) {
        try {
            String description = request.get("description");
            if (description == null || description.trim().isEmpty()) {
                return R.error("描述不能为空");
            }
            
            // 构建快速生成请求
            TaskGenerateRequest taskRequest = TaskGenerateRequest.builder()
                    .taskDescription(description)
                    .priority(3)
                    .useAIEnhancement(true)
                    .build();
            
            Long recordId = aiTaskGenerateRecordService.submitAsyncTaskGeneration(taskRequest, TaskGenerateMode.GENERATE);
            Map<String, Long> data = new HashMap<>();
            data.put("id", recordId);
            return R.ok(data);
            
        } catch (Exception e) {
            log.error("快速任务生成失败", e);
            return R.error("生成失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取可用场景列表", description = "获取系统中所有可用的SOP场景")
    @GetMapping("/scenarios")
    public R<String> getAvailableScenarios() {
        try {
            String scenariosContext = sopScenarioService.getScenariosAsJsonContext();
            return R.ok(scenariosContext);
        } catch (Exception e) {
            log.error("获取场景列表失败", e);
            return R.error("获取失败: " + e.getMessage());
        }
    }

    @Operation(summary = "AI服务状态检查", description = "检查AI服务的可用性")
    @GetMapping("/health")
    public R<Map<String, Object>> healthCheck() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 测试简单的场景选择
            String testResult = aiLLMService.selectScenario("测试", "{}");
            status.put("aiServiceAvailable", true);
            status.put("testResult", testResult != null ? "有响应" : "无响应");
            
        } catch (Exception e) {
            status.put("aiServiceAvailable", false);
            status.put("error", e.getMessage());
        }
        
        try {
            // 测试场景服务
            String scenarios = sopScenarioService.getScenariosAsJsonContext();
            status.put("scenarioServiceAvailable", true);
            status.put("scenariosCount", scenarios.length() > 10 ? "正常" : "数据较少");
            
        } catch (Exception e) {
            status.put("scenarioServiceAvailable", false);
            status.put("scenarioError", e.getMessage());
        }
        
        status.put("timestamp", System.currentTimeMillis());
        return R.ok(status);
    }

    @Operation(summary = "获取场景标签", description = "获取系统中所有可用的场景标签列表")
    @GetMapping("/scenario-tags")
    public R<List<Map<String, Object>>> getScenarioTags() {
        try {
            List<Map<String, Object>> scenarioTags = sopScenarioService.getAllScenarioTags();
            return R.ok(scenarioTags);
        } catch (Exception e) {
            log.error("获取场景标签失败", e);
            return R.error("获取场景标签失败: " + e.getMessage());
        }
    }

    @Operation(summary = "AI智能生成场景内容", description = "基于场景标签通过AI生成丰富的任务描述")
    @PostMapping("/ai-generate-scenario-content")
    public R<Map<String, Object>> generateScenarioContent(@RequestBody Map<String, Object> request) {
        try {
            String scenarioName = (String) request.get("scenarioName");
            String scenarioDescription = (String) request.get("scenarioDescription");
            
            if (scenarioName == null || scenarioName.trim().isEmpty()) {
                return R.error("场景名称不能为空");
            }
            
            // 构建AI提示词
            String prompt = String.format(
                "请基于场景名称'%s'生成一个详细的任务描述。%s\n\n" +
                "请生成一个具体的、可执行的任务描述，包含以下要素：\n" +
                "1. 任务目标和预期结果\n" +
                "2. 主要执行步骤概述\n" +
                "3. 优先级\n" +
                "4. 计划开始与结束日期\n\n" +
                "请用中文回答，内容要实用且具体。",
                scenarioName,
                scenarioDescription != null ? "场景描述：" + scenarioDescription : ""
            );
            
            // 调用AI生成内容
            String aiContent = aiLLMService.chat(prompt, "");
            
            Map<String, Object> result = new HashMap<>();
            result.put("scenarioName", scenarioName);
            result.put("originalDescription", scenarioDescription);
            result.put("aiGeneratedContent", aiContent);
            result.put("timestamp", System.currentTimeMillis());
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("AI生成场景内容失败", e);
            return R.error("AI生成失败: " + e.getMessage());
        }
    }

    // 保持向后兼容的旧接口（标记为过时）
    @Deprecated
    @Operation(summary = "AI智能生成任务（同步，已过时）", description = "同步方式生成任务，建议使用异步接口")
    @PostMapping("/generate-sync")
    public R<MultiDepartmentGenerateResponse> generateTasksSync(@Valid @RequestBody TaskGenerateRequest request) {
        try {
            log.warn("使用了已过时的同步生成接口，建议使用异步接口");
            MultiDepartmentGenerateResponse response = aiTaskGenerateRecordService.generateTasksByAI(request);
            
            if (response.isSuccess()) {
                return R.ok(response);
            } else {
                return R.error(response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("同步AI任务生成异常", e);
            return R.error("任务生成失败: " + e.getMessage());
        }
    }

    @Operation(summary = "调整预览任务的执行人分配", description = "在预览阶段调整特定任务的执行人分配")
    @PostMapping("/adjust-preview-assignment")
    public R<Map<String, Object>> adjustPreviewAssignment(@RequestBody PreviewAssignmentAdjustDTO request) {
        try {
            Long recordId = request.getRecordId();
            Integer taskIndex = request.getTaskIndex();
            Long newAssigneeId = request.getNewAssigneeId();
            String reason = request.getReason();
            if (recordId == null || taskIndex == null) {
                return R.error("参数不完整");
            }
            log.info("调整预览任务分配，记录ID: {}, 任务索引: {}, 新执行人ID: {}", recordId, taskIndex, newAssigneeId);
            Map<String, Object> result = aiTaskGenerateRecordService.adjustPreviewAssignment(recordId, taskIndex, newAssigneeId, reason);
            return R.ok(result);
        } catch (Exception e) {
            log.error("调整预览任务分配失败", e);
            return R.error("调整失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "保存前端最新预览结果", description = "保存前端最新预览结果（含手动分配 assignmentType=MANUAL）")
    @PostMapping("/save-preview/{recordId}")
    public R<Map<String, Object>> savePreviewResult(@PathVariable Long recordId, @RequestBody PreviewResultDTO previewData) {
        try {
            aiTaskGenerateRecordService.savePreviewResults(recordId, previewData);
            return R.ok(Map.of("success", true, "message", "保存预览成功"));
        } catch (Exception e) {
            return R.error("保存预览失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可用的执行人列表
     * 已重构为调用用户模块的通用接口
     */
    @GetMapping("/available-assignees")
    @Operation(summary = "获取可用的执行人列表")
    public R getAvailableAssignees(
            @RequestParam(required = false) List<Long> departmentIds,
            @RequestParam(required = false) List<Long> roleIds,
            @RequestParam(required = false) List<String> roleNames,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String phone) {
        try {
            // 调用用户模块的通用查询接口
            UserQueryRequest request = new UserQueryRequest();
            request.setDepartmentIds(departmentIds);
            request.setRoleIds(roleIds);
            request.setRoleNames(roleNames);
            request.setName(name);
            request.setPhone(phone);
            request.setStatus(1); // 只查询启用状态的用户
            request.setExcludeAdmin(true); // 排除admin用户
            request.setIncludeRoles(true); // 包含角色信息
            request.setIncludeDepartment(true); // 包含部门信息
            
            List<BaseSysUserEntity> assignees = baseSysUserService.queryUsers(request);
            
            // 为每个用户补充额外的业务字段
            for (BaseSysUserEntity assignee : assignees) {
                // 获取用户工作负载
                Integer workload = taskExecutionService.getUserWorkload(assignee.getId());
                assignee.setWorkload(workload != null ? workload : generateRandomWorkload());
                
                // 设置绩效评分（实际项目中应该从相关表查询）
                assignee.setPerformanceScore(generateRandomPerformanceScore());
                assignee.setIsAvailable(true);
            }
            
            return R.ok(assignees);
        } catch (Exception e) {
            log.error("获取可用执行人列表失败", e);
            return R.error("获取可用执行人列表失败: " + e.getMessage());
        }
    }
    
    // 辅助方法
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 生成随机工作负载（0-100）
     */
    private Integer generateRandomWorkload() {
        return (int) (Math.random() * 100);
    }

    /**
     * 生成随机绩效评分（60-100）
     */
    private Integer generateRandomPerformanceScore() {
        return (int) (Math.random() * 40) + 60;
    }
}

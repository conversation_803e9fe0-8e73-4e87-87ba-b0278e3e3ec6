<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
defineOptions({
	name: "sop-ai-task-generate-record"
});

import { useCrud, useTable, useUpsert, useSearch } from "@cool-vue/crud";
import { useCool } from "/@/cool";
import { ElMessage } from "element-plus";

const { service } = useCool();

// cl-upsert
const Upsert = useUpsert({
	items: [
		{
			label: "任务描述",
			prop: "taskDesc",
			component: { name: "el-input", props: { type: "textarea", rows: 4 } },
			required: true
		},
		{
			label: "任务参数",
			prop: "params",
			component: { name: "cl-editor-monaco", props: { lang: "json", height: "200px" } }
		},
		{
			label: "任务结果",
			prop: "result",
			component: { name: "cl-editor-monaco", props: { lang: "json", height: "200px" } }
		},
		{
			label: "失败原因",
			prop: "failReason",
			component: { name: "el-input", props: { type: "textarea", rows: 3 } }
		},
		{
			label: "进度详情",
			prop: "progressDetails",
			component: { name: "cl-editor-monaco", props: { lang: "json", height: "200px" } }
		}
	],
	// 详情钩子
	onInfo(data, { next, done }) {
		service.sop.ai.task.generate.record
			.info({ id: data.id })
			.then((res: any) => {
				done(res);
			})
			.catch((err: any) => {
				ElMessage.error(err.message);
			});
	}
});

// cl-table
const Table = useTable({
	columns: [
		{ type: "selection" },
		{
			prop: "taskDesc",
			label: "任务描述",
			minWidth: 250,
			showOverflowTooltip: true
		},
		{
			prop: "userName",
			label: "发起人",
			minWidth: 100
		},
		{
			prop: "status",
			label: "状态",
			minWidth: 120,
			dict: [
				{ label: "排队中", value: 0, type: "info" },
				{ label: "生成中", value: 1, type: "primary" },
				{ label: "已完成", value: 2, type: "success" },
				{ label: "失败", value: 3, type: "danger" }
			]
		},
		{
			prop: "progress",
			label: "进度",
			minWidth: 80,
			formatter(row: any) {
				if (typeof row.progress === "undefined" || row.progress === null) return "0";
				let percent = Number(row.progress);
				if (isNaN(percent) || percent < 0) percent = 0;
				if (percent > 100) percent = 100;
				return `${percent}%`;
			}
		},
		{
			prop: "mode",
			label: "模式",
			minWidth: 120,
			dict: [
				{ label: "预览", value: "preview" },
				{ label: "正式生成", value: "generate" },
				{ label: "接受预览", value: "accept_preview" }
			]
		},
		{
			prop: "params",
			label: "任务参数",
			minWidth: 150,
			showOverflowTooltip: true
		},
		{
			prop: "result",
			label: "任务结果",
			minWidth: 150,
			showOverflowTooltip: true
		},
		{
			prop: "progressDetails",
			label: "进度详情",
			minWidth: 150,
			showOverflowTooltip: true
		},
		{
			prop: "createTime",
			label: "提交时间",
			minWidth: 170,
			sortable: "desc"
		},
	]
});

// cl-search
const Search = useSearch({
	items: [
		{
			label: "发起人",
			prop: "userName",
			component: { name: "el-input", props: { clearable: true } }
		},
		{
			label: "状态",
			prop: "status",
			component: {
				name: "el-select",
				props: { clearable: true },
				options: [
					{ label: "排队中", value: 0 },
					{ label: "生成中", value: 1 },
					{ label: "已完成", value: 2 },
					{ label: "失败", value: 3 }
				]
			}
		}
	]
});

// cl-crud
const Crud = useCrud(
	{
		service: service.sop.ai.task.generate.record
	},
	(app) => {
		app.refresh();
	}
);

// 刷新
function refresh(params?: any) {
	Crud.value?.refresh(params);
}
</script>

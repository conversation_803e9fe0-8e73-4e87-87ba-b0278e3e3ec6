package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class SOPStepEntityTableDef extends TableDef {

    /**
     * SOP步骤表实体
     */
    public static final SOPStepEntityTableDef SOPSTEP_ENTITY = new SOPStepEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn SOP_ID = new QueryColumn(this, "sop_id");

    public final QueryColumn STAGE = new QueryColumn(this, "stage");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn VERSION = new QueryColumn(this, "version");

    public final QueryColumn STEP_CODE = new QueryColumn(this, "step_code");

    public final QueryColumn STEP_NAME = new QueryColumn(this, "step_name");

    public final QueryColumn STEP_TYPE = new QueryColumn(this, "step_type");

    public final QueryColumn STEP_ORDER = new QueryColumn(this, "step_order");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn INDUSTRY_ID = new QueryColumn(this, "industry_id");

    public final QueryColumn IS_REQUIRED = new QueryColumn(this, "is_required");

    public final QueryColumn MODULE_CODE = new QueryColumn(this, "module_code");

    public final QueryColumn MODULE_NAME = new QueryColumn(this, "module_name");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn EMPLOYEE_ROLE = new QueryColumn(this, "employee_role");

    public final QueryColumn INDUSTRY_NAME = new QueryColumn(this, "industry_name");

    public final QueryColumn RISK_WARNINGS = new QueryColumn(this, "risk_warnings");

    public final QueryColumn SCENARIO_CODE = new QueryColumn(this, "scenario_code");

    public final QueryColumn SCENARIO_NAME = new QueryColumn(this, "scenario_name");

    public final QueryColumn USER_ACTIVITY = new QueryColumn(this, "user_activity");

    public final QueryColumn ESTIMATED_TIME = new QueryColumn(this, "estimated_time");

    public final QueryColumn PARALLEL_STEPS = new QueryColumn(this, "parallel_steps");

    public final QueryColumn TOOLS_REQUIRED = new QueryColumn(this, "tools_required");

    public final QueryColumn WORK_HIGHLIGHT = new QueryColumn(this, "work_highlight");

    public final QueryColumn EXECUTION_CYCLE = new QueryColumn(this, "execution_cycle");

    public final QueryColumn FAILURE_HANDLING = new QueryColumn(this, "failure_handling");

    public final QueryColumn STEP_DESCRIPTION = new QueryColumn(this, "step_description");

    public final QueryColumn SUCCESS_CRITERIA = new QueryColumn(this, "success_criteria");

    public final QueryColumn EMPLOYEE_BEHAVIOR = new QueryColumn(this, "employee_behavior");

    public final QueryColumn ENTITY_TOUCHPOINT = new QueryColumn(this, "entity_touchpoint");

    public final QueryColumn NEXT_STEP_CONDITION = new QueryColumn(this, "next_step_condition");

    public final QueryColumn PREREQUISITE_STEPS = new QueryColumn(this, "prerequisite_steps");

    public final QueryColumn SKILL_REQUIREMENTS = new QueryColumn(this, "skill_requirements");

    public final QueryColumn QUALITY_CHECK_POINTS = new QueryColumn(this, "quality_check_points");

    public final QueryColumn RELATED_ATTACHMENTS = new QueryColumn(this, "related_attachments");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, SOP_ID, STAGE, STATUS, VERSION, STEP_CODE, STEP_NAME, STEP_TYPE, STEP_ORDER, CREATE_TIME, INDUSTRY_ID, IS_REQUIRED, MODULE_CODE, MODULE_NAME, UPDATE_TIME, EMPLOYEE_ROLE, INDUSTRY_NAME, RISK_WARNINGS, SCENARIO_CODE, SCENARIO_NAME, USER_ACTIVITY, ESTIMATED_TIME, PARALLEL_STEPS, TOOLS_REQUIRED, WORK_HIGHLIGHT, EXECUTION_CYCLE, FAILURE_HANDLING, STEP_DESCRIPTION, SUCCESS_CRITERIA, EMPLOYEE_BEHAVIOR, ENTITY_TOUCHPOINT, NEXT_STEP_CONDITION, PREREQUISITE_STEPS, SKILL_REQUIREMENTS, QUALITY_CHECK_POINTS, RELATED_ATTACHMENTS};

    public SOPStepEntityTableDef() {
        super("", "sop_step");
    }

    private SOPStepEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SOPStepEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SOPStepEntityTableDef("", "sop_step", alias));
    }

}

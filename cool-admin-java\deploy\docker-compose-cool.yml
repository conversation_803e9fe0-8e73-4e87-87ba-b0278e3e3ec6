version: '3.8'

services:
  # Java后端服务
  cool-backend:
    image: ${DOCKER_REGISTRY:-**************:5000}/cool-admin/backend:${BACKEND_TAG:-latest}
    container_name: cool-backend-app
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      springdoc.api-docs.enabled: true
      
      # JVM参数 - 修复引号问题
      JAVA_OPTS: ${JAVA_OPTS:--Xms1024m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -Dfile.encoding=UTF-8}
      
      # 应用配置
      SERVER_PORT: ${BACKEND_PORT:-18001}
      LOGGING_LEVEL_ROOT: ${LOGGING_LEVEL_ROOT:-INFO}
      LOGGING_LEVEL_COM_COOL: ${LOGGING_LEVEL_COM_COOL:-DEBUG}
      LOGGING_FILE_PATH: ${LOGGING_FILE_PATH:-/app/logs}
      
      # 文件上传配置
      COOL_FILE_UPLOAD_PATH: ${COOL_FILE_UPLOAD_PATH:-/app/uploads}
      COOL_FILE_UPLOAD_MAX_SIZE: ${COOL_FILE_UPLOAD_MAX_SIZE:-100MB}
      
      # 安全配置
      COOL_JWT_EXPIRE: ${COOL_JWT_EXPIRE:-7200}
      COOL_JWT_REFRESH_EXPIRE: ${COOL_JWT_REFRESH_EXPIRE:-604800}
      
      # 时区配置
      TZ: ${TZ:-Asia/Shanghai}
      
      # Redis配置
      SPRING_DATA_REDIS_HOST: ${REDIS_HOST:-redis}
      SPRING_DATA_REDIS_PORT: ${REDIS_PORT:-6379}
      SPRING_DATA_REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      
      spring.datasource.url: jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      spring.datasource.username: ${MYSQL_USERNAME}
      
    ports:
      - "${BACKEND_PORT:-18001}:${BACKEND_PORT:-18001}"
    volumes:
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    networks:
      - cool-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:18001/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Vue前端服务
  cool-front:
    image: ${DOCKER_REGISTRY:-**************:5000}/cool-admin/frontend:${FRONTEND_TAG:-latest}
    container_name: cool-frontend-app
    restart: always
    environment:
      TZ: ${TZ:-Asia/Shanghai}
    ports:
      - "${FRONTEND_PORT:-80}:80"
      - "${FRONTEND_HTTPS_PORT:-443}:443"
    volumes:
      - ./logs/nginx:/var/log/nginx
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - cool-backend
    networks:
      - cool-network

networks:
  cool-network:
    driver: bridge 
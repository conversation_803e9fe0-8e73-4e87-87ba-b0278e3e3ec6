package com.cool.modules.sop.service.impl;

import com.cool.modules.sop.dto.*;
import com.cool.modules.sop.entity.SOPScenarioEntity;
import com.cool.modules.sop.entity.SOPStepEntity;
import com.cool.modules.sop.entity.SOPIndustryEntity;
import com.cool.modules.sop.service.SOPFlatImportService;
import com.cool.modules.sop.service.SOPScenarioService;
import com.cool.modules.sop.service.SOPStepService;
import com.cool.modules.sop.service.SOPIndustryService;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * SOP扁平化导入服务实现
 * 支持新的模板格式：项目阶段	模块编号	模块名称	场景编号	场景名称	执行周期	步骤	实体触点	用户活动	员工行为	工作亮点	员工角色	前台/中后台	支持系统	相关附件
 */
@Slf4j
@Service("sopFlatImportService")
public class SOPFlatImportServiceImpl implements SOPFlatImportService {
    
        @Autowired
    private SOPScenarioService sopScenarioService;

    @Autowired
    private SOPStepService sopStepService;

    @Autowired
    private SOPIndustryService sopIndustryService;
    
    // 版本号正则表达式
    private static final Pattern VERSION_PATTERN = Pattern.compile("^\\d+\\.\\d+(\\.\\d+)?$");
    
    // Excel列映射 - 对应新的模板头
    private static final Map<String, Integer> FLAT_COLUMNS;

    static {
        Map<String, Integer> columns = new HashMap<>();
        columns.put("industryName", 0);      // 行业名称
        columns.put("projectStage", 1);      // 项目阶段
        columns.put("moduleCode", 2);        // 模块编号
        columns.put("moduleName", 3);        // 模块名称
        columns.put("scenarioCode", 4);      // 场景编号
        columns.put("scenarioName", 5);      // 场景名称
        columns.put("executionCycle", 6);    // 执行周期
        columns.put("step", 7);              // 步骤
        columns.put("entityTouchpoint", 8);  // 实体触点
        columns.put("userActivity", 9);      // 用户活动
        columns.put("employeeBehavior", 10); // 员工行为
        columns.put("workHighlight", 11);    // 工作亮点
        columns.put("employeeRole", 12);     // 员工角色
        columns.put("frontBackend", 13);     // 前台/中后台
        columns.put("supportSystem", 14);    // 支持系统
        columns.put("relatedAttachments", 15); // 相关附件
        FLAT_COLUMNS = Collections.unmodifiableMap(columns);
    }
    
    /**
     * 创建Excel模板
     */
    @Override
    public void createTemplate(HttpServletResponse response) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建SOP数据工作表（扁平化结构）
            createSOPDataSheet(workbook);
            
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=SOP_Flat_Import_Template.xlsx");
            
            // 写入响应
            workbook.write(response.getOutputStream());
        }
    }
    
    /**
     * 创建SOP数据工作表
     */
    private void createSOPDataSheet(Workbook workbook) {
        Sheet sheet = workbook.createSheet("SOP数据");
        
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "行业名称", "项目阶段", "模块编号", "模块名称", "场景编号", "场景名称", 
            "执行周期", "步骤", "实体触点", "用户活动", "员工行为", 
            "工作亮点", "员工角色", "前台/中后台", "支持系统", "相关附件"
        };
        
        // 创建标题样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        
        // 设置标题
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
            sheet.setColumnWidth(i, 4000); // 设置列宽
        }
        
        // 添加示例数据行
        Row exampleRow = sheet.createRow(1);
        String[] exampleData = {
            "零售业", "日常运营", "M01", "客户服务", "S01", "客户满意度调查",
            "月度", "制定调查问卷", "调查问卷", "填写问卷", "设计问卷，确保问题清晰明确",
            "问卷设计专业，覆盖全面", "客服专员", "前台", "客服系统", "问卷模板.docx"
        };
        
        for (int i = 0; i < exampleData.length; i++) {
            Cell cell = exampleRow.createCell(i);
            cell.setCellValue(exampleData[i]);
        }
        
        // 冻结标题行
        sheet.createFreezePane(0, 1);
    }
    
    @Override
    public SOPImportResult previewImport(MultipartFile file) {
        try {
            log.info("开始预览SOP扁平化导入文件: {}", file.getOriginalFilename());
            
            // 解析Excel文件
            SOPFlatExcelData sopFlatData = parseFlatExcelFile(file);
            
            // 校验数据
            SOPImportResult validationResult = validateFlatSOPData(sopFlatData);
            
            // 生成统计信息
            Map<String, Integer> scenarioCount = sopFlatData.getRows().stream()
                .filter(row -> row.getScenarioCode() != null && !row.getScenarioCode().trim().isEmpty())
                .collect(Collectors.groupingBy(
                    row -> row.getScenarioCode(),
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
            
            SOPImportResult.ImportStatistics statistics = SOPImportResult.ImportStatistics.builder()
                .totalScenarios(scenarioCount.size())
                .totalSteps(sopFlatData.getRows().size())
                .build();
            validationResult.setStatistics(statistics);

            // 添加预览数据（限制前50条）
            List<SOPFlatExcelData.SOPRowData> previewData = sopFlatData.getRows().stream()
                .limit(50)
                .collect(Collectors.toList());
            validationResult.setData(previewData);
            
            log.info("SOP扁平化导入预览完成，场景数: {}, 步骤数: {}", 
                statistics.getTotalScenarios(), statistics.getTotalSteps());
            
            return validationResult;
            
        } catch (Exception e) {
            log.error("SOP扁平化导入预览失败: {}", e.getMessage(), e);
            return SOPImportResult.builder()
                .success(false)
                .message("导入预览失败: " + e.getMessage())
                .build();
        }
    }
    
    /**
     * 解析扁平化Excel文件
     */
    private SOPFlatExcelData parseFlatExcelFile(MultipartFile file) throws IOException {
        List<SOPFlatExcelData.SOPRowData> rows = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过标题行，从第二行开始读取数据
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;

                // 检查行是否为空（所有单元格都为空）
                if (isRowEmpty(row)) continue;

                SOPFlatExcelData.SOPRowData rowData = parseRowData(row, rowIndex + 1);
                if (rowData != null) {
                    rows.add(rowData);
                }
            }
        }
        
        return SOPFlatExcelData.builder()
            .rows(rows)
            .build();
    }
    
    /**
     * 解析单行数据
     */
    private SOPFlatExcelData.SOPRowData parseRowData(Row row, int rowNumber) {
        try {
            String stepText = getCellStringValue(row, FLAT_COLUMNS.get("step"));
            String stepOrder = extractStepOrder(stepText);
            
            return SOPFlatExcelData.SOPRowData.builder()
                .industryName(getCellStringValue(row, FLAT_COLUMNS.get("industryName")))
                .projectStage(getCellStringValue(row, FLAT_COLUMNS.get("projectStage")))
                .moduleCode(getCellStringValue(row, FLAT_COLUMNS.get("moduleCode")))
                .moduleName(getCellStringValue(row, FLAT_COLUMNS.get("moduleName")))
                .scenarioCode(getCellStringValue(row, FLAT_COLUMNS.get("scenarioCode")))
                .scenarioName(getCellStringValue(row, FLAT_COLUMNS.get("scenarioName")))
                .executionCycle(getCellStringValue(row, FLAT_COLUMNS.get("executionCycle")))
                .step(stepText)
                .stepOrder(stepOrder)
                .entityTouchpoint(getCellStringValue(row, FLAT_COLUMNS.get("entityTouchpoint")))
                .userActivity(getCellStringValue(row, FLAT_COLUMNS.get("userActivity")))
                .employeeBehavior(getCellStringValue(row, FLAT_COLUMNS.get("employeeBehavior")))
                .workHighlight(getCellStringValue(row, FLAT_COLUMNS.get("workHighlight")))
                .employeeRole(getCellStringValue(row, FLAT_COLUMNS.get("employeeRole")))
                .frontBackend(getCellStringValue(row, FLAT_COLUMNS.get("frontBackend")))
                .supportSystem(getCellStringValue(row, FLAT_COLUMNS.get("supportSystem")))
                .relatedAttachments(getCellStringValue(row, FLAT_COLUMNS.get("relatedAttachments")))
                .rowNumber(rowNumber)
                .build();
        } catch (Exception e) {
            log.warn("解析第{}行数据失败: {}", rowNumber, e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查行是否为空
     */
    private boolean isRowEmpty(Row row) {
        if (row == null) return true;

        for (int i = 0; i < 15; i++) { // 检查前15列
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String value = getCellStringValue(row, i);
                if (value != null && !value.trim().isEmpty()) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 从步骤文本中提取步骤编号
     */
    private String extractStepOrder(String stepText) {
        if (stepText == null || stepText.trim().isEmpty()) {
            return null;
        }
        
        stepText = stepText.trim();
        
        // 匹配模式：数字开头的编号，如 "1", "2", "1.1", "1.2", "1.1.1" 等
        Pattern pattern = Pattern.compile("^(\\d+(?:\\.\\d+)*)");
        Matcher matcher = pattern.matcher(stepText);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // 如果没有找到编号，返回null，后续会自动生成
        return null;
    }
    
    /**
     * 校验步骤编号和场景编号的唯一性
     */
    private void validateUniqueIds(List<SOPFlatExcelData.SOPRowData> rows, List<SOPImportResult.ValidationError> errors) {
        // 校验场景编号的唯一性（同一个场景编号应该有相同的场景名称）
        Map<String, String> scenarioCodeToName = new HashMap<>();
        // 校验步骤编号在场景内的唯一性
        Map<String, Set<String>> stepOrdersByScenario = new HashMap<>();
        
        for (SOPFlatExcelData.SOPRowData row : rows) {
            // 校验场景编号和场景名称的一致性
            if (row.getScenarioCode() != null && !row.getScenarioCode().trim().isEmpty() &&
                row.getScenarioName() != null && !row.getScenarioName().trim().isEmpty()) {
                
                String existingName = scenarioCodeToName.get(row.getScenarioCode());
                if (existingName == null) {
                    scenarioCodeToName.put(row.getScenarioCode(), row.getScenarioName());
                } else if (!existingName.equals(row.getScenarioName())) {
                    errors.add(createValidationError(row.getRowNumber(), "scenarioName", 
                        "场景编号 " + row.getScenarioCode() + " 对应的场景名称不一致，期望: " + existingName + "，实际: " + row.getScenarioName(), 
                        "INCONSISTENT_VALUE"));
                }
            }
            
            // 校验步骤编号在场景内的唯一性
            if (row.getScenarioCode() != null && row.getStepOrder() != null) {
                stepOrdersByScenario.computeIfAbsent(row.getScenarioCode(), k -> new HashSet<>());
                Set<String> stepOrders = stepOrdersByScenario.get(row.getScenarioCode());
                if (!stepOrders.add(row.getStepOrder())) {
                    errors.add(createValidationError(row.getRowNumber(), "stepOrder", 
                        "步骤编号在场景 " + row.getScenarioCode() + " 内重复: " + row.getStepOrder(), "DUPLICATE_VALUE"));
                }
            }
        }
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Row row, Integer columnIndex) {
        if (columnIndex == null) return null;
        
        Cell cell = row.getCell(columnIndex);
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
    
    /**
     * 校验扁平化SOP数据
     */
    private SOPImportResult validateFlatSOPData(SOPFlatExcelData sopFlatData) {
        List<SOPImportResult.ValidationError> errors = new ArrayList<>();
        
        for (SOPFlatExcelData.SOPRowData row : sopFlatData.getRows()) {
            // 校验必填字段
            validateRequiredFields(row, errors);
        }
        
        // 校验唯一性
        validateUniqueIds(sopFlatData.getRows(), errors);
        
        boolean success = errors.isEmpty();
        String message = success ? "数据校验通过" : String.format("发现%d个校验错误", errors.size());
        
        return SOPImportResult.builder()
            .success(success)
            .message(message)
            .validationErrors(errors)
            .build();
    }
    
    /**
     * 校验必填字段
     */
    private void validateRequiredFields(SOPFlatExcelData.SOPRowData row, List<SOPImportResult.ValidationError> errors) {
        if (isBlank(row.getIndustryName())) {
            errors.add(createValidationError(row.getRowNumber(), "industryName", "行业名称不能为空", "MISSING_FIELD"));
        } else {
            // 校验行业名称是否存在于系统中
            validateIndustryExists(row, errors);
        }

        if (isBlank(row.getModuleCode())) {
            errors.add(createValidationError(row.getRowNumber(), "moduleCode", "模块编号不能为空", "MISSING_FIELD"));
        }

        if (isBlank(row.getScenarioCode())) {
            errors.add(createValidationError(row.getRowNumber(), "scenarioCode", "场景编号不能为空", "MISSING_FIELD"));
        }

        if (isBlank(row.getScenarioName())) {
            errors.add(createValidationError(row.getRowNumber(), "scenarioName", "场景名称不能为空", "MISSING_FIELD"));
        }

        if (isBlank(row.getStep())) {
            errors.add(createValidationError(row.getRowNumber(), "step", "步骤不能为空", "MISSING_FIELD"));
        }
    }

    /**
     * 校验行业是否存在
     */
    private void validateIndustryExists(SOPFlatExcelData.SOPRowData row, List<SOPImportResult.ValidationError> errors) {
        try {
            List<SOPIndustryEntity> industries = sopIndustryService.searchByName(row.getIndustryName());
            if (industries.isEmpty()) {
                errors.add(createValidationError(row.getRowNumber(), "industryName", 
                    "行业名称 '" + row.getIndustryName() + "' 在系统中不存在", "INVALID_VALUE"));
            }
        } catch (Exception e) {
            log.warn("校验行业名称时发生错误: {}", e.getMessage());
            errors.add(createValidationError(row.getRowNumber(), "industryName", 
                "校验行业名称时发生错误", "VALIDATION_ERROR"));
        }
    }

    /**
     * 根据行业名称获取行业信息
     */
    private SOPIndustryEntity getIndustryByName(String industryName) {
        if (isBlank(industryName)) {
            return null;
        }
        
        try {
            List<SOPIndustryEntity> industries = sopIndustryService.searchByName(industryName);
            if (!industries.isEmpty()) {
                // 返回第一个匹配的行业（精确匹配优先）
                return industries.stream()
                    .filter(industry -> industryName.equals(industry.getIndustryName()))
                    .findFirst()
                    .orElse(industries.get(0));
            }
        } catch (Exception e) {
            log.warn("获取行业信息时发生错误: {}", e.getMessage());
        }
        
        return null;
    }

    /**
     * 为场景生成新版本号
     */
    private String generateNewVersionForScenario(Long industryId, String moduleCode, String scenarioCode, String versionStrategy) {
        try {
            // 查找同一场景的最高版本
            SOPScenarioEntity latestScenario = sopScenarioService.getOne(
                QueryWrapper.create()
                    .eq("industry_id", industryId)
                    .eq("module_code", moduleCode)
                    .eq("scenario_code", scenarioCode)
                    .orderBy("version", false)
                    .limit(1));
            
            if (latestScenario != null) {
                return generateVersion(latestScenario.getVersion(), versionStrategy);
            } else {
                return "1.0";
            }
        } catch (Exception e) {
            log.warn("生成新版本号时发生错误: {}", e.getMessage());
            return "1.0";
        }
    }
    
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    private SOPImportResult.ValidationError createValidationError(Integer rowNumber, String field, String message, String type) {
        return SOPImportResult.ValidationError.builder()
            .rowNumber(rowNumber)
            .field(field)
            .message(message)
            .type(type)
            .build();
    }
    
    // 其他方法的实现将在下一部分添加...
    
    @Override
    @Transactional
    public SOPImportResult importSOP(SOPImportRequest request) {
        try {
            log.info("开始执行SOP扁平化导入，操作人: {}", request.getOperatorName());
            
            // 1. 解析Excel文件
            SOPFlatExcelData sopFlatData = parseFlatExcelFile(request.getFile());
            
            // 2. 校验数据
            SOPImportResult validationResult = validateFlatSOPData(sopFlatData);
            if (!validationResult.getSuccess()) {
                log.warn("SOP导入校验失败，错误数: {}", validationResult.getValidationErrors().size());
                return validationResult;
            }
            
            // 3. 按场景分组数据
            Map<String, List<SOPFlatExcelData.SOPRowData>> scenarioGroups = sopFlatData.getRows().stream()
                .filter(row -> row.getScenarioCode() != null && !row.getScenarioCode().trim().isEmpty())
                .collect(Collectors.groupingBy(SOPFlatExcelData.SOPRowData::getScenarioCode));
            
            // 4. 执行导入
            List<SOPImportResult.ImportedScenario> importedScenarios = new ArrayList<>();
            int successScenarios = 0;
            int failedScenarios = 0;
            int totalSteps = 0;
            
            for (Map.Entry<String, List<SOPFlatExcelData.SOPRowData>> entry : scenarioGroups.entrySet()) {
                String scenarioCode = entry.getKey();
                List<SOPFlatExcelData.SOPRowData> scenarioSteps = entry.getValue();
                
                try {
                    // 导入单个场景
                    SOPImportResult.ImportedScenario importedScenario = importSingleScenario(
                        scenarioCode, scenarioSteps, request);
                    importedScenarios.add(importedScenario);
                    successScenarios++;
                    totalSteps += scenarioSteps.size();
                    
                    log.info("场景 {} 导入成功，步骤数: {}", scenarioCode, scenarioSteps.size());
                    
                } catch (Exception e) {
                    log.error("场景 {} 导入失败: {}", scenarioCode, e.getMessage(), e);
                    failedScenarios++;
                }
            }
            
            // 5. 构建导入结果
            SOPImportResult.ImportStatistics statistics = SOPImportResult.ImportStatistics.builder()
                .totalScenarios(scenarioGroups.size())
                .successScenarios(successScenarios)
                .failedScenarios(failedScenarios)
                .totalSteps(totalSteps)
                .build();
            
            String message = String.format("导入完成！成功: %d个场景，失败: %d个场景，总步骤数: %d", 
                successScenarios, failedScenarios, totalSteps);
            
            log.info("SOP扁平化导入完成，{}", message);
            
            return SOPImportResult.builder()
                .success(failedScenarios == 0)
                .message(message)
                .statistics(statistics)
                .importedScenarios(importedScenarios)
                .build();
                
        } catch (Exception e) {
            log.error("SOP扁平化导入失败: {}", e.getMessage(), e);
            return SOPImportResult.builder()
                .success(false)
                .message("导入失败: " + e.getMessage())
                .build();
        }
    }
    
    /**
     * 导入单个场景
     */
    private SOPImportResult.ImportedScenario importSingleScenario(
            String scenarioCode, 
            List<SOPFlatExcelData.SOPRowData> scenarioSteps, 
            SOPImportRequest request) {
        
        // 获取第一行数据作为场景信息
        SOPFlatExcelData.SOPRowData firstRow = scenarioSteps.get(0);
        
        // 1. 检查场景是否已存在（按行业ID+模块编码+场景编码+版本查找）
        SOPIndustryEntity industry = getIndustryByName(firstRow.getIndustryName());
        Long industryId = industry != null ? industry.getId() : null;
        
        // 根据导入模式决定查找的版本
        String targetVersion = "CREATE_NEW".equals(request.getImportMode()) ? 
            generateNewVersionForScenario(industryId, firstRow.getModuleCode(), scenarioCode, request.getVersionStrategy()) : "1.0";
        
        SOPScenarioEntity existingScenario = sopScenarioService.getOne(
            QueryWrapper.create()
                .eq("industry_id", industryId)
                .eq("module_code", firstRow.getModuleCode())
                .eq("scenario_code", scenarioCode)
                .eq("version", targetVersion));
        
        SOPScenarioEntity scenario;
        String action;
        
        if (existingScenario != null) {
            // 根据导入模式处理现有场景
            if ("CREATE_NEW".equals(request.getImportMode())) {
                // 创建新版本
                scenario = createNewVersionScenario(existingScenario, firstRow, request);
                action = "NEW_VERSION";
            } else if ("UPDATE_EXISTING".equals(request.getImportMode()) || request.getForceOverride()) {
                // 更新现有版本
                scenario = updateExistingScenario(existingScenario, firstRow, request);
                action = "UPDATED";
                
                // 删除现有步骤（重新导入）
                sopStepService.remove(QueryWrapper.create().eq("sop_id", scenario.getId()));
            } else {
                // 合并模式
                scenario = mergeScenario(existingScenario, firstRow, request);
                action = "MERGED";
                
                // 删除现有步骤（重新导入）
                sopStepService.remove(QueryWrapper.create().eq("sop_id", scenario.getId()));
            }
            
        } else {
            // 创建新场景
            scenario = createNewScenario(scenarioCode, firstRow, request);
            action = "CREATED";
        }
        
        // 2. 导入步骤
        List<SOPStepEntity> steps = new ArrayList<>();
        for (int i = 0; i < scenarioSteps.size(); i++) {
            SOPFlatExcelData.SOPRowData rowData = scenarioSteps.get(i);
            
            SOPStepEntity step = new SOPStepEntity();
            step.setSopId(scenario.getId());
            
            // 生成步骤编号：如果有stepOrder就使用，否则生成1.1, 1.2格式
            String stepOrderStr = rowData.getStepOrder();
            if (stepOrderStr == null || stepOrderStr.trim().isEmpty()) {
                stepOrderStr = "1." + (i + 1);
            }
            step.setStepCode(stepOrderStr);
            step.setStepName(rowData.getStep());
            step.setStepDescription(rowData.getStep()); // 使用步骤名称作为描述
            step.setStepOrder(i + 1); // 数据库中的排序字段
            step.setEntityTouchpoint(rowData.getEntityTouchpoint());
            step.setUserActivity(rowData.getUserActivity());
            step.setEmployeeBehavior(rowData.getEmployeeBehavior());
            step.setWorkHighlight(rowData.getWorkHighlight());
            step.setEmployeeRole(rowData.getEmployeeRole());
            
            // 处理相关附件字段，确保为有效的JSON格式
            step.setRelatedAttachments(convertToJsonField(rowData.getRelatedAttachments()));
            step.setStatus(1);
            step.setCreateTime(new Date());
            step.setUpdateTime(new Date());
            
            // 设置场景相关信息
            step.setScenarioCode(scenarioCode);
            step.setScenarioName(scenario.getScenarioName());
            step.setModuleCode(scenario.getModuleCode());
            step.setModuleName(scenario.getModuleName());
            step.setStage(scenario.getStage());
            step.setExecutionCycle(scenario.getExecutionCycle());
            
            steps.add(step);
        }
        
        // 批量保存步骤
        sopStepService.saveBatch(steps);
        
        // 更新场景的总步骤数
        scenario.setTotalSteps(steps.size());
        sopScenarioService.updateById(scenario);
        
        log.info("场景 {} 步骤保存完成，步骤数: {}", scenarioCode, steps.size());
        
        return SOPImportResult.ImportedScenario.builder()
            .scenarioCode(scenarioCode)
            .scenarioName(scenario.getScenarioName())
            .version(scenario.getVersion())
            .action(action)
            .stepCount(steps.size())
            .build();
    }
    
    @Override
    public SOPImportResult checkVersionConflicts(MultipartFile file) {
        // TODO: 实现版本冲突检查
        return SOPImportResult.builder()
            .success(true)
            .message("版本冲突检查功能开发中...")
            .build();
    }
    
    @Override
    public SOPImportResult restoreBackup(String backupId) {
        // TODO: 实现备份恢复
        return SOPImportResult.builder()
            .success(false)
            .message("备份恢复功能开发中...")
            .build();
    }
    
    /**
     * 创建新版本场景
     */
    private SOPScenarioEntity createNewVersionScenario(SOPScenarioEntity existingScenario, 
                                                       SOPFlatExcelData.SOPRowData firstRow, 
                                                       SOPImportRequest request) {
        SOPScenarioEntity newScenario = new SOPScenarioEntity();
        
        // 设置行业信息
        SOPIndustryEntity industry = getIndustryByName(firstRow.getIndustryName());
        if (industry != null) {
            newScenario.setIndustryId(industry.getId());
            newScenario.setIndustryName(industry.getIndustryName());
        }
        
        newScenario.setScenarioCode(existingScenario.getScenarioCode());
        newScenario.setScenarioName(firstRow.getScenarioName());
        newScenario.setStage(firstRow.getProjectStage());
        newScenario.setModuleCode(firstRow.getModuleCode());
        newScenario.setModuleName(firstRow.getModuleName());
        newScenario.setExecutionCycle(firstRow.getExecutionCycle());
        newScenario.setVersion(generateVersion(existingScenario.getVersion(), request.getVersionStrategy()));
        newScenario.setStatus(1);
        newScenario.setCreateTime(new Date());
        newScenario.setUpdateTime(new Date());
        sopScenarioService.save(newScenario);
        return newScenario;
    }
    
    /**
     * 更新现有场景
     */
    private SOPScenarioEntity updateExistingScenario(SOPScenarioEntity existingScenario, 
                                                     SOPFlatExcelData.SOPRowData firstRow, 
                                                     SOPImportRequest request) {
        // 更新行业信息
        SOPIndustryEntity industry = getIndustryByName(firstRow.getIndustryName());
        if (industry != null) {
            existingScenario.setIndustryId(industry.getId());
            existingScenario.setIndustryName(industry.getIndustryName());
        }
        
        existingScenario.setScenarioName(firstRow.getScenarioName());
        existingScenario.setStage(firstRow.getProjectStage());
        existingScenario.setModuleCode(firstRow.getModuleCode());
        existingScenario.setModuleName(firstRow.getModuleName());
        existingScenario.setExecutionCycle(firstRow.getExecutionCycle());
        existingScenario.setUpdateTime(new Date());
        sopScenarioService.updateById(existingScenario);
        return existingScenario;
    }
    
    /**
     * 合并场景
     */
    private SOPScenarioEntity mergeScenario(SOPScenarioEntity existingScenario, 
                                            SOPFlatExcelData.SOPRowData firstRow, 
                                            SOPImportRequest request) {
        // 合并模式：只更新非空字段
        if (firstRow.getIndustryName() != null && !firstRow.getIndustryName().trim().isEmpty()) {
            SOPIndustryEntity industry = getIndustryByName(firstRow.getIndustryName());
            if (industry != null) {
                existingScenario.setIndustryId(industry.getId());
                existingScenario.setIndustryName(industry.getIndustryName());
            }
        }
        
        if (firstRow.getScenarioName() != null && !firstRow.getScenarioName().trim().isEmpty()) {
            existingScenario.setScenarioName(firstRow.getScenarioName());
        }
        if (firstRow.getProjectStage() != null && !firstRow.getProjectStage().trim().isEmpty()) {
            existingScenario.setStage(firstRow.getProjectStage());
        }
        if (firstRow.getModuleCode() != null && !firstRow.getModuleCode().trim().isEmpty()) {
            existingScenario.setModuleCode(firstRow.getModuleCode());
        }
        if (firstRow.getModuleName() != null && !firstRow.getModuleName().trim().isEmpty()) {
            existingScenario.setModuleName(firstRow.getModuleName());
        }
        if (firstRow.getExecutionCycle() != null && !firstRow.getExecutionCycle().trim().isEmpty()) {
            existingScenario.setExecutionCycle(firstRow.getExecutionCycle());
        }
        existingScenario.setUpdateTime(new Date());
        sopScenarioService.updateById(existingScenario);
        return existingScenario;
    }
    
    /**
     * 创建新场景
     */
    private SOPScenarioEntity createNewScenario(String scenarioCode, 
                                                SOPFlatExcelData.SOPRowData firstRow, 
                                                SOPImportRequest request) {
        SOPScenarioEntity scenario = new SOPScenarioEntity();
        
        // 设置行业信息
        SOPIndustryEntity industry = getIndustryByName(firstRow.getIndustryName());
        if (industry != null) {
            scenario.setIndustryId(industry.getId());
            scenario.setIndustryName(industry.getIndustryName());
        }
        
        scenario.setScenarioCode(scenarioCode);
        scenario.setScenarioName(firstRow.getScenarioName());
        scenario.setStage(firstRow.getProjectStage());
        scenario.setModuleCode(firstRow.getModuleCode());
        scenario.setModuleName(firstRow.getModuleName());
        scenario.setExecutionCycle(firstRow.getExecutionCycle());
        scenario.setVersion("1.0");
        scenario.setStatus(1);
        scenario.setCreateTime(new Date());
        scenario.setUpdateTime(new Date());
        sopScenarioService.save(scenario);
        return scenario;
    }

    /**
     * 将字符串转换为有效的JSON字段
     */
    private String convertToJsonField(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        value = value.trim();
        
        // 如果已经是JSON格式，直接返回
        if (value.startsWith("[") || value.startsWith("{")) {
            try {
                // 简单验证JSON格式
                if (value.startsWith("[")) {
                    // JSON数组格式验证
                    return value;
                } else if (value.startsWith("{")) {
                    // JSON对象格式验证
                    return value;
                }
            } catch (Exception e) {
                log.warn("JSON格式验证失败，转换为字符串数组: {}", value);
            }
        }
        
        // 不是JSON格式，转换为JSON数组
        return "[\"" + value.replace("\"", "\\\"") + "\"]";
    }

    @Override
    public String generateVersion(String currentVersion, String strategy) {
        if (currentVersion == null || currentVersion.trim().isEmpty()) {
            return "1.0";
        }
        
        String[] parts = currentVersion.split("\\.");
        int major = Integer.parseInt(parts.length > 0 ? parts[0] : "1");
        int minor = Integer.parseInt(parts.length > 1 ? parts[1] : "0");
        int patch = Integer.parseInt(parts.length > 2 ? parts[2] : "0");
        
        switch (strategy) {
            case "MAJOR":
                return (major + 1) + ".0";
            case "MINOR":
                return major + "." + (minor + 1);
            case "PATCH":
                return major + "." + minor + "." + (patch + 1);
            default:
                return major + "." + (minor + 1);
        }
    }
}

package com.cool.modules.task.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务分配结果DTO
 */
@Data
public class AssignmentResult {

    /**
     * 分配是否成功
     */
    private Boolean success = false;

    /**
     * 成功分配的任务列表
     */
    private List<TaskAssignment> assignments;

    /**
     * 分配失败的任务列表
     */
    private List<FailedTask> failedTasks;

    /**
     * 分配建议
     */
    private List<String> suggestions;

    /**
     * 分配总结
     */
    private AssignmentSummary summary;

    /**
     * 单个任务分配结果
     */
    @Data
    public static class TaskAssignment {
        /**
         * 任务ID
         */
        private String taskId;

        /**
         * 任务名称
         */
        private String taskName;

        /**
         * 员工角色
         */ 
        private String employeeRole;

        /**
         * 分配的执行人列表
         */
        private List<Assignee> assignees;

        /**
         * 分配理由
         */
        private String reason;

        /**
         * 是否AI生成
         */
        private Boolean aiGenerated;

        /**
         * 分配时间
         */
        private LocalDateTime assignmentTime;

        /**
         * 分配置信度 (0-100)
         */
        private Integer confidence;
    }

    /**
     * 执行人信息
     */
    @Data
    public static class Assignee {
        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 用户名称
         */
        private String userName;

        /**
         * 用户角色
         */
        private String userRole;

        /**
         * 用户角色标签
         */
        private String userRoleLabel;

        /**
         * 在任务中的角色：primary-主要负责人，assistant-协助人员
         */
        private String taskRole;

        /**
         * 分配置信度 (0-100)
         */
        private Integer confidence;

        /**
         * 当前工作负载百分比
         */
        private Integer currentWorkload;

        /**
         * 技能匹配度 (0-100)
         */
        private Integer skillMatch;
    }

    /**
     * 分配失败的任务
     */
    @Data
    public static class FailedTask {
        /**
         * 任务ID
         */
        private Long taskId;

        /**
         * 任务名称
         */
        private String taskName;

        /**
         * 失败原因
         */
        private String failureReason;

        /**
         * 失败代码
         */
        private String failureCode;

        /**
         * 建议操作
         */
        private List<String> suggestedActions;
    }

    /**
     * 分配总结
     */
    @Data
    public static class AssignmentSummary {
        /**
         * 总任务数
         */
        private Integer totalTasks;

        /**
         * 成功分配数
         */
        private Integer successfulAssignments;

        /**
         * 失败分配数
         */
        private Integer failedAssignments;

        /**
         * 平均置信度
         */
        private Double averageConfidence;

        /**
         * 分配耗时（毫秒）
         */
        private Long processingTime;

        /**
         * 使用的分配策略
         */
        private String strategy;
    }
}

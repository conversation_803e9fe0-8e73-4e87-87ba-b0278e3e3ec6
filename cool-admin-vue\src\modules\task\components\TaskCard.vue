<template>
  <div class="task-card" :class="cardClasses" @click="handleCardClick">
    <!-- 卡片头部 -->
    <div class="task-card-header">
      <div class="header-left">
        <!-- 部门标签 -->
        <department-tag
          :department-id="task.departmentId"
          :department-name="task.departmentName"
          size="small"
          :clickable="true"
          @click="handleDepartmentClick"
        />
        
        <!-- 任务标题 -->
        <h3 class="task-title">
          {{ task.taskName || task.packageName }}
        </h3>
      </div>
      
      <div class="header-right">
        <!-- 权限状态指示器 -->
        <permission-status-indicator
          :task="task"
          :task-type="taskType"
          size="small"
        />
        
        <!-- 更多操作 -->
        <el-dropdown trigger="click" @command="handleCommand" @click.stop>
          <el-button type="text" size="small" class="more-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item 
                command="view" 
                :icon="View"
                :disabled="!canView"
              >
                查看详情
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canEdit" 
                command="edit" 
                :icon="Edit"
              >
                编辑
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canAssign" 
                command="assign" 
                :icon="UserFilled"
              >
                分配
              </el-dropdown-item>
              <el-dropdown-item 
                v-if="canDelete" 
                command="delete" 
                :icon="Delete"
                divided
              >
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="task-card-content">
      <!-- 任务描述 -->
      <div v-if="task.description || task.taskDescription" class="task-description">
        <el-icon><Document /></el-icon>
        <span>{{ truncateText(task.description || task.taskDescription, 100) }}</span>
      </div>
      
      <!-- 关键信息 -->
      <div class="task-key-info">
        <div v-if="task.deadline" class="info-item deadline">
          <el-icon><Clock /></el-icon>
          <span class="label">截止时间：</span>
          <span class="value" :class="{ 'urgent': isUrgent(task.deadline) }">
            {{ formatDate(task.deadline) }}
          </span>
        </div>
        
        <div v-if="task.assigneeName || task.ownerName" class="info-item assignee">
          <el-icon><User /></el-icon>
          <span class="label">执行人：</span>
          <span class="value">{{ task.assigneeName || task.ownerName || '未分配' }}</span>
          <el-tag 
            v-if="task.assigneeDepartmentName" 
            type="info" 
            size="small"
            class="assignee-dept"
          >
            {{ task.assigneeDepartmentName }}
          </el-tag>
        </div>
        
        <div v-if="task.progress !== undefined" class="info-item progress">
          <el-icon><TrendCharts /></el-icon>
          <span class="label">进度：</span>
          <el-progress
            :percentage="task.progress"
            :color="getProgressColor(task.progress)"
            :stroke-width="6"
            :show-text="false"
            class="progress-bar"
          />
          <span class="progress-text">{{ task.progress }}%</span>
        </div>
        
        <div v-if="task.priority" class="info-item priority">
          <el-icon><Flag /></el-icon>
          <span class="label">优先级：</span>
          <priority-tag :priority="task.priority" />
        </div>
        
        <div v-if="task.packageStatus !== undefined || task.status !== undefined" class="info-item status">
          <el-icon><InfoFilled /></el-icon>
          <span class="label">状态：</span>
          <task-status-tag :status="task.packageStatus || task.status" />
        </div>
      </div>
    </div>
    
    <!-- 卡片底部 -->
    <div class="task-card-footer">
      <div class="footer-left">
        <!-- 创建者信息 -->
        <div class="creator-info">
          <span class="creator-text">
            创建者：{{ task.creatorName || task.createUser }}
            <span v-if="task.creatorDepartmentName" class="creator-dept">
              ({{ task.creatorDepartmentName }})
            </span>
          </span>
          <span class="create-time">{{ formatDate(task.createTime) }}</span>
        </div>
      </div>
      
      <div class="footer-right">
        <!-- 协作标识 -->
        <el-tag
          v-if="task.isCrossDepartment"
          type="warning"
          size="small"
          class="collaboration-tag"
        >
          <el-icon><Link /></el-icon>
          跨部门协作
        </el-tag>
        
        <!-- 快捷操作按钮 -->
        <div class="quick-actions">
          <el-button
            v-if="canView"
            type="text"
            size="small"
            @click.stop="viewTaskDetail"
          >
            <el-icon><View /></el-icon>
            详情
          </el-button>
          
          <el-button
            v-if="canEdit"
            type="text"
            size="small"
            @click.stop="editTask"
          >
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          
          <el-button
            v-if="canAssign"
            type="text"
            size="small"
            @click.stop="assignTask"
          >
            <el-icon><UserFilled /></el-icon>
            分配
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { usePermission } from '../hooks/usePermission'
import DepartmentTag from './DepartmentTag.vue'
import PermissionStatusIndicator from './PermissionStatusIndicator.vue'
import PriorityTag from './PriorityTag.vue'
import TaskStatusTag from './task-status-tag.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  taskType: {
    type: String,
    default: 'package' // package | info | execution
  },
  clickable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view', 'edit', 'assign', 'delete', 'click', 'department-click'])

const { canOperateTask, hasTaskPermission } = usePermission()

// 权限计算
const canView = ref(true)
const canEdit = ref(false)
const canAssign = ref(false)
const canDelete = ref(false)

const cardClasses = computed(() => {
  return {
    'cross-department': props.task.isCrossDepartment,
    'urgent': isUrgent(props.task.deadline),
    'completed': isCompleted(props.task),
    'clickable': props.clickable
  }
})

const isCompleted = (task) => {
  return task.status === 2 || task.packageStatus === 2 || task.progress === 100
}

const isUrgent = (deadline) => {
  if (!deadline) return false
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diffDays = (deadlineDate - now) / (1000 * 60 * 60 * 24)
  return diffDays <= 3 && diffDays >= 0
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

const getProgressColor = (progress) => {
  if (progress < 30) return '#f56c6c'
  if (progress < 70) return '#e6a23c'
  return '#67c23a'
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const handleCardClick = () => {
  if (props.clickable) {
    emit('click', props.task)
  }
}

const handleDepartmentClick = (departmentInfo) => {
  emit('department-click', departmentInfo)
}

const handleCommand = (command) => {
  switch (command) {
    case 'view':
      viewTaskDetail()
      break
    case 'edit':
      editTask()
      break
    case 'assign':
      assignTask()
      break
    case 'delete':
      deleteTask()
      break
  }
}

const viewTaskDetail = () => {
  emit('view', props.task)
}

const editTask = () => {
  emit('edit', props.task)
}

const assignTask = () => {
  emit('assign', props.task)
}

const deleteTask = () => {
  emit('delete', props.task)
}

// 初始化权限检查
onMounted(async () => {
  try {
    canView.value = await hasTaskPermission(props.taskType, props.task.id, 'VIEW')
    canEdit.value = await hasTaskPermission(props.taskType, props.task.id, 'EDIT')
    canAssign.value = await hasTaskPermission(props.taskType, props.task.id, 'ASSIGN')
    canDelete.value = await hasTaskPermission(props.taskType, props.task.id, 'DELETE')
  } catch (error) {
    console.error('权限检查失败:', error)
  }
})
</script>

<style scoped>
.task-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  transition: all 0.3s ease;
  position: relative;
}

.task-card.clickable {
  cursor: pointer;
}

.task-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.task-card.cross-department {
  border-left: 4px solid #e6a23c;
}

.task-card.urgent {
  border-left: 4px solid #f56c6c;
}

.task-card.completed {
  background: #f0f9ff;
  border-color: #67c23a;
}

.task-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.header-left {
  flex: 1;
  min-width: 0;
}

.task-title {
  margin: 8px 0 0 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  transition: color 0.3s;
  line-height: 1.4;
}

.task-card.clickable .task-title:hover {
  color: #409eff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.more-btn {
  padding: 4px;
  color: #666;
}

.task-card-content {
  margin-bottom: 12px;
}

.task-description {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.task-key-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.info-item .label {
  color: #666;
  min-width: 60px;
  flex-shrink: 0;
}

.info-item .value {
  color: #333;
  font-weight: 500;
}

.info-item.deadline .value.urgent {
  color: #f56c6c;
}

.info-item.progress {
  align-items: center;
}

.info-item.progress .progress-bar {
  flex: 1;
  margin: 0 8px;
  max-width: 100px;
}

.progress-text {
  min-width: 35px;
  text-align: right;
  font-weight: 500;
  font-size: 12px;
}

.assignee-dept {
  margin-left: 4px;
}

.task-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.creator-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.creator-text {
  color: #666;
}

.creator-dept {
  color: #409eff;
}

.create-time {
  color: #999;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collaboration-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.quick-actions {
  display: flex;
  gap: 4px;
}

.quick-actions .el-button {
  padding: 4px 6px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-card {
    padding: 12px;
  }
  
  .task-card-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .header-right {
    align-self: flex-end;
  }
  
  .task-card-footer {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .footer-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .info-item.progress .progress-bar {
    max-width: 60px;
  }
}
</style> 
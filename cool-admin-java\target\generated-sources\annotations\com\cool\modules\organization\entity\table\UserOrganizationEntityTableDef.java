package com.cool.modules.organization.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class UserOrganizationEntityTableDef extends TableDef {

    /**
     * 用户组织关系实体
 
 <AUTHOR> Admin
 @since 2025-01-17
     */
    public static final UserOrganizationEntityTableDef USER_ORGANIZATION_ENTITY = new UserOrganizationEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 备注
     */
    public final QueryColumn REMARK = new QueryColumn(this, "remark");

    /**
     * 状态
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 加入时间
     */
    public final QueryColumn JOIN_TIME = new QueryColumn(this, "join_time");

    /**
     * 排序号
     */
    public final QueryColumn ORDER_NUM = new QueryColumn(this, "order_num");

    /**
     * 角色代码
     */
    public final QueryColumn ROLE_CODE = new QueryColumn(this, "role_code");

    /**
     * 是否为主要角色
     */
    public final QueryColumn IS_PRIMARY = new QueryColumn(this, "is_primary");

    /**
     * 分配时间
     */
    public final QueryColumn ASSIGN_TIME = new QueryColumn(this, "assign_time");

    /**
     * 分配人ID
     */
    public final QueryColumn ASSIGNER_ID = new QueryColumn(this, "assigner_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 过期时间
     */
    public final QueryColumn EXPIRE_TIME = new QueryColumn(this, "expire_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 组织ID
     */
    public final QueryColumn ORGANIZATION_ID = new QueryColumn(this, "organization_id");

    /**
     * 权限范围（JSON格式，用于存储特殊权限配置）
     */
    public final QueryColumn PERMISSION_SCOPE = new QueryColumn(this, "permission_scope");

    /**
     * 组织类型
     */
    public final QueryColumn ORGANIZATION_TYPE = new QueryColumn(this, "organization_type");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, REMARK, STATUS, USER_ID, JOIN_TIME, ORDER_NUM, ROLE_CODE, IS_PRIMARY, ASSIGN_TIME, ASSIGNER_ID, CREATE_TIME, EXPIRE_TIME, UPDATE_TIME, ORGANIZATION_ID, PERMISSION_SCOPE, ORGANIZATION_TYPE};

    public UserOrganizationEntityTableDef() {
        super("", "org_user_organization");
    }

    private UserOrganizationEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public UserOrganizationEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new UserOrganizationEntityTableDef("", "org_user_organization", alias));
    }

}

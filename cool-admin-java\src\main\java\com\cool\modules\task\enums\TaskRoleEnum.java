package com.cool.modules.task.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务角色枚举
 */
@Getter
@AllArgsConstructor
public enum TaskRoleEnum {
    
    /**
     * 主要负责人
     */
    PRIMARY("primary", "主要负责人"),
    
    /**
     * 协助人员
     */
    ASSISTANT("assistant", "协助人员"),
    
    /**
     * 审核人员
     */
    REVIEWER("reviewer", "审核人员"),
    
    /**
     * 观察者
     */
    OBSERVER("observer", "观察者");

    private final String code;
    private final String name;

    /**
     * 根据代码获取枚举
     */
    public static TaskRoleEnum getByCode(String code) {
        if (code == null || code.isEmpty()) return null;
        for (TaskRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }

    /**
     * 根据代码获取名称
     */
    public static String getNameByCode(String code) {
        TaskRoleEnum role = getByCode(code);
        return role != null ? role.getName() : "未知";
    }
    
    /**
     * 检查是否为有效的任务角色
     */
    public static boolean isValidRole(String code) {
        return getByCode(code) != null;
    }
} 
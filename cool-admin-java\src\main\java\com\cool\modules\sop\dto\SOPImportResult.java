package com.cool.modules.sop.dto;

import lombok.Data;
import lombok.Builder;
import java.util.List;
import java.util.Map;

/**
 * SOP导入结果DTO
 */
@Data
@Builder
public class SOPImportResult {
    
    /**
     * 导入是否成功
     */
    private Boolean success;
    
    /**
     * 导入消息
     */
    private String message;
    
    /**
     * 导入统计信息
     */
    private ImportStatistics statistics;
    
    /**
     * 版本冲突信息
     */
    private List<VersionConflict> versionConflicts;
    
    /**
     * 校验错误信息
     */
    private List<ValidationError> validationErrors;
    
    /**
     * 导入的场景列表
     */
    private List<ImportedScenario> importedScenarios;
    
    /**
     * 需要用户确认的操作
     */
    private List<UserConfirmation> confirmations;

    /**
     * 预览数据（用于显示导入内容明细）
     */
    private Object data;
    
    @Data
    @Builder
    public static class ImportStatistics {
        private Integer totalScenarios;
        private Integer totalSteps;
        private Integer successScenarios;
        private Integer failedScenarios;
        private Integer updatedScenarios;
        private Integer newScenarios;
        private Integer skippedScenarios;
    }
    
    @Data
    @Builder
    public static class VersionConflict {
        private String scenarioCode;
        private String scenarioName;
        private String existingVersion;
        private String importVersion;
        private String conflictType; // VERSION_LOWER, VERSION_SAME, CODE_EXISTS
        private String recommendation; // UPGRADE, SKIP, MERGE
    }
    
    @Data
    @Builder
    public static class ValidationError {
        private String type; // MISSING_FIELD, INVALID_FORMAT, DUPLICATE_CODE
        private String field;
        private String value;
        private String message;
        private Integer rowNumber;
        private String scenarioCode;
    }
    
    @Data
    @Builder
    public static class ImportedScenario {
        private Long scenarioId;
        private String scenarioCode;
        private String scenarioName;
        private String version;
        private String action; // CREATED, UPDATED, SKIPPED
        private Integer stepCount;
    }
    
    @Data
    @Builder
    public static class UserConfirmation {
        private String type; // VERSION_UPGRADE, OVERWRITE_EXISTING
        private String scenarioCode;
        private String scenarioName;
        private String currentVersion;
        private String newVersion;
        private String message;
        private Map<String, Object> options;
    }
}

package com.cool.modules.task.controller.admin;

import com.cool.core.request.R;
import com.cool.modules.task.dto.TaskCompletionRequest;
import com.cool.modules.task.dto.TaskCloseRequest;
import com.cool.modules.task.dto.TaskForceCompleteRequest;
import com.cool.modules.task.dto.TaskReopenRequest;
import com.cool.modules.task.dto.BatchTaskOperationRequest;
import com.cool.modules.task.service.TaskStatusService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * 任务状态管理控制器
 */
@Tag(name = "任务状态管理", description = "任务完成、关闭等状态管理")
@RestController
@RequestMapping("/admin/task/status")
public class AdminTaskStatusController {

    private final TaskStatusService taskStatusService;

    public AdminTaskStatusController(TaskStatusService taskStatusService) {
        this.taskStatusService = taskStatusService;
    }

    /**
     * 完成任务执行
     */
    @Operation(summary = "完成任务执行")
    @PostMapping("/task/execution/complete")
    public R completeTaskExecution(@RequestBody TaskCompletionRequest request) {
        try {
            Boolean result = taskStatusService.completeTaskExecution(request);
            if (result) {
                return R.ok("任务完成成功");
            } else {
                return R.error("任务完成失败，请检查权限或任务状态");
            }
        } catch (Exception e) {
            return R.error("任务完成失败: " + e.getMessage());
        }
    }

    /**
     * 强制完成任务
     */
    @Operation(summary = "强制完成任务")
    @PostMapping("/task/force-complete")
    public R forceCompleteTask(@RequestBody TaskForceCompleteRequest request) {
        try {
            Boolean result = taskStatusService.forceCompleteTask(request.getTaskId(), request.getReason(), request.getOperatorId());
            if (result) {
                return R.ok("强制完成任务成功");
            } else {
                return R.error("强制完成任务失败");
            }
        } catch (Exception e) {
            return R.error("强制完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 关闭任务
     */
    @Operation(summary = "关闭任务")
    @PostMapping("/task/close")
    public R closeTask(@RequestBody TaskCloseRequest request) {
        try {
            Boolean result = taskStatusService.closeTask(request);
            if (result) {
                return R.ok("关闭任务成功");
            } else {
                return R.error("关闭任务失败，请检查权限或任务状态");
            }
        } catch (Exception e) {
            return R.error("关闭任务失败: " + e.getMessage());
        }
    }

    /**
     * 重新开启任务
     */
    @Operation(summary = "重新开启任务")
    @PostMapping("/task/reopen")
    public R reopenTask(@RequestBody TaskReopenRequest request) {
        try {
            Boolean result = taskStatusService.reopenTask(request.getTaskId(), request.getReason(), request.getOperatorId());
            if (result) {
                return R.ok("重新开启任务成功");
            } else {
                return R.error("重新开启任务失败");
            }
        } catch (Exception e) {
            return R.error("重新开启任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查任务是否可以完成
     */
    @Operation(summary = "检查任务是否可以完成")
    @GetMapping("/task/canComplete")
    public R canCompleteTask(@RequestParam Long taskId, @RequestParam Long assigneeId) {
        Boolean canComplete = taskStatusService.canCompleteTask(taskId, assigneeId);
        return R.ok(canComplete);
    }

    /**
     * 检查任务是否可以关闭
     */
    @Operation(summary = "检查任务是否可以关闭")
    @GetMapping("/task/canClose")
    public R canCloseTask(@RequestParam Long taskId, @RequestParam Long operatorId) {
        Boolean canClose = taskStatusService.canCloseTask(taskId, operatorId);
        return R.ok(canClose);
    }

    /**
     * 批量强制完成任务
     */
    @Operation(summary = "批量强制完成任务")
    @PostMapping("/task/batch/force-complete")
    public R batchForceCompleteTask(@RequestBody BatchTaskOperationRequest request) {
        try {
            Integer successCount = taskStatusService.batchForceCompleteTask(request.getTaskIds(), request.getReason(), request.getOperatorId());
            return R.ok("批量强制完成任务成功，成功处理 " + successCount + " 个任务");
        } catch (Exception e) {
            return R.error("批量强制完成任务失败: " + e.getMessage());
        }
    }

    /**
     * 批量关闭任务
     */
    @Operation(summary = "批量关闭任务")
    @PostMapping("/task/batch/close")
    public R batchCloseTask(@RequestBody BatchTaskOperationRequest request) {
        try {
            Integer successCount = taskStatusService.batchCloseTask(request.getTaskIds(), request.getReason(), request.getOperatorId());
            return R.ok("批量关闭任务成功，成功处理 " + successCount + " 个任务");
        } catch (Exception e) {
            return R.error("批量关闭任务失败: " + e.getMessage());
        }
    }

    /**
     * 批量重新开启任务
     */
    @Operation(summary = "批量重新开启任务")
    @PostMapping("/task/batch/reopen")
    public R batchReopenTask(@RequestBody BatchTaskOperationRequest request) {
        try {
            Integer successCount = taskStatusService.batchReopenTask(request.getTaskIds(), request.getReason(), request.getOperatorId());
            return R.ok("批量重新开启任务成功，成功处理 " + successCount + " 个任务");
        } catch (Exception e) {
            return R.error("批量重新开启任务失败: " + e.getMessage());
        }
    }

}

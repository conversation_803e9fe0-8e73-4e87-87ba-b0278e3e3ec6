package com.cool.modules.task.service.impl;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.task.entity.TaskPermissionLogEntity;
import com.cool.modules.task.mapper.TaskPermissionLogMapper;
import com.cool.modules.task.service.TaskPermissionLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务权限日志服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskPermissionLogServiceImpl extends BaseServiceImpl<TaskPermissionLogMapper, TaskPermissionLogEntity> 
        implements TaskPermissionLogService {

    @Override
    public void logPermissionCheck(Long userId, String username, String operationType, 
                                  String taskType, Long taskId, Long departmentId, 
                                  boolean hasPermission, String clientIp, String userAgent) {
        try {
            TaskPermissionLogEntity logEntity = new TaskPermissionLogEntity();
            logEntity.setUserId(userId);
            logEntity.setUsername(username);
            logEntity.setOperationType(operationType);
            logEntity.setTaskType(taskType);
            logEntity.setTaskId(taskId);
            logEntity.setDepartmentId(departmentId);
            logEntity.setPermissionResult(hasPermission ? 1 : 0);
            logEntity.setOperationTime(LocalDateTime.now());
            logEntity.setClientIp(clientIp);
            logEntity.setUserAgent(userAgent);
            
            // 异步保存日志，避免影响主业务流程
            save(logEntity);
            
        } catch (Exception e) {
            log.error("记录权限检查日志失败: userId={}, operationType={}, taskType={}, taskId={}", 
                     userId, operationType, taskType, taskId, e);
        }
    }

    @Override
    public List<Map<String, Object>> getUserPermissionStats(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return mapper.getUserPermissionStats(userId, startTime, endTime);
    }

    @Override
    public Map<String, Object> getDepartmentPermissionStats(Long departmentId, LocalDateTime startTime, LocalDateTime endTime) {
        return mapper.getDepartmentPermissionStats(departmentId, startTime, endTime);
    }

    @Override
    public List<TaskPermissionLogEntity> getViolationLogs(LocalDateTime startTime, Integer limit) {
        return mapper.getViolationLogs(startTime, limit);
    }

    @Override
    public int cleanExpiredLogs(LocalDateTime expireTime) {
        return mapper.cleanExpiredLogs(expireTime);
    }
} 
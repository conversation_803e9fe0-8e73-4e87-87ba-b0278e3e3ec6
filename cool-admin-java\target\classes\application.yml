server:
  port: ${SERVER_PORT:18001}
  servlet:
    context-path: /
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/plain
  tomcat:
    # 编码
    uri-encoding: UTF-8

spring:
  application:
    name: cool-admin-java
  profiles:
    active: local
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    mode: HTML
  #返回时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  # 日志编码配置
  output:
    ansi:
      enabled: ALWAYS
  # 强制设置日志编码
  logging:
    charset:
      console: UTF-8
      file: UTF-8
    pattern:
      console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
      file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
  # Web设置
  web:
    resources:
      add-mappings: false
      static-locations: classpath:/static/,file:./assets/public/

  # caffeine 缓存
  # cache:
  #   type: caffeine
  #   file: assets/cache

  #redis 缓存
  cache:
    type: redis
  data:
    redis:
      host: **************
      port: 16379
      database: 0
      password: 5tgbNHY^
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: always
    autoStartup: true
    #相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: CoolScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            clusterCheckinInterval: 10000
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 5
            threadPriority: 9
            threadsInheritContextClassLoaderOfInitializingThread: true

# 忽略url
ignored:
  # 忽略后台鉴权url
  adminAuthUrls:
    - /
    - /upload/**
    - /actuator/**
    - /download/**
    - /static/**
    - /favicon.ico
    - /v3/api-docs/**
    - /swagger
    - /swagger-ui/**
    - /css/*
    - /js/*
    - /druid/**
    - /admin/base/open/**
  # 忽略记录请求日志url
  logUrls:
    - /
    - /**/eps
    - /app/**
    - /css/*
    - /js/*
    - /favicon.ico
# 文档
springdoc:
  api-docs:
    #swagger后端请求地址
    path: /v3/api-docs
  swagger-ui:
    #自定义swagger前端请求路径，输入http://127.0.0.1:端口号/swagger会自动重定向到swagger页面
    path: /swagger
    # 排除的接口
    exclude-paths:
      - /admin/base/**
      - /admin/plugin/**
      - /admin/recycle/**
      - /admin/dict/info/page
      - /admin/dict/info/add

mybatis-flex:
  #多数据源
  #  datasource:
  #MyBatis 配置文件位置，如果有单独的 MyBatis 配置，需要将其路径配置到 configLocation 中
  #  configuration:
  #MyBatis Mapper 所对应的 XML 文件位置，如果在 Mapper 中有自定义的方法（XML 中有自定义的实现），需要进行该配置，指定 Mapper 所对应的 XML 文件位置
  mapper-locations: [ "classpath*:/mapper/**/*.xml" ]
  type-aliases-package: com.cool.**.entity.*
  global-config:
    print-banner: false


# Cool相关配置
cool:
  # 缓存名称
  cacheName: comm
  plugin:
    # 插件安装位置
    path: assets/plugin
  # token 相关配置
  token:
    # 过期时间 单位：秒 半小时
    expire: 1800
    # 刷新token过期时间 单位：秒 7天
    refreshExpire: 604800
  # 文件上传相关
  file:
    #上传模式
    mode: local
    # 本地上传配置
    local:
      # 文件访问地址
      base-url: http://127.0.0.1:${server.port}/upload
  # 系统日志请求参数超过1024字节 就不记录,避免日志过大
  log:
    # 请求参数最大字节,超过请求参数不记录
    max-byte-length: 1024
    # 核心线程数的倍数
    core-pool-size-multiplier: 2
    # 最大线程数的倍数
    max-pool-size-multiplier: 3
    # 队列容量的倍数
    queue-capacity-multiplier: 3
  multi-tenant:
    # 是否开启多租户，默认关闭
    enable: false
  # AI配置
  ai:
    # 是否启用AI功能
    enabled: ${AI_ENABLED:true}

    # 默认AI提供商 (openai, dify)
    default-provider: ${AI_DEFAULT_PROVIDER:dify}

    # OpenAI配置
    openai:
      api-key: otNSANiYQOYQg5OK557eC8F19f1c425fA946E236469c5eFf
      base-url: http://one-api.hypersmart.ltd/v1
      model: DeepSeek-V3
      max-tokens: ${OPENAI_MAX_TOKENS:4000}
      temperature: ${OPENAI_TEMPERATURE:0.1}
      timeout: ${OPENAI_TIMEOUT:60000}
      retry-count: ${OPENAI_RETRY_COUNT:3}

    # Dify工作流配置
    dify:
      # Dify API基础地址
      base-url: ${DIFY_BASE_URL:http://localhost:5001}
      # Dify API密钥
      api-key: ${DIFY_API_KEY:your-dify-api-key}
      # 请求超时时间(毫秒)
      timeout: ${DIFY_TIMEOUT:120000}
      # 重试次数
      retry-count: ${DIFY_RETRY_COUNT:3}
      # 是否启用流式响应
      stream-enabled: ${DIFY_STREAM_ENABLED:true}
      # 工作流配置
      workflows:
        # SOP解析工作流
        sop-parse:
          app-id: ${DIFY_SOP_PARSE_APP_ID:your-sop-parse-app-id}
          workflow-id: ${DIFY_SOP_PARSE_WORKFLOW_ID:your-workflow-id}
        # 任务生成工作流
        task-generate:
          app-id: ${DIFY_TASK_GENERATE_APP_ID:your-task-generate-app-id}
          workflow-id: ${DIFY_TASK_GENERATE_WORKFLOW_ID:your-workflow-id}
        # 质量检查工作流
        quality-check:
          app-id: ${DIFY_QUALITY_CHECK_APP_ID:your-quality-check-app-id}
          workflow-id: ${DIFY_QUALITY_CHECK_WORKFLOW_ID:your-workflow-id}
        # 流程优化工作流
        process-optimize:
          app-id: ${DIFY_PROCESS_OPTIMIZE_APP_ID:your-process-optimize-app-id}
          workflow-id: ${DIFY_PROCESS_OPTIMIZE_WORKFLOW_ID:your-workflow-id}

    # 本地模型配置
    local-model:
      enabled: ${LOCAL_MODEL_ENABLED:false}
      endpoint: ${LOCAL_MODEL_ENDPOINT:http://localhost:11434}
      model: ${LOCAL_MODEL_NAME:llama2}
      max-tokens: ${LOCAL_MODEL_MAX_TOKENS:2000}
      temperature: ${LOCAL_MODEL_TEMPERATURE:0.2}
    
    # AI功能开关
    features:
      sop-generation: true
      intelligent-scheduling: true
      execution-guidance: ${AI_EXECUTION_GUIDANCE:true}
      quality-assessment: ${AI_QUALITY_ASSESSMENT:true}
      process-optimization: ${AI_PROCESS_OPTIMIZATION:true}
      predictive-analysis: ${AI_PREDICTIVE_ANALYSIS:true}
    
    # 性能配置
    performance:
      thread-pool-size: ${AI_THREAD_POOL_SIZE:10}
      queue-capacity: ${AI_QUEUE_CAPACITY:100}
      cache-enabled: ${AI_CACHE_ENABLED:true}
      cache-expire-minutes: ${AI_CACHE_EXPIRE_MINUTES:30}

# AutoTable配置，根据实体类自动生成表
auto-table:
  show-banner: false

# 分布式唯一ID组件
leaf:
  segment:
    # 默认禁用
    enable: false
package com.cool.modules.task.controller.admin;

import cn.hutool.json.JSONObject;
import com.cool.core.annotation.CoolRestController;
import com.cool.core.base.BaseController;
import com.cool.core.request.R;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.service.TaskExecutionService;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import lombok.RequiredArgsConstructor;
import java.util.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.cool.modules.task.entity.table.TaskExecutionEntityTableDef.TASK_EXECUTION_ENTITY;

/**
 * 任务执行管理控制器
 */
@Slf4j
@Tag(name = "任务执行管理", description = "任务执行记录的管理和操作")
@CoolRestController(api = {"add", "delete", "update", "info", "list", "page"})
@RequiredArgsConstructor
public class AdminTaskExecutionController extends BaseController<TaskExecutionService, TaskExecutionEntity> {
    @Override
    protected void init(HttpServletRequest request, JSONObject requestParams) {
        // 设置分页查询条件
        setPageOption(createOp()
            .fieldEq(TASK_EXECUTION_ENTITY.TASK_ID, TASK_EXECUTION_ENTITY.ASSIGNEE_ID,
                     TASK_EXECUTION_ENTITY.EXECUTION_STATUS, TASK_EXECUTION_ENTITY.ASSIGNMENT_TYPE)
            .keyWordLikeFields(TASK_EXECUTION_ENTITY.ASSIGNEE_NAME, TASK_EXECUTION_ENTITY.REMARK)
        );

        // 设置列表查询条件
        setListOption(createOp()
            .fieldEq(TASK_EXECUTION_ENTITY.TASK_ID, TASK_EXECUTION_ENTITY.ASSIGNEE_ID,
                     TASK_EXECUTION_ENTITY.EXECUTION_STATUS, TASK_EXECUTION_ENTITY.ASSIGNMENT_TYPE)
            .keyWordLikeFields(TASK_EXECUTION_ENTITY.ASSIGNEE_NAME, TASK_EXECUTION_ENTITY.REMARK)
        );
    }

    /**
     * 根据任务ID获取执行记录
     */
    @Operation(summary = "根据任务ID获取执行记录")
    @GetMapping("/byTask/{taskId}")
    public R<List<Map<String, Object>>> getByTaskId(@PathVariable Long taskId) {
        try {
            log.info("接收到获取任务执行记录请求，taskId: {}", taskId);
            List<TaskExecutionEntity> executions = service.getByTaskId(taskId);
            log.info("获取到执行记录数量: {}", executions.size());
            List<Map<String, Object>> result = new ArrayList<>();

            for (TaskExecutionEntity execution : executions) {
                Map<String, Object> executionInfo = new HashMap<>();
                executionInfo.put("id", execution.getId());
                executionInfo.put("taskId", execution.getTaskId());
                executionInfo.put("assigneeId", execution.getAssigneeId());
                executionInfo.put("assigneeName", execution.getAssigneeName());
                executionInfo.put("executionStatus", execution.getExecutionStatus());
                executionInfo.put("acceptTime", execution.getAcceptTime());
                executionInfo.put("completionTime", execution.getCompletionTime());
                executionInfo.put("assignmentType", execution.getAssignmentType());
                executionInfo.put("createTime", execution.getCreateTime());
                executionInfo.put("updateTime", execution.getUpdateTime());

                // 暂时设置默认值，后续可以通过关联查询获取用户详细信息
                executionInfo.put("phone", "");
                executionInfo.put("headImg", "");
                executionInfo.put("departmentName", "");
                executionInfo.put("roleName", "");

                result.add(executionInfo);
            }

            return R.ok(result);
        } catch (Exception e) {
            return R.error("获取任务执行记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据执行人ID获取执行记录
     */
    @Operation(summary = "根据执行人ID获取执行记录")
    @GetMapping("/byAssignee/{assigneeId}")
    public R<List<TaskExecutionEntity>> getByAssigneeId(@PathVariable Long assigneeId) {
        try {
            List<TaskExecutionEntity> executions = service.getByAssigneeId(assigneeId);
            return R.ok(executions);
        } catch (Exception e) {
            return R.error("获取用户执行记录失败: " + e.getMessage());
        }
    }

    /**
     * 检查任务是否已分配
     */
    @Operation(summary = "检查任务是否已分配")
    @GetMapping("/checkAssigned/{taskId}")
    public R<Boolean> isTaskAssigned(@PathVariable Long taskId) {
        try {
            Boolean isAssigned = service.isTaskAssigned(taskId);
            return R.ok(isAssigned);
        } catch (Exception e) {
            return R.error("检查任务分配状态失败: " + e.getMessage());
        }
    }

    /**
     * 完成任务执行
     */
    @Operation(summary = "完成任务执行")
    @PostMapping("/complete")
    public R<Boolean> completeExecution(
            @Parameter(description = "任务ID") @RequestParam Long taskId,
            @Parameter(description = "执行人ID") @RequestParam Long assigneeId) {
        try {
            Boolean result = service.completeExecution(taskId, assigneeId);
            if (result) {
                return R.ok(result);
            } else {
                return R.error("任务完成失败，未找到对应的执行记录");
            }
        } catch (Exception e) {
            log.error("完成任务执行失败，taskId: {}, assigneeId: {}", taskId, assigneeId, e);
            return R.error("完成任务执行失败: " + e.getMessage());
        }
    }


    /**
     * 取消任务分配
     */
    @Operation(summary = "取消任务分配")
    @PostMapping("/cancel")
    public R<Boolean> cancelAssignment(
            @Parameter(description = "任务ID") @RequestParam Long taskId,
            @Parameter(description = "执行人ID") @RequestParam Long assigneeId) {
        try {
            Boolean result = service.cancelAssignment(taskId, assigneeId);
            if (result) {
                return R.ok(result);
            } else {
                return R.error("取消分配失败，未找到对应的执行记录");
            }
        } catch (Exception e) {
            log.error("取消任务分配失败，taskId: {}, assigneeId: {}", taskId, assigneeId, e);
            return R.error("取消任务分配失败: " + e.getMessage());
        }
    }

    /**
     * 更新执行状态
     */
    @Operation(summary = "更新执行状态")
    @PostMapping("/updateStatus")
    public R<Boolean> updateExecutionStatus(@RequestBody Map<String, Object> params) {
        try {
            Long id = Long.valueOf(params.get("id").toString());
            String status = params.get("executionStatus").toString();
            String remark = params.getOrDefault("remark", "").toString();

            TaskExecutionEntity execution = service.getById(id);
            if (execution == null) {
                return R.error("执行记录不存在");
            }

            // 验证状态是否有效
            if (!TaskExecutionStatusEnum.isValidStatus(status)) {
                return R.error("无效的执行状态: " + status);
            }

            execution.setExecutionStatus(status);
            execution.setRemark(remark);
            execution.setUpdateTime(new Date());

            // 如果是完成状态，设置完成时间
            if (TaskExecutionStatusEnum.COMPLETED.getCode().equals(status)) {
                execution.setCompletionTime(new Date());
            }

            boolean result = service.updateById(execution);
            return result ? R.ok(result) : R.error("状态更新失败");
        } catch (Exception e) {
            log.error("更新执行状态失败", e);
            return R.error("更新执行状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新执行状态
     */
    @Operation(summary = "批量更新执行状态")
    @PostMapping("/batchUpdateStatus")
    public R<Boolean> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) params.get("ids");
            String status = params.get("executionStatus").toString();
            String remark = params.getOrDefault("remark", "").toString();

            // 验证状态是否有效
            if (!TaskExecutionStatusEnum.isValidStatus(status)) {
                return R.error("无效的执行状态: " + status);
            }

            for (Long id : ids) {
                TaskExecutionEntity execution = service.getById(id);
                if (execution != null) {
                    execution.setExecutionStatus(status);
                    execution.setRemark(remark);
                    execution.setUpdateTime(new Date());

                    if (TaskExecutionStatusEnum.COMPLETED.getCode().equals(status)) {
                        execution.setCompletionTime(new Date());
                    }

                    service.updateById(execution);
                }
            }

            return R.ok(true);
        } catch (Exception e) {
            log.error("批量更新执行状态失败", e);
            return R.error("批量更新执行状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取个人工作台任务列表
     */
    @Operation(summary = "获取个人工作台任务列表", description = "查询指定用户的所有任务及执行状态")
    @GetMapping("/personal-tasks/{assigneeId}")
    public R<List<Map<String, Object>>> getPersonalTasks(@PathVariable Long assigneeId) {
        try {
            List<Map<String, Object>> personalTasks = service.getPersonalTasks(assigneeId);
            return R.ok(personalTasks);
        } catch (Exception e) {
            return R.error("获取个人任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行统计信息
     */
    @Operation(summary = "获取执行统计信息")
    @GetMapping("/stats")
    public R<Map<String, Object>> getExecutionStats(
            @Parameter(description = "执行人ID") @RequestParam(required = false) Long assigneeId,
            @Parameter(description = "任务ID") @RequestParam(required = false) Long taskId) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create();

            if (assigneeId != null) {
                queryWrapper.eq("assignee_id", assigneeId);
            }
            if (taskId != null) {
                queryWrapper.eq("task_id", taskId);
            }

            long totalCount = service.count(queryWrapper);

            QueryWrapper completedWrapper = QueryWrapper.create()
                .eq("execution_status", TaskExecutionStatusEnum.COMPLETED.getCode());
            if (assigneeId != null) completedWrapper.eq("assignee_id", assigneeId);
            if (taskId != null) completedWrapper.eq("task_id", taskId);
            long completedCount = service.count(completedWrapper);

            QueryWrapper inProgressWrapper = QueryWrapper.create()
                .eq("execution_status", TaskExecutionStatusEnum.IN_PROGRESS.getCode());
            if (assigneeId != null) inProgressWrapper.eq("assignee_id", assigneeId);
            if (taskId != null) inProgressWrapper.eq("task_id", taskId);
            long inProgressCount = service.count(inProgressWrapper);

            Map<String, Object> stats = Map.of(
                "totalCount", totalCount,
                "completedCount", completedCount,
                "inProgressCount", inProgressCount,
                "completionRate", totalCount > 0 ? (double) completedCount / totalCount * 100 : 0
            );

            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取执行统计信息失败", e);
            return R.error("获取执行统计信息失败: " + e.getMessage());
        }
    }
}

# 熟悉md模板内容
_Exported on 2025/7/15 at GMT+8 16:54:16 from Cursor (1.2.4)_

---

**User**

我们先来熟悉所有的md模板内容

---

**Cursor**

好的，您的需求是先熟悉所有的md模板内容。根据您的目录结构，相关的md模板文件有：

- cool-admin-java/doc/sun/template/area-overview.md
- cool-admin-java/doc/sun/template/owner-income-expense.md
- cool-admin-java/doc/sun/template/fund-income-expense.md
- cool-admin-java/doc/sun/template/issue-report.md

接下来，我会依次为您整理和解读这四个模板的内容与结构，帮助您快速熟悉每个模板的用途和所需数据依赖。请稍等片刻，我将为您提取并解读每个md模板的详细内容。

四个md模板内容整理如下，便于您快速熟悉每个模板的结构和用途：

---

### 1. 物业服务区域概况公示表（area-overview.md）

- **一、基本信息**  
  包含区域名称、所在地（市/区/县/街道/社区）、区域范围（东/南/西/北至）、总建筑面积、总户数等。

- **二、物业服务信息**  
  包含物业服务人、合同期限、执行标准、收费方式（包干制/酬金制）、收费标准等。

- **三、业主共有部分**  
  包含物业服务用房、业主共有车位、会所/业主活动室、游泳池等的名称、面积/数量、坐落位置。

---

### 2. 业主共有部分经营收益收支情况公示表（owner-income-expense.md）

- **表头**  
  物业服务区域、物业服务人（盖章）

- **主要内容**  
  - 业主共有部分经营收入（如共有停车位、广告位、信号基站、场地租赁、快递柜、其他等）
  - 经营成本支出（人工、水电、税金、其他）
  - 经营收益（扣除成本后）
  - 经营收益依约或业主同意的支出（如维修、业主活动、补充维修资金、财务审计等）
  - 本期/累计经营收益结余

- **表尾**  
  制表、审核、日期

---

### 3. 物业服务资金收支情况公示表（fund-income-expense.md）

- **表头**  
  物业服务区域、物业服务人（盖章）

- **主要内容**  
  - 预收物业服务资金收入（住宅、商业、其他业态、车位等）
  - 物业服务支出（人员费用、共用部位/设施设备运行维护、清洁卫生、绿化养护、秩序维护、办公、管理费分摊、固定资产折旧、保险、税金、其他等）
  - 物业服务人酬金
  - 资金结余（当期、往期、累计）

- **表尾**  
  制表、审核、日期

---

### 4. 物业小区问题解决情况工作报告（issue-report.md）

- **表头**  
  年度季度说明

- **正文**  
  向业主报告本季度通过各渠道反映的小区问题解决情况

- **表格内容**  
  - 诉求渠道（客户问询、报修、投诉、智慧工单、其他）
  - 反映问题总量、响应时间
  - 物业主责问题（总量、已解决、待解决、满意度）
  - 其他（非物业）问题（总量、已上报、协助解决）
  - 备注

- **表尾**  
  制表、审核、日期

---

#### 总结
每个模板都以“表格”为主，内容结构清晰，分别对应物业区域基本情况、业主共有收益、物业资金收支、业主问题解决等不同主题。  
数据依赖主要来自 master_data.txt、business_data.txt、financial_data.txt、smart_work_order.txt 四个数据源文件。

如需进一步分析每个模板与数据源的字段映射或自动填充逻辑，请告知！

---

**User**

我们来依次按照template_key来分析该模板所依赖的数据源

---

**Cursor**

好的，下面我将依次按照每个 template_key（area-overview、owner-income-expense、fund-income-expense、issue-report）详细分析每个模板所依赖的数据源和字段需求，帮助您理解数据填充的来源和结构。

---

### 1. area-overview（物业服务区域概况）

**模板内容结构：**
- 基本信息（区域名称、所在地、区域范围、总建筑面积、总户数）
- 物业服务信息（服务人、合同期限、执行标准、收费方式、收费标准）
- 业主共有部分（服务用房、共有车位、会所/活动室、游泳池等）

**依赖数据源：**
- **master_data.txt**  
  该模板主要依赖 master_data.txt，需包含：
  - 区域名称、所在地、区域范围（东/南/西/北至）、总建筑面积、总户数
  - 物业服务人、合同期限、执行标准、收费方式、收费标准
  - 业主共有部分的各项明细（如服务用房、车位、会所等的名称、面积/数量、位置）

---

### 2. owner-income-expense（业主共有部分经营收益收支情况）

**模板内容结构：**
- 业主共有部分经营收入（停车位、广告位、信号基站、场地租赁、快递柜、其他、利息等）
- 经营成本支出（人工、水电、税金、其他）
- 经营收益（扣除成本后）
- 经营收益依约或业主同意的支出（维修、活动、补充维修资金、分配、审计、其他）
- 本期/累计经营收益结余

**依赖数据源：**
- **business_data.txt**  
  该模板主要依赖 business_data.txt，需包含：
  - 各类经营收入明细及金额
  - 各类经营成本支出明细及金额
  - 经营收益、结余等汇总数据
  - 相关备注、制表人、审核人、日期等

---

### 3. fund-income-expense（物业服务资金收支情况）

**模板内容结构：**
- 物业服务资金收入（住宅、商业、其他业态、车位等）
- 物业服务支出（人员费用、共用部位/设施设备运行维护、清洁卫生、绿化养护、秩序维护、办公、管理费分摊、固定资产折旧、保险、税金、其他等）
- 物业服务人酬金
- 资金结余（当期、往期、累计）

**依赖数据源：**
- **financial_data.txt**  
  该模板主要依赖 financial_data.txt，需包含：
  - 各类收入明细及金额（预算、当季、累计）
  - 各类支出明细及金额（预算、当季、累计）
  - 酬金、结余等汇总数据
  - 相关备注、制表人、审核人、日期等

---

### 4. issue-report（物业小区问题解决情况工作报告）

**模板内容结构：**
- 诉求渠道（客户问询、报修、投诉、智慧工单、其他）
- 反映问题总量、响应时间
- 物业主责问题（总量、已解决、待解决、满意度）
- 其他（非物业）问题（总量、已上报、协助解决）
- 备注

**依赖数据源：**
- **smart_work_order.txt**  
  该模板主要依赖 smart_work_order.txt，需包含：
  - 各诉求渠道的问题数量、响应时间
  - 物业主责问题的总量、已解决、待解决、满意度
  - 其他问题的总量、已上报、协助解决
  - 相关备注、制表人、审核人、日期等

---

#### 总结表

| template_key           | 主要依赖数据源           | 说明                                                         |
|-----------------------|------------------------|--------------------------------------------------------------|
| area-overview         | master_data.txt        | 区域、物业服务、共有部分等基础信息                           |
| owner-income-expense  | business_data.txt      | 业主共有部分经营收支明细                                     |
| fund-income-expense   | financial_data.txt     | 物业服务资金收支明细                                         |
| issue-report          | smart_work_order.txt   | 小区问题解决情况、诉求渠道、主责/非主责问题统计等            |

如需进一步分析每个字段与数据源的具体映射或举例说明，请随时告知！


import { useCool } from '/@/cool';

const { service } = useCool();

/**
 * 默认分配策略配置
 */
export const defaultStrategyConfig = {
  workloadBalance: true,
  skillMatching: true,
  availabilityCheck: true,
  priorityBased: true,
  geographicProximity: false
};

/**
 * 任务分配服务
 */
export class AssignmentService {
  
  /**
   * 执行分配
   */
  static async executeAssignment(request: any) {
    try {
      return await service.task.assignment.execute(request);
    } catch (error) {
      console.error('执行分配失败:', error);
      throw error;
    }
  }

  /**
   * 分配单个任务
   */
  static async assignSingleTask(taskId: number, autoAssign: boolean = true) {
    try {
      return await service.task.assignment.single(taskId, { autoAssign });
    } catch (error) {
      console.error('分配单个任务失败:', error);
      throw error;
    }
  }

  /**
   * 分配任务包
   */
  static async assignTaskPackage(packageId: number, autoAssign: boolean = true) {
    try {
      return await service.task.assignment.package(packageId, { autoAssign });
    } catch (error) {
      console.error('分配任务包失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务候选人
   */
  static async getCandidatesForTask(taskId: number) {
    try {
      return await service.task.assignment.candidates.task(taskId);
    } catch (error) {
      console.error('获取任务候选人失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有候选人
   */
  static async getAllCandidates() {
    try {
      return await service.task.assignment.candidates.all();
    } catch (error) {
      console.error('获取所有候选人失败:', error);
      throw error;
    }
  }

  /**
   * 根据角色获取候选人
   */
  static async getCandidatesByRoles(requiredRoles: string[]) {
    try {
      return await service.task.assignment.candidates.byRoles({ requiredRoles });
    } catch (error) {
      console.error('根据角色获取候选人失败:', error);
      throw error;
    }
  }

  /**
   * 验证分配
   */
  static async validateAssignment(taskId: number, assigneeIds: number[]) {
    try {
      return await service.task.assignment.validate(taskId, { assigneeIds });
    } catch (error) {
      console.error('验证分配失败:', error);
      throw error;
    }
  }

  /**
   * 手动分配
   */
  static async manualAssign(request: any) {
    try {
      return await service.task.assignment.manual(request);
    } catch (error) {
      console.error('手动分配失败:', error);
      throw error;
    }
  }
}

export default AssignmentService; 
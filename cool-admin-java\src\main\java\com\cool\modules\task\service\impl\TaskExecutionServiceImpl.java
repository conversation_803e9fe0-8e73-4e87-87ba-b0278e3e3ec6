package com.cool.modules.task.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import com.cool.core.base.BaseServiceImpl;
import com.cool.modules.task.entity.TaskExecutionEntity;
import com.cool.modules.task.entity.TaskInfoEntity;
import com.cool.modules.task.entity.TaskPackageEntity;
import com.cool.modules.task.enums.TaskBusinessStatusEnum;
import com.cool.modules.task.enums.TaskExecutionStatusEnum;
import com.cool.modules.task.mapper.TaskAssignmentMapper;
import com.cool.modules.task.service.TaskExecutionService;
import com.cool.modules.task.service.TaskInfoService;
import com.cool.modules.task.service.TaskPackageService;
import com.mybatisflex.core.query.QueryWrapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务执行服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskExecutionServiceImpl extends BaseServiceImpl<TaskAssignmentMapper, TaskExecutionEntity> implements TaskExecutionService {

    @Lazy
    private final TaskInfoService taskInfoService;
    private final TaskPackageService taskPackageService;

    @Override
    public List<TaskExecutionEntity> getByTaskId(Long taskId) {
        log.info("查询任务执行记录，taskId: {}", taskId);
        List<TaskExecutionEntity> executions = list(QueryWrapper.create()
                .eq("task_id", taskId)
                .orderBy("create_time", false));
        log.info("查询到的执行记录数量: {}, 记录: {}", executions.size(), executions);
        return executions;
    }

    @Override
    public List<TaskExecutionEntity> getByAssigneeId(Long assigneeId) {
        return list(QueryWrapper.create()
                .eq("assignee_id", assigneeId)
                .orderBy("create_time", false));
    }

    @Override
    public Boolean isTaskAssigned(Long taskId) {
        long count = count(QueryWrapper.create()
                .eq("task_id", taskId)
                .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()));
        return count > 0;
    }

    @Override
    @Transactional
    public Boolean cancelAssignment(Long taskId, Long assigneeId) {
        try {
            TaskExecutionEntity execution = getOne(QueryWrapper.create()
                    .eq("task_id", taskId)
                    .eq("assignee_id", assigneeId)
                    .eq("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode()));

            if (execution != null) {
                execution.setExecutionStatusEnum(TaskExecutionStatusEnum.CANCELLED);
                execution.setUpdateTime(new Date());
                return updateById(execution);
            }
            return false;
        } catch (Exception e) {
            log.error("取消任务分配失败，taskId: {}, assigneeId: {}", taskId, assigneeId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean completeExecution(Long taskId, Long assigneeId) {
        try {
            TaskExecutionEntity execution = getOne(QueryWrapper.create()
                    .eq("task_id", taskId)
                    .eq("assignee_id", assigneeId)
                    .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()));

            if (execution != null) {
                execution.setExecutionStatusEnum(TaskExecutionStatusEnum.COMPLETED);
                execution.setCompletionTime(new Date());
                execution.setUpdateTime(new Date());
                return updateById(execution);
            }
            return false;
        } catch (Exception e) {
            log.error("完成任务执行失败，taskId: {}, assigneeId: {}", taskId, assigneeId, e);
            return false;
        }
    }



    @Override
    public Integer getUserWorkload(Long userId) {
        try {
            // 计算用户当前的工作负载
            // 这里简化计算：统计用户当前分配但未完成的任务数量
            long assignedTasks = count(QueryWrapper.create()
                    .eq("assignee_id", userId)
                    .in("execution_status", TaskExecutionStatusEnum.ASSIGNED.getCode(), TaskExecutionStatusEnum.IN_PROGRESS.getCode()));

            // 假设每个用户最多能处理10个任务，计算百分比
            int maxTasks = 10;
            return (int) Math.min(100, (assignedTasks * 100) / maxTasks);

        } catch (Exception e) {
            log.error("获取用户工作负载失败，userId: {}", userId, e);
            return 0;
        }
    }

    @Override
    public boolean startTask(Long taskId, Long assigneeId) {
        try {
            // 查找指定任务和执行人的执行记录
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("task_id", taskId)
                .eq("assignee_id", assigneeId);

            TaskExecutionEntity execution = getOne(queryWrapper);
            if (execution == null) {
                System.err.println("未找到任务执行记录，taskId: " + taskId + ", assigneeId: " + assigneeId);
                return false;
            }

            // 更新执行状态为IN_PROGRESS
            execution.setExecutionStatusEnum(TaskExecutionStatusEnum.IN_PROGRESS);
            execution.setAcceptTime(new Date());

            boolean result = updateById(execution);

            // 如果任务开始成功，更新任务包状态
            if (result) {
                updateTaskPackageStatusAfterTaskStart(taskId);
            }

            return result;
        } catch (Exception e) {
            System.err.println("开始任务失败，taskId: " + taskId + ", assigneeId: " + assigneeId + ", error: " + e.getMessage());
            return false;
        }
    }

    /**
     * 任务开始后更新任务包状态
     */
    private void updateTaskPackageStatusAfterTaskStart(Long taskId) {
        try {
            // 获取任务信息
            TaskInfoEntity task = taskInfoService.getById(taskId);
            if (task != null && task.getPackageId() != null) {
                // 更新任务包状态为执行中
                TaskPackageEntity taskPackage = taskPackageService.getById(task.getPackageId());
                if (taskPackage != null && taskPackage.getPackageStatus() != TaskBusinessStatusEnum.EXECUTING.getCode()) { // 如果不是已完成状态
                    taskPackage.setPackageStatus(TaskBusinessStatusEnum.EXECUTING.getCode()); // 2 = 执行中
                    taskPackageService.updateById(taskPackage);
                    System.out.println("任务包 " + task.getPackageId() + " 状态已更新为执行中");
                }
            }
        } catch (Exception e) {
            System.err.println("更新任务包状态失败: " + e.getMessage());
        }
    }

    @Override
    public boolean completeTask(Long taskId, Long assigneeId, String remark) {
        try {
            // 查找指定任务和执行人的执行记录
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("task_id", taskId)
                .eq("assignee_id", assigneeId);

            TaskExecutionEntity execution = getOne(queryWrapper);
            if (execution == null) {
                System.err.println("未找到任务执行记录，taskId: " + taskId + ", assigneeId: " + assigneeId);
                return false;
            }

            // 更新执行状态为COMPLETED
            execution.setExecutionStatusEnum(TaskExecutionStatusEnum.COMPLETED);
            execution.setCompletionTime(new Date());
            if (remark != null && !remark.isEmpty()) {
                execution.setRemark(remark);
            }

            return updateById(execution);
        } catch (Exception e) {
            System.err.println("完成任务失败，taskId: " + taskId + ", assigneeId: " + assigneeId + ", error: " + e.getMessage());
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getPersonalTasks(Long assigneeId) {
        // 获取执行人的所有任务执行记录
        List<TaskExecutionEntity> executions = getByAssigneeId(assigneeId);

        List<Map<String, Object>> personalTasks = new java.util.ArrayList<>();
        for (TaskExecutionEntity execution : executions) {
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("executionId", execution.getId());
            taskInfo.put("taskId", execution.getTaskId());

            // 通过taskId获取任务名称
            try {
                TaskInfoEntity task = taskInfoService.getById(execution.getTaskId());
                taskInfo.put("taskName", task != null ? task.getName() : "未知任务");
            } catch (Exception e) {
                taskInfo.put("taskName", "未知任务");
            }

            taskInfo.put("executionStatus", execution.getExecutionStatus());
            taskInfo.put("assignmentType", execution.getAssignmentType());
            taskInfo.put("createTime", execution.getCreateTime());
            taskInfo.put("completionTime", execution.getCompletionTime());
            taskInfo.put("remark", execution.getRemark());
            personalTasks.add(taskInfo);
        }

        return personalTasks;
    }

}

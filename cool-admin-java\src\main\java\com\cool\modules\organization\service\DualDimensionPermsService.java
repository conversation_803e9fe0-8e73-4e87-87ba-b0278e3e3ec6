package com.cool.modules.organization.service;

import cn.hutool.core.lang.Dict;
import com.cool.modules.base.entity.sys.BaseSysMenuEntity;

import java.util.List;

/**
 * 双维度权限服务接口
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
public interface DualDimensionPermsService {
    
    /**
     * 获取用户在当前组织形态下的权限菜单
     * 
     * @param adminUserId 用户ID
     * @return 权限菜单数据
     */
    Dict permmenu(Long adminUserId);
    
    /**
     * 获取用户在项目形态下的菜单
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<BaseSysMenuEntity> getProjectMenus(Long userId);
    
    /**
     * 获取用户在项目形态下的权限
     * 
     * @param userId 用户ID
     * @return 权限数组
     */
    String[] getProjectPerms(Long userId);
    
    /**
     * 获取用户在项目形态下的角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> getProjectRoleIds(Long userId);
    
    /**
     * 根据项目角色代码获取系统角色ID
     * 
     * @param projectRoleCode 项目角色代码
     * @return 系统角色ID
     */
    Long getSystemRoleIdByProjectRole(String projectRoleCode);
    
    /**
     * 初始化项目菜单结构
     */
    void initProjectMenus();
    
    /**
     * 创建全局项目角色
     */
    void createGlobalProjectRoles();
    
    /**
     * 检查用户是否有项目权限
     * 
     * @param userId 用户ID
     * @return 是否有项目权限
     */
    boolean hasProjectPermission(Long userId);
}

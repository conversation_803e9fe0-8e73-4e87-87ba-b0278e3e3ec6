package com.cool.modules.organization.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.cool.modules.base.entity.sys.BaseSysMenuEntity;
import com.cool.modules.base.entity.sys.BaseSysRoleEntity;
import com.cool.modules.base.service.sys.BaseSysMenuService;
import com.cool.modules.base.service.sys.BaseSysPermsService;
import com.cool.modules.base.service.sys.BaseSysRoleService;
import com.cool.modules.organization.entity.UserOrganizationEntity;
import com.cool.modules.organization.enums.GlobalProjectRoleEnum;
import com.cool.modules.organization.enums.OrganizationModeEnum;
import com.cool.modules.organization.service.DualDimensionPermsService;
import com.cool.modules.organization.service.OrganizationModeService;
import com.cool.modules.organization.service.UserOrganizationService;
import com.mybatisflex.core.query.QueryWrapper;

import cn.hutool.core.lang.Dict;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 双维度权限服务实现
 * 
 * <AUTHOR> Admin
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DualDimensionPermsServiceImpl implements DualDimensionPermsService {
    
    private final @Lazy BaseSysPermsService baseSysPermsService;
    private final BaseSysMenuService baseSysMenuService;
    private final BaseSysRoleService baseSysRoleService;
    private final OrganizationModeService organizationModeService;
    private final UserOrganizationService userOrganizationService;
    
    @PostConstruct
    public void init() {
        try {
            // 初始化项目菜单和角色
            createGlobalProjectRoles();
            initProjectMenus();
            log.info("双维度权限服务初始化完成");
        } catch (Exception e) {
            log.error("双维度权限服务初始化失败", e);
        }
    }
    
    @Override
    public Dict permmenu(Long adminUserId) {
        // 获取用户当前组织形态
        String currentMode = organizationModeService.getCurrentMode(adminUserId);
        
        // 根据组织形态获取对应的菜单和权限
        List<BaseSysMenuEntity> menus;
        String[] perms;
        
        if (OrganizationModeEnum.DEPARTMENT.getCode().equals(currentMode)) {
            // 部门形态：使用现有逻辑
            menus = baseSysPermsService.getMenus(adminUserId);
            perms = baseSysPermsService.getPerms(adminUserId);
        } else if (OrganizationModeEnum.PROJECT.getCode().equals(currentMode)) {
            // 项目形态：获取项目相关菜单和权限
            menus = getProjectMenus(adminUserId);
            perms = getProjectPerms(adminUserId);
        } else {
            // 默认使用部门形态
            menus = baseSysPermsService.getMenus(adminUserId);
            perms = baseSysPermsService.getPerms(adminUserId);
        }
        
        log.debug("用户 {} 在 {} 模式下获取到 {} 个菜单，{} 个权限", 
                 adminUserId, currentMode, menus.size(), perms.length);
        
        return Dict.create().set("menus", menus).set("perms", perms);
    }
    
    @Override
    public List<BaseSysMenuEntity> getProjectMenus(Long userId) {
        // 获取用户在项目维度的角色ID列表
        List<Long> projectRoleIds = getProjectRoleIds(userId);
        
        if (projectRoleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 使用现有的getMenus方法，但传入项目角色ID
        return baseSysPermsService.getMenus(projectRoleIds.toArray(new Long[0]));
    }
    
    @Override
    public String[] getProjectPerms(Long userId) {
        // 获取用户在项目维度的角色ID列表
        List<Long> projectRoleIds = getProjectRoleIds(userId);
        
        if (projectRoleIds.isEmpty()) {
            return new String[0];
        }
        
        // 使用现有的getPerms方法，但传入项目角色ID
        return baseSysPermsService.getPerms(projectRoleIds.toArray(new Long[0]));
    }
    
    @Override
    public List<Long> getProjectRoleIds(Long userId) {
        // 获取用户在项目维度的所有角色
        List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
            userId, OrganizationModeEnum.PROJECT.getCode());
        
        // 根据项目角色代码获取对应的系统角色ID
        return projectRoles.stream()
            .filter(role -> role.getStatus() == 1)
            .filter(role -> role.getExpireTime() == null || role.getExpireTime().after(new Date()))
            .map(role -> getSystemRoleIdByProjectRole(role.getRoleCode()))
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    }
    
    @Override
    public Long getSystemRoleIdByProjectRole(String projectRoleCode) {
        // 根据项目角色代码获取对应的系统角色ID
        BaseSysRoleEntity role = baseSysRoleService.getOne(
            QueryWrapper.create().eq(BaseSysRoleEntity::getLabel, projectRoleCode));
        return role != null ? role.getId() : null;
    }
    
    @Override
    public void initProjectMenus() {
        try {
            // 检查项目工作台根菜单是否已存在
            BaseSysMenuEntity existingRoot = baseSysMenuService.getOne(
                QueryWrapper.create().eq(BaseSysMenuEntity::getRouter, "/project"));
            
            if (existingRoot != null) {
                log.debug("项目菜单已存在，跳过初始化");
                return;
            }
            
            // 创建项目工作台根菜单
            BaseSysMenuEntity projectRoot = new BaseSysMenuEntity();
            projectRoot.setName("项目工作台");
            projectRoot.setRouter("/project");
            projectRoot.setType(0); // 目录
            projectRoot.setIcon("icon-project");
            projectRoot.setOrderNum(1);
            projectRoot.setIsShow(true);
            projectRoot.setKeepAlive(true);
            
            baseSysMenuService.save(projectRoot);
            Long parentId = projectRoot.getId();
            
            // 创建子菜单
            List<BaseSysMenuEntity> subMenus = Arrays.asList(
                createMenu("项目概览", "/project/dashboard", "modules/project/views/dashboard/index.vue", 1, parentId),
                createMenu("我的项目", "/project/list", "modules/project/views/list/index.vue", 2, parentId),
                createMenu("项目管理", "/project/management", "modules/project/views/management/index.vue", 3, parentId),
                createMenu("成员管理", "/project/member", "modules/project/views/member/index.vue", 4, parentId),
                createMenu("任务管理", "/project/task", "modules/project/views/task/index.vue", 5, parentId),
                createMenu("项目报表", "/project/report", "modules/project/views/report/index.vue", 6, parentId)
            );
            
            // 保存子菜单
            for (BaseSysMenuEntity menu : subMenus) {
                baseSysMenuService.save(menu);
                
                // 为每个菜单创建操作权限按钮
                createMenuPermissions(menu);
            }
            
            log.info("项目菜单结构初始化完成");
            
        } catch (Exception e) {
            log.error("初始化项目菜单失败", e);
        }
    }
    
    private BaseSysMenuEntity createMenu(String name, String router, String viewPath, int orderNum, Long parentId) {
        BaseSysMenuEntity menu = new BaseSysMenuEntity();
        menu.setName(name);
        menu.setRouter(router);
        menu.setViewPath(viewPath);
        menu.setType(1); // 菜单
        menu.setParentId(parentId);
        menu.setOrderNum(orderNum);
        menu.setIsShow(true);
        menu.setKeepAlive(true);
        return menu;
    }
    
    private void createMenuPermissions(BaseSysMenuEntity menu) {
        // 为每个菜单创建标准的CRUD权限按钮
        String[] permissions = {"add", "edit", "delete", "view", "export"};
        String[] permissionNames = {"新增", "编辑", "删除", "查看", "导出"};
        String basePerms = "project:" + menu.getRouter().replace("/project/", "") + ":";
        
        for (int i = 0; i < permissions.length; i++) {
            BaseSysMenuEntity permButton = new BaseSysMenuEntity();
            permButton.setParentId(menu.getId());
            permButton.setName(permissionNames[i]);
            permButton.setPerms(basePerms + permissions[i]);
            permButton.setType(2); // 按钮
            permButton.setOrderNum(i + 1);
            permButton.setIsShow(true);
            baseSysMenuService.save(permButton);
        }
    }
    
    @Override
    public void createGlobalProjectRoles() {
        try {
            // 创建全局项目角色
            for (GlobalProjectRoleEnum roleEnum : GlobalProjectRoleEnum.values()) {
                createProjectRole(roleEnum.getCode(), roleEnum.getName(), roleEnum.getDescription());
            }
            
            log.info("全局项目角色创建完成");
            
        } catch (Exception e) {
            log.error("创建全局项目角色失败", e);
        }
    }
    
    private void createProjectRole(String label, String name, String remark) {
        // 检查角色是否已存在
        BaseSysRoleEntity existingRole = baseSysRoleService.getOne(
            QueryWrapper.create().eq(BaseSysRoleEntity::getLabel, label));
        
        if (existingRole == null) {
            BaseSysRoleEntity role = new BaseSysRoleEntity();
            role.setLabel(label);
            role.setName(name);
            role.setRemark(remark);
            role.setUserId(1L); // 系统创建
            
            baseSysRoleService.save(role);
            log.debug("创建项目角色: {} - {}", label, name);
        } else {
            log.debug("项目角色已存在: {} - {}", label, name);
        }
    }
    
    @Override
    public boolean hasProjectPermission(Long userId) {
        if (userId == null) {
            return false;
        }
        
        List<UserOrganizationEntity> projectRoles = userOrganizationService.getByUserIdAndType(
            userId, OrganizationModeEnum.PROJECT.getCode());
        
        return !projectRoles.isEmpty();
    }
}

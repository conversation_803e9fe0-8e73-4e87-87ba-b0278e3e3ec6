package com.cool.modules.sop.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

// Auto generate by mybatis-flex, do not modify it.
public class SOPIndustryEntityTableDef extends TableDef {

    /**
     * SOP行业管理实体
     */
    public static final SOPIndustryEntityTableDef SOPINDUSTRY_ENTITY = new SOPIndustryEntityTableDef();

    public final QueryColumn ID = new QueryColumn(this, "id");

    public final QueryColumn ICON = new QueryColumn(this, "icon");

    public final QueryColumn SORT = new QueryColumn(this, "sort");

    public final QueryColumn CONFIG = new QueryColumn(this, "config");

    public final QueryColumn STATUS = new QueryColumn(this, "status");

    public final QueryColumn PARENT_ID = new QueryColumn(this, "parent_id");

    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    public final QueryColumn INDUSTRY_CODE = new QueryColumn(this, "industry_code");

    public final QueryColumn INDUSTRY_NAME = new QueryColumn(this, "industry_name");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, ICON, SORT, CONFIG, STATUS, PARENT_ID, CREATE_TIME, UPDATE_TIME, DESCRIPTION, INDUSTRY_CODE, INDUSTRY_NAME};

    public SOPIndustryEntityTableDef() {
        super("", "sop_industry");
    }

    private SOPIndustryEntityTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SOPIndustryEntityTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SOPIndustryEntityTableDef("", "sop_industry", alias));
    }

}

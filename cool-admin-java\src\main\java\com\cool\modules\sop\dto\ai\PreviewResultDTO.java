package com.cool.modules.sop.dto.ai;

import lombok.Data;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class PreviewResultDTO implements Serializable {
    private String mode; // preview/generate
    private Boolean multiDepartment;
    private List<DepartmentPreview> departments; // 统一多部门结构
    private Map<String, Object> summary; // 统计信息

    @Data
    public static class DepartmentPreview implements Serializable {
        private Long departmentId;
        private String departmentName;
        private ScenarioInfo scenario;
        private List<TaskGenerateResponse.GeneratedTask> tasks;
    }

    @Data
    public static class ScenarioInfo implements Serializable {
        private Long id;
        private String name;
        private String description;
        private String code;
    }
} 